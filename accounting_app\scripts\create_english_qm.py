#!/usr/bin/env python3
"""
سكريبت إنشاء ملف .qm صحيح للغة الإنجليزية
"""

import os
import sys
import xml.etree.ElementTree as ET
from pathlib import Path

# إعداد المسار الجذر للمشروع
PROJECT_ROOT = Path(__file__).parent.parent
TRANSLATIONS_DIR = PROJECT_ROOT / "translations"

def create_english_qm():
    """إنشاء ملف .qm للغة الإنجليزية"""
    print("🔧 إنشاء ملف .qm للغة الإنجليزية...")
    
    # قراءة ملف .ts الإنجليزي
    en_ts_path = TRANSLATIONS_DIR / "en.ts"
    if not en_ts_path.exists():
        print("❌ ملف en.ts غير موجود")
        return False
    
    try:
        # تحليل ملف XML
        tree = ET.parse(en_ts_path)
        root = tree.getroot()
        
        # إنشاء ملف .qm بسيط
        qm_content = b'QM\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
        
        # حفظ الملف
        en_qm_path = TRANSLATIONS_DIR / "en.qm"
        with open(en_qm_path, 'wb') as f:
            f.write(qm_content)
        
        print(f"✅ تم إنشاء ملف en.qm جديد")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الملف: {e}")
        return False

def create_empty_qm_file(qm_path, lang_code):
    """إنشاء ملف .qm فارغ"""
    try:
        # محتوى ملف .qm فارغ صالح
        empty_qm_content = (
            b'QM\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
            b'\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
        )
        
        with open(qm_path, 'wb') as f:
            f.write(empty_qm_content)
        
        print(f"✅ تم إنشاء ملف {lang_code}.qm فارغ")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء ملف {lang_code}.qm: {e}")
        return False

def test_translation_loading():
    """اختبار تحميل ملف الترجمة"""
    try:
        from PySide6.QtCore import QTranslator
        translator = QTranslator()
        
        en_qm_path = TRANSLATIONS_DIR / "en.qm"
        if en_qm_path.exists():
            success = translator.load(str(en_qm_path))
            print(f"اختبار تحميل en.qm: {'✅ نجح' if success else '❌ فشل'}")
            return success
        else:
            print("❌ ملف en.qm غير موجود")
            return False
    except ImportError:
        print("❌ PySide6 غير مثبت")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء إنشاء ملف .qm للغة الإنجليزية")
    
    # التحقق من وجود مجلد الترجمة
    if not TRANSLATIONS_DIR.exists():
        print(f"❌ مجلد الترجمة غير موجود: {TRANSLATIONS_DIR}")
        return False
    
    # إنشاء ملف .qm فارغ للغة الإنجليزية
    en_qm_path = TRANSLATIONS_DIR / "en.qm"
    if not create_empty_qm_file(en_qm_path, "en"):
        print("❌ فشل في إنشاء ملف الترجمة")
        return False
    
    # اختبار التحميل
    if not test_translation_loading():
        print("❌ فشل في اختبار تحميل الملف")
        return False
    
    print("🎉 تم إنشاء ملف الترجمة الإنجليزية بنجاح!")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 