import sys
import os

# إضافة مجلد accounting_app إلى مسار Python عند التشغيل المباشر
if __name__ == "__main__":
    # إصلاح ترميز Console في Windows لدعم Unicode
    if sys.platform == "win32":
        try:
            os.system("chcp 65001 >nul 2>&1")
        except:
            pass
    
    # الحصول على مسار المجلد الحالي
    current_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.dirname(current_dir)  # مجلد l0.17
    sys.path.insert(0, parent_dir)

from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import QCoreApplication

# استيراد المكونات من الحزمة
from accounting_app import (
    AppInitializer, 
    MainWindow, 
    AppEvents, 
    apply_safe_listview_fix
)
from accounting_app.utils import ensure_single_instance

def main():
    """الدالة الرئيسية للتطبيق"""
    # إصلاح ترميز Console في Windows لدعم Unicode
    if sys.platform == "win32":
        try:
            os.system("chcp 65001 >nul 2>&1")
            # إعادة تكوين stdout و stderr لاستخدام UTF-8
            import io
            sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
            sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')
        except:
            pass
    
    app = QApplication(sys.argv)
    
    # التحقق من عدم تشغيل نسخة أخرى من التطبيق
    try:
        single_instance = ensure_single_instance("accounting_app", 12345)
        print("✓ تم الحصول على قفل التطبيق بنجاح")
    except RuntimeError as e:
        # عرض رسالة تحذير للمستخدم
        msg_box = QMessageBox()
        msg_box.setIcon(QMessageBox.Warning)
        msg_box.setWindowTitle("تحذير")
        msg_box.setText("لا يمكن تشغيل نسخة أخرى من التطبيق")
        msg_box.setInformativeText(str(e))
        msg_box.setStandardButtons(QMessageBox.Ok)
        msg_box.exec()
        
        print(f"❌ {e}")
        sys.exit(1)

    # تطبيق الحل الجذري لمشكلة QListView
    apply_safe_listview_fix()

    # تهيئة التطبيق
    initializer = AppInitializer(app)
    initializer.initialize()

    # إنشاء النافذة الرئيسية
    window = MainWindow(
        initializer.language_controller, 
        initializer.event_bus, 
        initializer.settings
    )
    # عرض النافذة الرئيسية في المقدمة
    window.show()
    window.raise_()
    window.activateWindow()

    # إشعار ترحيبي
    initializer.event_bus.emit(AppEvents.NOTIFY, {
        "type": "info",
        "title": QCoreApplication.translate("MainWindow", "أهلاً بك!"),
        "message": QCoreApplication.translate("MainWindow", "مرحبًا بك في برنامج المحاسبة الذكي 🎉")
    })

    # تشغيل التطبيق
    try:
        print("🔄 بدء حلقة الأحداث...")
        print("💡 النافذة يجب أن تظهر الآن. إذا لم تظهر، تحقق من شريط المهام.")
        sys.exit(app.exec())
    finally:
        # إطلاق قفل التطبيق عند الإغلاق
        if 'single_instance' in locals():
            single_instance.release_lock()

if __name__ == "__main__":
    main()