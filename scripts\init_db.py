from sqlalchemy.orm import Session
from database.config.connection_pool import <PERSON><PERSON><PERSON>al
from database.services.auth_service import AuthService

def create_admin():
    db: Session = SessionLocal()
    try:
        service = AuthService(db)
        admin_username = "admin"
        admin_password = "admin123"
        admin_role = "admin"
        if service.user_repo.get_by_username(admin_username):
            print("Admin user already exists.")
        else:
            user = service.register_user(admin_username, admin_password, admin_role)
            print(f"Admin user created: {user.username} (password: {admin_password})")
    finally:
        db.close()

if __name__ == "__main__":
    create_admin() 