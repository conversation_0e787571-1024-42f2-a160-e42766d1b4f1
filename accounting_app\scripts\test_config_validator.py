#!/usr/bin/env python3
"""
سكريبت اختبار فاحص الإعدادات
Test Config Validator Script
"""

import sys
import os
import json
import tempfile
from pathlib import Path

# إضافة مجلد المشروع إلى مسار Python
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from accounting_app.utils import ConfigValidator, ValidationResult, validate_config_file, create_valid_config_file

def test_config_validator():
    """اختبار فاحص الإعدادات"""
    print("🧪 بدء اختبار فاحص الإعدادات...")
    
    # اختبار 1: إنشاء فاحص الإعدادات
    print("\n📋 اختبار 1: إنشاء فاحص الإعدادات")
    validator = ConfigValidator()
    print(f"✅ تم إنشاء فاحص الإعدادات بنجاح")
    
    # اختبار 2: إنشاء إعدادات افتراضية
    print("\n📋 اختبار 2: إنشاء إعدادات افتراضية")
    default_config = validator.create_default_config()
    print(f"✅ تم إنشاء إعدادات افتراضية مع {len(default_config)} قسم")
    
    # اختبار 3: التحقق من صحة الإعدادات الافتراضية
    print("\n📋 اختبار 3: التحقق من صحة الإعدادات الافتراضية")
    result = validator.validate_config(default_config)
    print(f"النتيجة: {'✅ صحيح' if result.is_valid else '❌ خطأ'}")
    
    if result.errors:
        print("الأخطاء:")
        for error in result.errors:
            print(f"  ❌ {error}")
    
    if result.warnings:
        print("التحذيرات:")
        for warning in result.warnings:
            print(f"  ⚠️  {warning}")
    
    # اختبار 4: اختبار إعدادات غير صحيحة
    print("\n📋 اختبار 4: اختبار إعدادات غير صحيحة")
    invalid_config = {
        "app_info": {
            "app_name": "",  # اسم فارغ
            "app_version": "invalid",  # إصدار غير صحيح
            "app_description": "وصف التطبيق",
            "environment": "invalid_env"  # بيئة غير صحيحة
        },
        "user_preferences": {
            "language": "invalid_lang",  # لغة غير صحيحة
            "theme": "invalid_theme"  # ثيم غير صحيح
        }
    }
    
    result = validator.validate_config(invalid_config)
    print(f"النتيجة: {'✅ صحيح' if result.is_valid else '❌ خطأ'} (متوقع: خطأ)")
    
    if result.errors:
        print("الأخطاء المكتشفة:")
        for error in result.errors:
            print(f"  ❌ {error}")
    
    if result.fixed_values:
        print("القيم المصلحة:")
        for field, value in result.fixed_values.items():
            print(f"  🔧 {field}: {value}")
    
    # اختبار 5: اختبار إنشاء ملف إعدادات صحيح
    print("\n📋 اختبار 5: اختبار إنشاء ملف إعدادات صحيح")
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        temp_config_path = f.name
    
    try:
        success = create_valid_config_file(temp_config_path)
        print(f"إنشاء ملف الإعدادات: {'✅ نجح' if success else '❌ فشل'}")
        
        if success:
            # التحقق من الملف المنشأ
            result = validate_config_file(temp_config_path)
            print(f"التحقق من الملف المنشأ: {'✅ صحيح' if result.is_valid else '❌ خطأ'}")
            
            # عرض محتوى الملف
            with open(temp_config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
                print(f"عدد الأقسام في الملف: {len(config_data)}")
                
                for section, data in config_data.items():
                    print(f"  📁 {section}: {len(data)} حقل")
    
    finally:
        # تنظيف الملف المؤقت
        if os.path.exists(temp_config_path):
            os.unlink(temp_config_path)
    
    print("\n✅ انتهى اختبار فاحص الإعدادات")

def test_schema_validation():
    """اختبار التحقق من المخطط"""
    print("\n🧪 اختبار التحقق من المخطط...")
    
    validator = ConfigValidator()
    
    # اختبار مخطط صحيح
    valid_config = {
        "app_info": {
            "app_name": "Test App",
            "app_version": "1.0.0",
            "app_description": "تطبيق اختبار",
            "environment": "development"
        },
        "user_preferences": {
            "language": "ar",
            "theme": "dark",
            "date_format": "dd/mm/yyyy",
            "time_format": "24h",
            "currency": "SAR"
        },
        "ai_settings": {
            "enabled": True,
            "model": "gpt-3.5-turbo",
            "max_tokens": 1000,
            "temperature": 0.7
        },
        "notification_settings": {
            "enabled": True,
            "sound_enabled": True,
            "desktop_notifications": True
        },
        "database_settings": {
            "type": "sqlite",
            "auto_backup": True
        },
        "security_settings": {
            "session_timeout": 30,
            "password_min_length": 8
        },
        "ui_settings": {
            "window_size": {
                "width": 1200,
                "height": 800
            },
            "sidebar_width": 250
        },
        "performance_settings": {
            "log_level": "INFO",
            "cache_enabled": True
        }
    }
    
    result = validator.validate_config(valid_config)
    print(f"اختبار مخطط صحيح: {'✅ نجح' if result.is_valid else '❌ فشل'}")
    
    # اختبار مخطط غير صحيح
    invalid_config = {
        "app_info": {
            "app_name": "",  # اسم فارغ
            "app_version": "invalid",  # إصدار غير صحيح
            "environment": "invalid"  # بيئة غير صحيحة
        }
    }
    
    result = validator.validate_config(invalid_config)
    print(f"اختبار مخطط غير صحيح: {'✅ نجح' if result.is_valid else '❌ فشل'} (متوقع: فشل)")

def test_error_handling():
    """اختبار معالجة الأخطاء"""
    print("\n🧪 اختبار معالجة الأخطاء...")
    
    # اختبار ملف غير موجود
    result = validate_config_file("/path/that/does/not/exist.json")
    print(f"اختبار ملف غير موجود: {'✅ نجح' if not result.is_valid else '❌ فشل'} (متوقع: فشل)")
    
    # اختبار ملف JSON غير صحيح
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        f.write('{"invalid": json}')
        temp_path = f.name
    
    try:
        result = validate_config_file(temp_path)
        print(f"اختبار JSON غير صحيح: {'✅ نجح' if not result.is_valid else '❌ فشل'} (متوقع: فشل)")
    finally:
        if os.path.exists(temp_path):
            os.unlink(temp_path)

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار فاحص الإعدادات")
    print("=" * 50)
    
    try:
        test_config_validator()
        test_schema_validation()
        test_error_handling()
        
        print("\n" + "=" * 50)
        print("✅ جميع الاختبارات اكتملت بنجاح!")
        
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main()) 