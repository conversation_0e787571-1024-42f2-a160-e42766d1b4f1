import logging
from .logger import setup_logging
from .settings import AppSettings
from .style_manager import StyleManager
from .event_bus import event_bus
from accounting_app.controllers.language_controller import LanguageController
from accounting_app.utils import check_resources_on_startup

class AppInitializer:
    """مُهيئ التطبيق الرئيسي"""
    
    def __init__(self, app):
        self.app = app
        self.settings = None
        self.language_controller = None
        self.event_bus = event_bus  # استخدام النسخة الموحدة

    def initialize(self):
        """تهيئة جميع مكونات التطبيق"""
        try:
            # إعداد نظام التسجيل
            setup_logging()
            
            # فحص موارد التطبيق
            logging.getLogger(__name__).info("🔍 فحص موارد التطبيق...")
            resources_ok = check_resources_on_startup()
            if not resources_ok:
                logging.getLogger(__name__).warning("⚠️  تم اكتشاف مشاكل في الموارد، لكن التطبيق سيستمر في العمل")
            
            # تحميل الإعدادات
            self.settings = AppSettings()
            
            # استخدام نظام الأحداث الموحد (لا حاجة لإنشاء نسخة جديدة)
            # self.event_bus = EventBus()  # تم إزالة هذا السطر
            
            # إعداد تحكم اللغة
            self.language_controller = LanguageController(
                self.app, 
                self.event_bus, 
                self.settings
            )
            
            # تطبيق الأنماط
            self._init_style()
            
            logging.getLogger(__name__).info("Application initialized successfully.")
        except Exception as e:
            logging.getLogger(__name__).exception(f"Initialization failed: {e}")
            raise

    def _init_style(self):
        """تهيئة الأنماط"""
        style = StyleManager(self.app, theme=self.settings.theme)
        style.apply_style() 