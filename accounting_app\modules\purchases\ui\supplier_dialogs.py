"""
نوافذ حوار فرعية لإدارة الموردين
"""

from PySide6.QtWidgets import (
    QDialog, QFormLayout, QLineEdit, QComboBox, QTextEdit, QCheckBox,
    QDateEdit, QDialogButtonBox
)
from PySide6.QtCore import QDate


class ContactDialog(QDialog):
    """نافذة حوار إضافة جهة اتصال"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle(self.tr("إضافة جهة اتصال"))
        self.setModal(True)
        self.init_ui()
    
    def init_ui(self):
        layout = QFormLayout(self)
        
        self.name_edit = QLineEdit()
        self.position_edit = QLineEdit()
        self.phone_edit = QLineEdit()
        self.mobile_edit = QLineEdit()
        self.email_edit = QLineEdit()
        self.is_primary_check = QCheckBox()
        
        layout.addRow(self.tr("الاسم:"), self.name_edit)
        layout.addRow(self.tr("المنصب:"), self.position_edit)
        layout.addRow(self.tr("الهاتف:"), self.phone_edit)
        layout.addRow(self.tr("الجوال:"), self.mobile_edit)
        layout.addRow(self.tr("البريد الإلكتروني:"), self.email_edit)
        layout.addRow(self.tr("جهة اتصال رئيسية:"), self.is_primary_check)
        
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addRow(buttons)
    
    def get_contact_data(self):
        return {
            'name': self.name_edit.text(),
            'position': self.position_edit.text(),
            'phone': self.phone_edit.text(),
            'mobile': self.mobile_edit.text(),
            'email': self.email_edit.text(),
            'is_primary': self.is_primary_check.isChecked()
        }


class AddressDialog(QDialog):
    """نافذة حوار إضافة عنوان"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle(self.tr("إضافة عنوان"))
        self.setModal(True)
        self.init_ui()
    
    def init_ui(self):
        layout = QFormLayout(self)
        
        self.type_combo = QComboBox()
        self.type_combo.addItems([self.tr("رئيسي"), self.tr("فرعي"), self.tr("مستودع"), self.tr("مكتب")])
        
        self.street_edit = QLineEdit()
        self.city_edit = QLineEdit()
        self.state_edit = QLineEdit()
        self.postal_code_edit = QLineEdit()
        self.country_edit = QLineEdit()
        self.country_edit.setText("السعودية")
        self.is_primary_check = QCheckBox()
        
        layout.addRow(self.tr("النوع:"), self.type_combo)
        layout.addRow(self.tr("الشارع:"), self.street_edit)
        layout.addRow(self.tr("المدينة:"), self.city_edit)
        layout.addRow(self.tr("المنطقة:"), self.state_edit)
        layout.addRow(self.tr("الرمز البريدي:"), self.postal_code_edit)
        layout.addRow(self.tr("الدولة:"), self.country_edit)
        layout.addRow(self.tr("عنوان رئيسي:"), self.is_primary_check)
        
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addRow(buttons)
    
    def get_address_data(self):
        return {
            'type': self.type_combo.currentText(),
            'street': self.street_edit.text(),
            'city': self.city_edit.text(),
            'state': self.state_edit.text(),
            'postal_code': self.postal_code_edit.text(),
            'country': self.country_edit.text(),
            'is_primary': self.is_primary_check.isChecked()
        }


class BankAccountDialog(QDialog):
    """نافذة حوار إضافة حساب بنكي"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle(self.tr("إضافة حساب بنكي"))
        self.setModal(True)
        self.init_ui()
    
    def init_ui(self):
        layout = QFormLayout(self)
        
        self.bank_name_edit = QLineEdit()
        self.account_number_edit = QLineEdit()
        self.iban_edit = QLineEdit()
        self.swift_code_edit = QLineEdit()
        self.account_holder_name_edit = QLineEdit()
        self.is_primary_check = QCheckBox()
        
        layout.addRow(self.tr("اسم البنك:"), self.bank_name_edit)
        layout.addRow(self.tr("رقم الحساب:"), self.account_number_edit)
        layout.addRow(self.tr("الآيبان:"), self.iban_edit)
        layout.addRow(self.tr("رمز السويفت:"), self.swift_code_edit)
        layout.addRow(self.tr("اسم صاحب الحساب:"), self.account_holder_name_edit)
        layout.addRow(self.tr("حساب رئيسي:"), self.is_primary_check)
        
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addRow(buttons)
    
    def get_bank_data(self):
        return {
            'bank_name': self.bank_name_edit.text(),
            'account_number': self.account_number_edit.text(),
            'iban': self.iban_edit.text(),
            'swift_code': self.swift_code_edit.text(),
            'account_holder_name': self.account_holder_name_edit.text(),
            'is_primary': self.is_primary_check.isChecked()
        }


class DocumentDialog(QDialog):
    """نافذة حوار إضافة وثيقة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle(self.tr("إضافة وثيقة"))
        self.setModal(True)
        self.init_ui()
    
    def init_ui(self):
        layout = QFormLayout(self)
        
        self.document_type_combo = QComboBox()
        self.document_type_combo.addItems([
            self.tr("سجل تجاري"),
            self.tr("رخصة تجارية"),
            self.tr("شهادة ضريبية"),
            self.tr("شهادة جودة"),
            self.tr("أخرى")
        ])
        
        self.document_number_edit = QLineEdit()
        self.issue_date_edit = QDateEdit()
        self.issue_date_edit.setDate(QDate.currentDate())
        self.expiry_date_edit = QDateEdit()
        self.expiry_date_edit.setDate(QDate.currentDate().addYears(1))
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(80)
        
        layout.addRow(self.tr("نوع الوثيقة:"), self.document_type_combo)
        layout.addRow(self.tr("رقم الوثيقة:"), self.document_number_edit)
        layout.addRow(self.tr("تاريخ الإصدار:"), self.issue_date_edit)
        layout.addRow(self.tr("تاريخ الانتهاء:"), self.expiry_date_edit)
        layout.addRow(self.tr("ملاحظات:"), self.notes_edit)
        
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addRow(buttons)
    
    def get_document_data(self):
        return {
            'document_type': self.document_type_combo.currentText(),
            'document_number': self.document_number_edit.text(),
            'issue_date': self.issue_date_edit.date().toString("yyyy-MM-dd"),
            'expiry_date': self.expiry_date_edit.date().toString("yyyy-MM-dd"),
            'notes': self.notes_edit.toPlainText()
        }
