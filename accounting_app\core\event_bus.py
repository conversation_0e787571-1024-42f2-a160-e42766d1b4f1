import logging
from collections import defaultdict
from typing import Any, Callable, List

logger = logging.getLogger(__name__)

class EventBus:
    """نظام الأحداث المركزي للتطبيق"""
    
    def __init__(self):
        self._listeners = defaultdict(list)
        self._event_history = []

    def subscribe(self, event_name: str, callback: Callable) -> None:
        """سجل وظيفة للاستماع لحدث معين"""
        if not callable(callback):
            logger.error(f"Invalid callback for event '{event_name}': {callback}")
            return
        
        self._listeners[event_name].append(callback)
        logger.debug(f"🔔 Subscribed to event '{event_name}' with callback: {callback.__name__}")

    def unsubscribe(self, event_name: str, callback: Callable) -> bool:
        """إلغاء الاشتراك من حدث معين"""
        if event_name in self._listeners and callback in self._listeners[event_name]:
            self._listeners[event_name].remove(callback)
            logger.debug(f"🔔 Unsubscribed from event '{event_name}' with callback: {callback.__name__}")
            return True
        return False

    def emit(self, event_name: str, data: Any = None) -> None:
        """إرسال الحدث لجميع المستمعين"""
        logger.info(f"🔔 Emit: {event_name} | Data: {data}")
        
        # تسجيل الحدث في التاريخ
        self._event_history.append({
            'event': event_name,
            'data': data,
            'listeners_count': len(self._listeners[event_name])
        })
        
        # إرسال الحدث لجميع المستمعين
        for callback in self._listeners[event_name]:
            try:
                callback(data)
                logger.debug(f"✅ Event '{event_name}' processed by {callback.__name__}")
            except Exception as e:
                logger.error(f"❌ Error in callback {callback.__name__} for event '{event_name}': {e}")

    def get_listeners_count(self, event_name: str) -> int:
        """عدد المستمعين لحدث معين"""
        return len(self._listeners[event_name])

    def get_all_events(self) -> List[str]:
        """قائمة بجميع الأحداث المسجلة"""
        return list(self._listeners.keys())

    def clear_event(self, event_name: str) -> None:
        """مسح جميع المستمعين لحدث معين"""
        if event_name in self._listeners:
            del self._listeners[event_name]
            logger.info(f"🔔 Cleared all listeners for event '{event_name}'")

    def clear_all(self) -> None:
        """مسح جميع الأحداث والمستمعين"""
        self._listeners.clear()
        self._event_history.clear()
        logger.info("🔔 Cleared all events and listeners")

    def get_event_history(self, limit: int = 10) -> List[dict]:
        """الحصول على تاريخ الأحداث الأخيرة"""
        return self._event_history[-limit:] if self._event_history else []

# نسخة موحدة يُستورد منها في أي مكان
event_bus = EventBus()

# أمثلة على الأحداث الشائعة
COMMON_EVENTS = {
    "language_changed": "تغيير اللغة",
    "theme_changed": "تغيير الثيم",
    "user_logged_in": "تسجيل دخول المستخدم",
    "user_logged_out": "تسجيل خروج المستخدم",
    "notification_received": "استقبال إشعار",
    "open_tab": "فتح تبويب",
    "ai_message_sent": "إرسال رسالة للذكاء الاصطناعي",
    "ai_message_received": "استقبال رد من الذكاء الاصطناعي",
    "show_toast": "عرض رسالة toast",
    "show_error": "عرض خطأ",
    "show_loading": "عرض شاشة التحميل",
    "hide_loading": "إخفاء شاشة التحميل"
} 