from PySide6.QtWidgets import QGraphicsDropShadowEffect
from PySide6.QtGui import QColor
from PySide6.QtCore import Qt

class ShadowEffects:
    """فئة مساعدة لتطبيق تأثيرات الظل على العناصر"""
    
    @staticmethod
    def apply_button_shadow(widget, blur_radius=8, x_offset=0, y_offset=2, color=None):
        """تطبيق ظل على الأزرار"""
        if color is None:
            color = QColor(0, 0, 0, 60)
        
        effect = QGraphicsDropShadowEffect()
        effect.setBlurRadius(blur_radius)
        effect.setXOffset(x_offset)
        effect.setYOffset(y_offset)
        effect.setColor(color)
        widget.setGraphicsEffect(effect)
        return effect
    
    @staticmethod
    def apply_card_shadow(widget, blur_radius=12, x_offset=0, y_offset=4, color=None):
        """تطبيق ظل على البطاقات والعناصر الكبيرة"""
        if color is None:
            color = QColor(0, 0, 0, 80)
        
        effect = QGraphicsDropShadowEffect()
        effect.setBlurRadius(blur_radius)
        effect.setXOffset(x_offset)
        effect.setYOffset(y_offset)
        effect.setColor(color)
        widget.setGraphicsEffect(effect)
        return effect
    
    @staticmethod
    def apply_toast_shadow(widget, blur_radius=16, x_offset=0, y_offset=6, color=None):
        """تطبيق ظل على رسائل Toast"""
        if color is None:
            color = QColor(0, 0, 0, 100)
        
        effect = QGraphicsDropShadowEffect()
        effect.setBlurRadius(blur_radius)
        effect.setXOffset(x_offset)
        effect.setYOffset(y_offset)
        effect.setColor(color)
        widget.setGraphicsEffect(effect)
        return effect
    
    @staticmethod
    def apply_dialog_shadow(widget, blur_radius=20, x_offset=0, y_offset=8, color=None):
        """تطبيق ظل على النوافذ المنبثقة"""
        if color is None:
            color = QColor(0, 0, 0, 120)
        
        effect = QGraphicsDropShadowEffect()
        effect.setBlurRadius(blur_radius)
        effect.setXOffset(x_offset)
        effect.setYOffset(y_offset)
        effect.setColor(color)
        widget.setGraphicsEffect(effect)
        return effect
    
    @staticmethod
    def remove_shadow(widget):
        """إزالة تأثير الظل من العنصر"""
        widget.setGraphicsEffect(None)
    
    @staticmethod
    def apply_hover_effect(widget, normal_shadow=None, hover_shadow=None):
        """تطبيق تأثير hover مع تغيير الظل"""
        if normal_shadow is None:
            normal_shadow = ShadowEffects.apply_button_shadow(widget)
        
        # حفظ مرجع للظل الحالي
        widget._normal_shadow = normal_shadow
        
        # تطبيق تأثير hover
        def on_enter():
            if hover_shadow:
                widget.setGraphicsEffect(hover_shadow)
        
        def on_leave():
            widget.setGraphicsEffect(widget._normal_shadow)
        
        widget.enterEvent = lambda event: on_enter()
        widget.leaveEvent = lambda event: on_leave()
        
        return normal_shadow 