from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QLineEdit, QTableWidget, QTableWidgetItem, QHeaderView, QPushButton, QDialog, QFormLayout, QDialogButtonBox, QHBoxLayout, QComboBox, QMessageBox, QLabel, QCheckBox, QTextEdit, QGroupBox, QFrame, QFileDialog, QScrollArea, QListWidget, QListWidgetItem, QToolTip, QGridLayout, QSpacerItem, QSizePolicy, QDateEdit, QTabWidget, QToolButton, QStyle
)
from PySide6.QtCore import Qt, QDate, QParallelAnimationGroup, QAbstractAnimation, QEasingCurve, QPropertyAnimation, QPoint, QSize
from datetime import datetime
from PySide6.QtGui import QPixmap, QIntValidator, QDoubleValidator, QIcon, QPainter, QColor, QPolygon
from PySide6.QtCore import QTimer

class CollapsibleGroupBox(QWidget):
    """
    مجموعة قابلة للطي مع عنوان وزر توسيع/طي.
    """
    def __init__(self, title, parent=None, expanded=False):
        super().__init__(parent)
        self.toggle_button = QToolButton(text=title, checkable=True, checked=expanded)
        self.toggle_button.setStyleSheet("QToolButton { border: none; font-weight: bold; font-size: 15px; text-align: right; }")
        self.toggle_button.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)
        self.toggle_button.setArrowType(Qt.DownArrow if expanded else Qt.RightArrow)
        self.toggle_button.clicked.connect(self.on_toggle)

        self.content_area = QWidget()
        self.content_area.setMaximumHeight(0 if not expanded else 16777215)
        self.content_area.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)

        self.anim = QPropertyAnimation(self.content_area, b"maximumHeight")
        self.anim.setDuration(200)
        self.anim.setEasingCurve(QEasingCurve.InOutCubic)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.addWidget(self.toggle_button)
        layout.addWidget(self.content_area)
        self.setLayout(layout)

    def setContentLayout(self, content_layout):
        self.content_area.setLayout(content_layout)
        self.content_area.setMaximumHeight(content_layout.sizeHint().height() if self.toggle_button.isChecked() else 0)

    def on_toggle(self):
        checked = self.toggle_button.isChecked()
        self.toggle_button.setArrowType(Qt.DownArrow if checked else Qt.RightArrow)
        content_height = self.content_area.layout().sizeHint().height()
        start_value = self.content_area.maximumHeight()
        end_value = content_height if checked else 0
        self.anim.stop()
        self.anim.setStartValue(start_value)
        self.anim.setEndValue(end_value)
        self.anim.start()

    def _create_arrow_icon(self, direction="right", color="#2563eb"):
        # رسم سهم بسيط بلون مخصص
        pixmap = QPixmap(24, 24)
        pixmap.fill(Qt.transparent)
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        painter.setPen(Qt.NoPen)
        painter.setBrush(QColor(color))
        points = {
            "right": [QPoint(8, 6), QPoint(16, 12), QPoint(8, 18)],
            "down": [QPoint(6, 8), QPoint(12, 16), QPoint(18, 8)]
        }
        painter.drawPolygon(QPolygon(points[direction]))
        painter.end()
        return QIcon(pixmap)

class ProductDialog(QDialog):
    def __init__(self, parent=None, product=None, categories=None, units=None, warehouses=None):
        super().__init__(parent)
        self.setWindowTitle(self.tr("إضافة / تعديل منتج"))
        self.setMinimumWidth(480)
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.product = product
        self.categories = categories or [
            "مواد غذائية", "مشروبات", "حلويات", "معلبات", "منتجات ألبان", "أجبان", "لحوم", "دواجن", "أسماك", "خضروات", "فواكه", "مخبوزات", "زيوت وسمن", "بهارات وتوابل", "مكسرات", "منظفات", "مستحضرات تجميل", "مستلزمات منزلية", "ورقيات", "مستلزمات أطفال", "مستلزمات طبية", "أدوات مدرسية", "أخرى"
        ]
        self.units = units or [
            "قطعة", "كرتون", "كيلوغرام", "غرام", "لتر", "مليلتر", "علبة", "صندوق", "رزمة", "باكيت", "دستة", "درزن", "حبة", "كيس", "شوال", "عبوة", "رول", "جالون", "برميل", "وحدة"
        ]
        self.warehouses = warehouses or ["المخزن الرئيسي", "مخزن فرعي"]
        self.init_ui()
        self.setFixedSize(520, 700)  # حجم ثابت للنافذة
        # لا تستخدم resize أو adjustSize بعد ذلك
        # إذا كان تعديل منتج، عبئ الحقول
        if self.product:
            self.set_product_data(self.product)

    def _adjust_dialog_size(self):
        self.adjustSize()
        self.updateGeometry()

    def init_ui(self):
        style = self.style()  # للحصول على أيقونات Qt القياسية
        arrow_color = "#2563eb"  # أزرق داكن متناسق مع الثيم
        # --- إنشاء جميع الحقول التي قد تستخدم مبكراً ---
        self.carton_qty_edit = QLineEdit(self)
        self.carton_count_edit = QLineEdit(self)
        self.qty_edit = QLineEdit(self)
        # --- تعريف جميع الحقول كخصائص للكائن ---
        # اسم المنتج
        self.name_edit = QLineEdit(self)
        self.name_expand_btn = QPushButton()
        self.name_expand_btn.setFixedWidth(28)
        self.name_expand_btn.setCheckable(True)
        self.name_expand_btn.setChecked(False)
        self.name_expand_btn.setIcon(self._create_arrow_icon("right", arrow_color))
        name_layout = QHBoxLayout()
        name_layout.addWidget(QLabel(self.tr("اسم المنتج:")))
        name_layout.addWidget(self.name_edit)
        name_layout.addWidget(self.name_expand_btn)
        self.name_advanced_widget = QWidget()
        name_advanced_layout = QVBoxLayout()
        name_advanced_layout.setContentsMargins(32, 0, 0, 0)
        self.internal_code_edit = QLineEdit(self)
        self.alt_barcode_edit = QLineEdit(self)
        name_advanced_layout.addWidget(QLabel(self.tr("كود داخلي/SKU إضافي:")))
        name_advanced_layout.addWidget(self.internal_code_edit)
        name_advanced_layout.addWidget(QLabel(self.tr("باركود بديل:")))
        name_advanced_layout.addWidget(self.alt_barcode_edit)
        self.name_advanced_widget.setLayout(name_advanced_layout)
        self.name_advanced_widget.setVisible(False)
        self.name_expand_btn.toggled.connect(lambda checked: self.name_advanced_widget.setVisible(checked))
        self.name_expand_btn.toggled.connect(lambda checked: self.name_expand_btn.setIcon(self._create_arrow_icon("down", arrow_color) if checked else self._create_arrow_icon("right", arrow_color)))
        self.name_expand_btn.toggled.connect(self._adjust_dialog_size)

        # الباركود / الرمز
        self.sku_edit = QLineEdit(self)
        self.sku_expand_btn = QPushButton()
        self.sku_expand_btn.setFixedWidth(28)
        self.sku_expand_btn.setCheckable(True)
        self.sku_expand_btn.setChecked(False)
        self.sku_expand_btn.setIcon(self._create_arrow_icon("right", arrow_color))
        sku_layout = QHBoxLayout()
        sku_layout.addWidget(QLabel(self.tr("الباركود / الرمز:")))
        sku_layout.addWidget(self.sku_edit)
        sku_layout.addWidget(self.sku_expand_btn)
        self.sku_advanced_widget = QWidget()
        sku_advanced_layout = QVBoxLayout()
        sku_advanced_layout.setContentsMargins(32, 0, 0, 0)
        self.scan_btn = QPushButton(self.tr("مسح باركود"), self)
        sku_advanced_layout.addWidget(self.scan_btn)
        self.sku_advanced_widget.setLayout(sku_advanced_layout)
        self.sku_advanced_widget.setVisible(False)
        self.sku_expand_btn.toggled.connect(lambda checked: self.sku_advanced_widget.setVisible(checked))
        self.sku_expand_btn.toggled.connect(lambda checked: self.sku_expand_btn.setIcon(self._create_arrow_icon("down", arrow_color) if checked else self._create_arrow_icon("right", arrow_color)))
        self.sku_expand_btn.toggled.connect(self._adjust_dialog_size)

        # الفئة
        self.category_combo = QComboBox(self)
        self.category_combo.addItems(self.categories)
        self.category_expand_btn = QPushButton()
        self.category_expand_btn.setFixedWidth(28)
        self.category_expand_btn.setCheckable(True)
        self.category_expand_btn.setChecked(False)
        self.category_expand_btn.setIcon(self._create_arrow_icon("right", arrow_color))
        category_layout = QHBoxLayout()
        category_layout.addWidget(QLabel(self.tr("الفئة:")))
        category_layout.addWidget(self.category_combo)
        category_layout.addWidget(self.category_expand_btn)
        self.category_advanced_widget = QWidget()
        category_advanced_layout = QVBoxLayout()
        category_advanced_layout.setContentsMargins(32, 0, 0, 0)
        self.manage_categories_btn = QPushButton(self.tr("إدارة الفئات"), self)
        category_advanced_layout.addWidget(self.manage_categories_btn)
        self.category_advanced_widget.setLayout(category_advanced_layout)
        self.category_advanced_widget.setVisible(False)
        self.category_expand_btn.toggled.connect(lambda checked: self.category_advanced_widget.setVisible(checked))
        self.category_expand_btn.toggled.connect(lambda checked: self.category_expand_btn.setIcon(self._create_arrow_icon("down", arrow_color) if checked else self._create_arrow_icon("right", arrow_color)))
        self.category_expand_btn.toggled.connect(self._adjust_dialog_size)

        # الوحدة
        self.unit_combo = QComboBox(self)
        self.unit_combo.addItems(self.units)
        self.unit_combo.currentTextChanged.connect(self.toggle_carton_fields)
        self.unit_expand_btn = QPushButton()
        self.unit_expand_btn.setFixedWidth(28)
        self.unit_expand_btn.setCheckable(True)
        self.unit_expand_btn.setChecked(False)
        self.unit_expand_btn.setIcon(self._create_arrow_icon("right", arrow_color))
        unit_layout = QHBoxLayout()
        unit_layout.addWidget(QLabel(self.tr("الوحدة:")))
        unit_layout.addWidget(self.unit_combo)
        unit_layout.addWidget(self.unit_expand_btn)
        self.unit_advanced_widget = QWidget()
        unit_advanced_layout = QVBoxLayout()
        unit_advanced_layout.setContentsMargins(32, 0, 0, 0)
        self.carton_qty_edit.setPlaceholderText(self.tr("كمية في الكرتون"))
        self.carton_count_edit.setPlaceholderText(self.tr("عدد الكراتين"))
        self.carton_qty_edit.setValidator(QIntValidator(0, 999999, self))
        self.carton_count_edit.setValidator(QIntValidator(0, 999999, self))
        self.carton_qty_edit.textChanged.connect(self.update_total_qty)
        self.carton_count_edit.textChanged.connect(self.update_total_qty)
        unit_advanced_layout.addWidget(QLabel(self.tr("كمية في الكرتون:")))
        unit_advanced_layout.addWidget(self.carton_qty_edit)
        unit_advanced_layout.addWidget(QLabel(self.tr("عدد الكراتين:")))
        unit_advanced_layout.addWidget(self.carton_count_edit)
        self.unit_advanced_widget.setLayout(unit_advanced_layout)
        self.unit_advanced_widget.setVisible(False)
        # تفعيل الحالة الصحيحة بعد إنشاء الحقول
        self.toggle_carton_fields(self.unit_combo.currentText())
        self.unit_expand_btn.toggled.connect(lambda checked: self.unit_advanced_widget.setVisible(checked))
        self.unit_expand_btn.toggled.connect(lambda checked: self.unit_expand_btn.setIcon(self._create_arrow_icon("down", arrow_color) if checked else self._create_arrow_icon("right", arrow_color)))
        self.unit_expand_btn.toggled.connect(self._adjust_dialog_size)
        # عند تغيير الوحدة، تحقق من فتح/إغلاق الحقول المنطوية تلقائياً
        self.unit_combo.currentTextChanged.connect(self._auto_expand_unit_advanced)

        # سعر الشراء
        self.purchase_price_edit = QLineEdit(self)
        self.purchase_expand_btn = QPushButton()
        self.purchase_expand_btn.setFixedWidth(28)
        self.purchase_expand_btn.setCheckable(True)
        self.purchase_expand_btn.setChecked(False)
        self.purchase_expand_btn.setIcon(self._create_arrow_icon("right", arrow_color))
        purchase_layout = QHBoxLayout()
        purchase_layout.addWidget(QLabel(self.tr("سعر الشراء:")))
        purchase_layout.addWidget(self.purchase_price_edit)
        purchase_layout.addWidget(self.purchase_expand_btn)
        self.purchase_advanced_widget = QWidget()
        purchase_advanced_layout = QVBoxLayout()
        purchase_advanced_layout.setContentsMargins(32, 0, 0, 0)
        self.tax_edit = QLineEdit(self)
        self.wholesale_price_edit = QLineEdit(self)
        purchase_advanced_layout.addWidget(QLabel(self.tr("الضريبة (%):")))
        purchase_advanced_layout.addWidget(self.tax_edit)
        purchase_advanced_layout.addWidget(QLabel(self.tr("سعر الجملة:")))
        purchase_advanced_layout.addWidget(self.wholesale_price_edit)
        self.purchase_advanced_widget.setLayout(purchase_advanced_layout)
        self.purchase_advanced_widget.setVisible(False)
        self.purchase_expand_btn.toggled.connect(lambda checked: self.purchase_advanced_widget.setVisible(checked))
        self.purchase_expand_btn.toggled.connect(lambda checked: self.purchase_expand_btn.setIcon(self._create_arrow_icon("down", arrow_color) if checked else self._create_arrow_icon("right", arrow_color)))
        self.purchase_expand_btn.toggled.connect(self._adjust_dialog_size)

        # سعر البيع
        self.sale_price_edit = QLineEdit(self)
        self.sale_expand_btn = QPushButton()
        self.sale_expand_btn.setFixedWidth(28)
        self.sale_expand_btn.setCheckable(True)
        self.sale_expand_btn.setChecked(False)
        self.sale_expand_btn.setIcon(self._create_arrow_icon("right", arrow_color))
        sale_layout = QHBoxLayout()
        sale_layout.addWidget(QLabel(self.tr("سعر البيع:")))
        sale_layout.addWidget(self.sale_price_edit)
        sale_layout.addWidget(self.sale_expand_btn)
        self.sale_advanced_widget = QWidget()
        sale_advanced_layout = QVBoxLayout()
        sale_advanced_layout.setContentsMargins(32, 0, 0, 0)
        self.valuation_combo = QComboBox(self)
        self.min_sale_qty_edit = QLineEdit(self)
        sale_advanced_layout.addWidget(QLabel(self.tr("طريقة التقييم:")))
        sale_advanced_layout.addWidget(self.valuation_combo)
        sale_advanced_layout.addWidget(QLabel(self.tr("حد أدنى للبيع:")))
        sale_advanced_layout.addWidget(self.min_sale_qty_edit)
        self.sale_advanced_widget.setLayout(sale_advanced_layout)
        self.sale_advanced_widget.setVisible(False)
        self.sale_expand_btn.toggled.connect(lambda checked: self.sale_advanced_widget.setVisible(checked))
        self.sale_expand_btn.toggled.connect(lambda checked: self.sale_expand_btn.setIcon(self._create_arrow_icon("down", arrow_color) if checked else self._create_arrow_icon("right", arrow_color)))
        self.sale_expand_btn.toggled.connect(self._adjust_dialog_size)

        # الكمية الحالية
        # تم إنشاؤها مسبقاً في الأعلى
        self.qty_expand_btn = QPushButton()
        self.qty_expand_btn.setFixedWidth(28)
        self.qty_expand_btn.setCheckable(True)
        self.qty_expand_btn.setChecked(False)
        self.qty_expand_btn.setIcon(self._create_arrow_icon("right", arrow_color))
        qty_layout = QHBoxLayout()
        qty_layout.addWidget(QLabel(self.tr("الكمية الحالية:")))
        qty_layout.addWidget(self.qty_edit)
        qty_layout.addWidget(self.qty_expand_btn)
        self.qty_advanced_widget = QWidget()
        qty_advanced_layout = QVBoxLayout()
        qty_advanced_layout.setContentsMargins(32, 0, 0, 0)
        self.min_qty_edit = QLineEdit(self)
        self.max_qty_edit = QLineEdit(self)
        qty_advanced_layout.addWidget(QLabel(self.tr("حد إعادة الطلب:")))
        qty_advanced_layout.addWidget(self.min_qty_edit)
        qty_advanced_layout.addWidget(QLabel(self.tr("الحد الأقصى:")))
        qty_advanced_layout.addWidget(self.max_qty_edit)
        self.qty_advanced_widget.setLayout(qty_advanced_layout)
        self.qty_advanced_widget.setVisible(False)
        self.qty_expand_btn.toggled.connect(lambda checked: self.qty_advanced_widget.setVisible(checked))
        self.qty_expand_btn.toggled.connect(lambda checked: self.qty_expand_btn.setIcon(self._create_arrow_icon("down", arrow_color) if checked else self._create_arrow_icon("right", arrow_color)))
        self.qty_expand_btn.toggled.connect(self._adjust_dialog_size)
        # تفعيل الحالة الصحيحة بعد إنشاء جميع الحقول
        self.toggle_carton_fields(self.unit_combo.currentText())

        # المستودع
        self.warehouse_combo = QComboBox(self)
        self.warehouse_expand_btn = QPushButton()
        self.warehouse_expand_btn.setFixedWidth(28)
        self.warehouse_expand_btn.setCheckable(True)
        self.warehouse_expand_btn.setChecked(False)
        self.warehouse_expand_btn.setIcon(self._create_arrow_icon("right", arrow_color))
        warehouse_layout = QHBoxLayout()
        warehouse_layout.addWidget(QLabel(self.tr("المستودع:")))
        warehouse_layout.addWidget(self.warehouse_combo)
        warehouse_layout.addWidget(self.warehouse_expand_btn)
        # إضافة حقل الموقع (location) فقط
        self.location_combo = QComboBox(self)
        warehouse_location_layout = QHBoxLayout()
        warehouse_location_layout.addWidget(QLabel(self.tr("الموقع:")))
        warehouse_location_layout.addWidget(self.location_combo)
        # إضافة زر جانبي للطي/توسيع بجانب الموقع
        self.location_expand_btn = QPushButton()
        self.location_expand_btn.setFixedWidth(28)
        self.location_expand_btn.setCheckable(True)
        self.location_expand_btn.setChecked(False)
        self.location_expand_btn.setIcon(self._create_arrow_icon("right", arrow_color))
        warehouse_location_layout.addWidget(self.location_expand_btn)
        # تعبئة المستودعات والمواقع
        # إذا كانت self.warehouses قائمة أسماء فقط، حولها لقائمة قواميس
        if self.warehouses and isinstance(self.warehouses[0], str):
            self.warehouses = [
                {"name": w, "location": ""} for w in self.warehouses
            ]
        self.warehouse_combo.clear()
        self.warehouse_combo.addItems([w["name"] for w in self.warehouses])
        # تعبئة المواقع لأول مستودع
        self._update_locations_for_selected_warehouse()
        self.warehouse_combo.currentIndexChanged.connect(self._update_locations_for_selected_warehouse)

        # حالة المنتج
        self.active_check = QCheckBox(self.tr("نشط"), self)
        self.status_expand_btn = QPushButton()
        self.status_expand_btn.setFixedWidth(28)
        self.status_expand_btn.setCheckable(True)
        self.status_expand_btn.setChecked(False)
        self.status_expand_btn.setIcon(self._create_arrow_icon("right", arrow_color))
        status_layout = QHBoxLayout()
        status_layout.addWidget(QLabel(self.tr("حالة المنتج:")))
        status_layout.addWidget(self.active_check)
        status_layout.addWidget(self.status_expand_btn)
        self.status_advanced_widget = QWidget()
        status_advanced_layout = QVBoxLayout()
        status_advanced_layout.setContentsMargins(32, 0, 0, 0)
        # استبدال QLabel بـ QDateEdit
        from PySide6.QtWidgets import QDateEdit
        from PySide6.QtCore import QDate
        self.created_at_edit = QDateEdit(self)
        self.created_at_edit.setCalendarPopup(True)
        self.created_at_edit.setDisplayFormat("yyyy-MM-dd")
        self.updated_at_edit = QDateEdit(self)
        self.updated_at_edit.setCalendarPopup(True)
        self.updated_at_edit.setDisplayFormat("yyyy-MM-dd")
        # تعبئة افتراضية
        today = QDate.currentDate()
        self.created_at_edit.setDate(today)
        self.updated_at_edit.setDate(today)
        status_advanced_layout.addWidget(QLabel(self.tr("تاريخ الإضافة:")))
        status_advanced_layout.addWidget(self.created_at_edit)
        status_advanced_layout.addWidget(QLabel(self.tr("تاريخ آخر تحديث:")))
        status_advanced_layout.addWidget(self.updated_at_edit)
        self.status_advanced_widget.setLayout(status_advanced_layout)
        self.status_advanced_widget.setVisible(False)
        self.status_expand_btn.toggled.connect(lambda checked: self.status_advanced_widget.setVisible(checked))
        self.status_expand_btn.toggled.connect(lambda checked: self.status_expand_btn.setIcon(self._create_arrow_icon("down", arrow_color) if checked else self._create_arrow_icon("right", arrow_color)))
        self.status_expand_btn.toggled.connect(self._adjust_dialog_size)

        # الصورة الرئيسية
        self.image_icon_label = QLabel(self)
        self.image_icon_label.setFixedSize(16, 16)
        self.image_icon_label.setAlignment(Qt.AlignCenter)
        self.image_icon_label.setText("🖼️")
        self.image_icon_label.setStyleSheet("font-size: 14px; color: #2563eb;")

        self.image_label = QLabel(self)
        self.image_label.setFixedSize(180, 100)
        self.image_label.setAlignment(Qt.AlignCenter)
        self.image_label.setStyleSheet("border: 1.5px dashed #2563eb; background: #fafbfc; font-size: 14px;")
        self.image_label.setText(self.tr("اضغط لإضافة صورة"))
        self.image_label.setCursor(Qt.PointingHandCursor)
        self.main_image_path = None
        self.image_label.mousePressEvent = self.select_main_image  # تفعيل النقر

        self.image_expand_btn = QPushButton()
        self.image_expand_btn.setFixedWidth(28)
        self.image_expand_btn.setCheckable(True)
        self.image_expand_btn.setChecked(False)
        self.image_expand_btn.setIcon(self._create_arrow_icon("right", arrow_color))

        image_layout = QHBoxLayout()
        image_layout.setSpacing(8)
        image_layout.addWidget(self.image_icon_label)
        image_layout.addWidget(self.image_label)
        image_layout.addWidget(self.image_expand_btn)
        self.image_advanced_widget = QWidget()
        image_advanced_layout = QVBoxLayout()
        image_advanced_layout.setContentsMargins(32, 0, 0, 0)
        # خانة صورة إضافية تفاعلية
        # خانة صورة إضافية تفاعلية وزر إرفاق ملف في نفس السطر
        extra_row_layout = QHBoxLayout()
        extra_row_layout.setSpacing(12)
        # صورة إضافية
        self.extra_image_label = QLabel(self)
        self.extra_image_label.setFixedSize(100, 80)
        self.extra_image_label.setAlignment(Qt.AlignCenter)
        self.extra_image_label.setStyleSheet("border: 1.2px dashed #aaa; background: #fafbfc; font-size: 12px;")
        self.extra_image_label.setText(self.tr("اضغط لإضافة صورة إضافية"))
        self.extra_image_label.setCursor(Qt.PointingHandCursor)
        self.extra_image_path = None
        self.extra_image_label.mousePressEvent = self.select_extra_image
        # زر إرفاق ملف
        self.attach_btn = QLabel(self)
        self.attach_btn.setFixedSize(100, 80)
        self.attach_btn.setAlignment(Qt.AlignCenter)
        self.attach_btn.setStyleSheet("border: 1.2px dashed #aaa; background: #fafbfc; font-size: 12px;")
        self.attach_btn.setText(self.tr("اضغط لإرفاق ملف"))
        self.attach_btn.setCursor(Qt.PointingHandCursor)
        self.attached_file_path = None
        self.attach_btn.mousePressEvent = self.select_attached_file
        extra_row_layout.addWidget(self.extra_image_label)
        extra_row_layout.addWidget(self.attach_btn)
        image_advanced_layout.addLayout(extra_row_layout)
        self.image_advanced_widget.setLayout(image_advanced_layout)
        self.image_advanced_widget.setVisible(False)
        self.image_expand_btn.toggled.connect(lambda checked: self.image_advanced_widget.setVisible(checked))
        self.image_expand_btn.toggled.connect(lambda checked: self.image_expand_btn.setIcon(self._create_arrow_icon("down", arrow_color) if checked else self._create_arrow_icon("right", arrow_color)))
        self.image_expand_btn.toggled.connect(self._adjust_dialog_size)

        # --- بناء التخطيط الرئيسي ---
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(12)
        # منطقة التمرير
        scroll_area = QScrollArea(self)
        scroll_area.setWidgetResizable(True)
        scroll_content = QWidget()
        layout = QVBoxLayout(scroll_content)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(12)
        layout.addLayout(name_layout)
        layout.addWidget(self.name_advanced_widget)
        layout.addLayout(sku_layout)
        layout.addWidget(self.sku_advanced_widget)
        layout.addLayout(category_layout)
        layout.addWidget(self.category_advanced_widget)
        layout.addLayout(unit_layout)
        layout.addWidget(self.unit_advanced_widget)
        layout.addLayout(purchase_layout)
        layout.addWidget(self.purchase_advanced_widget)
        layout.addLayout(sale_layout)
        layout.addWidget(self.sale_advanced_widget)
        layout.addLayout(qty_layout)
        layout.addWidget(self.qty_advanced_widget)
        layout.addLayout(warehouse_layout)
        layout.addLayout(warehouse_location_layout)
        layout.addLayout(status_layout)
        layout.addWidget(self.status_advanced_widget)
        layout.addLayout(image_layout)
        layout.addWidget(self.image_advanced_widget)
        scroll_area.setWidget(scroll_content)
        main_layout.addWidget(scroll_area)
        # --- أزرار الحفظ والإلغاء ---
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel, self)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        main_layout.addWidget(buttons)
        self.setLayout(main_layout)

        # منطق التمرير التلقائي عند التوسيع
        def scroll_to_widget(widget):
            QTimer.singleShot(0, lambda: scroll_area.ensureWidgetVisible(widget))
        self.image_expand_btn.toggled.connect(lambda checked: scroll_to_widget(self.image_advanced_widget) if checked else None)
        self.status_expand_btn.toggled.connect(lambda checked: scroll_to_widget(self.status_advanced_widget) if checked else None)
        self.name_expand_btn.toggled.connect(lambda checked: scroll_to_widget(self.name_advanced_widget) if checked else None)
        self.sku_expand_btn.toggled.connect(lambda checked: scroll_to_widget(self.sku_advanced_widget) if checked else None)
        self.category_expand_btn.toggled.connect(lambda checked: scroll_to_widget(self.category_advanced_widget) if checked else None)
        self.unit_expand_btn.toggled.connect(lambda checked: scroll_to_widget(self.unit_advanced_widget) if checked else None)
        self.purchase_expand_btn.toggled.connect(lambda checked: scroll_to_widget(self.purchase_advanced_widget) if checked else None)
        self.sale_expand_btn.toggled.connect(lambda checked: scroll_to_widget(self.sale_advanced_widget) if checked else None)
        self.qty_expand_btn.toggled.connect(lambda checked: scroll_to_widget(self.qty_advanced_widget) if checked else None)

    def _create_arrow_icon(self, direction="right", color="#2563eb"):
        # رسم سهم بسيط بلون مخصص
        pixmap = QPixmap(24, 24)
        pixmap.fill(Qt.transparent)
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        painter.setPen(Qt.NoPen)
        painter.setBrush(QColor(color))
        points = {
            "right": [QPoint(8, 6), QPoint(16, 12), QPoint(8, 18)],
            "down": [QPoint(6, 8), QPoint(12, 16), QPoint(18, 8)]
        }
        painter.drawPolygon(QPolygon(points[direction]))
        painter.end()
        return QIcon(pixmap)

    def toggle_carton_fields(self, unit):
        # الوحدات التي تحتاج حساب تلقائي وتظهر الحقول الخاصة بها
        auto_units = [
            "كرتون", "علبة", "صندوق", "رزمة", "باكيت", "شوال", "عبوة", "رول", "دستة", "درزن"
        ]
        unit_clean = unit.strip().lower()
        match = any(unit_clean == u.lower() for u in auto_units)
        if match:
            self.carton_qty_edit.show()
            self.carton_count_edit.show()
            self.carton_qty_edit.setEnabled(True)
            self.carton_count_edit.setEnabled(True)
            self.update_total_qty()
        else:
            self.carton_qty_edit.hide()
            self.carton_count_edit.hide()
            self.carton_qty_edit.setText("")
            self.carton_count_edit.setText("")
            self.qty_edit.setText("")

    def update_total_qty(self):
        # إذا الوحدة من الوحدات الذكية، احسب الكمية تلقائياً
        auto_units = [
            "كرتون", "علبة", "صندوق", "رزمة", "باكيت", "شوال", "عبوة", "رول", "دستة", "درزن"
        ]
        unit = self.unit_combo.currentText()
        unit_clean = unit.strip().lower()
        match = any(unit_clean == u.lower() for u in auto_units)
        if match:
            try:
                carton_qty = int(self.carton_qty_edit.text()) if self.carton_qty_edit.text() else 0
                carton_count = int(self.carton_count_edit.text()) if self.carton_count_edit.text() else 0
                total = carton_qty * carton_count
                self.qty_edit.setText(str(total) if total > 0 else "")
            except Exception:
                self.qty_edit.setText("")
        # إذا الوحدة ليست من auto_units، لا يتم التحديث تلقائياً

    def validate_field(self, widget, typ, required=False, min_value=None, max_value=None):
        value = widget.text()
        valid = True
        msg = ""
        if required and not value.strip():
            valid = False
            msg = self.tr("هذا الحقل مطلوب")
        else:
            try:
                val = typ(value)
                if min_value is not None and val < min_value:
                    valid = False
                    msg = self.tr(f"القيمة يجب أن تكون >= {min_value}")
                if max_value is not None and val > max_value:
                    valid = False
                    msg = self.tr(f"القيمة يجب أن تكون <= {max_value}")
            except Exception:
                valid = False
                msg = self.tr("قيمة غير صالحة")
        if not valid:
            widget.setStyleSheet("border: 1px solid red;")
            QToolTip.showText(widget.mapToGlobal(widget.rect().bottomRight()), msg, widget)
        else:
            widget.setStyleSheet("")
            QToolTip.hideText()
        return valid

    def select_main_image(self, event):
        file, _ = QFileDialog.getOpenFileName(self, self.tr("اختر صورة رئيسية"), "", "Images (*.png *.jpg *.jpeg *.bmp)")
        if file:
            pixmap = QPixmap(file)
            self.image_label.setPixmap(pixmap.scaled(self.image_label.size(), Qt.KeepAspectRatio, Qt.SmoothTransformation))
            self.image_label.setText("")
            self.main_image_path = file
        else:
            if not self.main_image_path:
                self.image_label.setPixmap(QPixmap())
                self.image_label.setText(self.tr("اضغط لإضافة صورة"))

    def select_extra_images(self):
        files, _ = QFileDialog.getOpenFileNames(self, self.tr("اختر صور إضافية"), "", "Images (*.png *.jpg *.jpeg *.bmp)")
        for file in files:
            item = QListWidgetItem(file)
            self.extra_images_list.addItem(item)

    def select_extra_image(self, event):
        file, _ = QFileDialog.getOpenFileName(self, self.tr("اختر صورة إضافية"), "", "Images (*.png *.jpg *.jpeg *.bmp)")
        if file:
            pixmap = QPixmap(file)
            self.extra_image_label.setPixmap(pixmap.scaled(self.extra_image_label.size(), Qt.KeepAspectRatio, Qt.SmoothTransformation))
            self.extra_image_label.setText("")
            self.extra_image_path = file
        else:
            if not self.extra_image_path:
                self.extra_image_label.setPixmap(QPixmap())
                self.extra_image_label.setText(self.tr("اضغط لإضافة صورة إضافية"))

    def select_attached_file(self, event):
        file, _ = QFileDialog.getOpenFileName(self, self.tr("إرفاق ملف"), "", self.tr("All Files (*.*)"))
        if file:
            filename = file.split("/")[-1]
            self.attach_btn.setText(filename)
            self.attached_file_path = file
        else:
            self.attach_btn.setText(self.tr("اضغط لإرفاق ملف"))
            self.attached_file_path = None

    def get_data(self):
        return {
            "name": self.name_edit.text(),
            "sku": self.sku_edit.text(),
            "category": self.category_combo.currentText(),
            "unit": self.unit_combo.currentText(),
            "purchase_price": self.purchase_price_edit.text(),
            "sale_price": self.sale_price_edit.text(),
            "tax": self.tax_edit.text(),
            "qty": self.qty_edit.text(),
            "min_qty": self.min_qty_edit.text(),
            "max_qty": self.max_qty_edit.text(),
            "valuation_method": self.valuation_combo.currentText(),
            "description": self.description_edit.toPlainText(),
            "is_active": self.active_check.isChecked(),
            "internal_code": self.internal_code_edit.text(),
            "brand": self.brand_edit.text(),
            "warehouse": self.warehouse_combo.currentText(),
            "location": self.location_combo.currentText(),
            "internal_notes": self.internal_notes_edit.toPlainText(),
            "min_sale_qty": self.min_sale_qty_edit.text(),
            "alt_barcode": self.alt_barcode_edit.text(),
            "wholesale_price": self.wholesale_price_edit.text(),
            # التواريخ من QDateEdit
            "created_at": self.created_at_edit.date().toString("yyyy-MM-dd"),
            "updated_at": self.updated_at_edit.date().toString("yyyy-MM-dd"),
        }

    def set_product_data(self, product):
        # إذا كان هناك منتج للتعديل، عبئ الحقول
        if product:
            # ... باقي الحقول ...
            from PySide6.QtCore import QDate
            if product.get("created_at"):
                self.created_at_edit.setDate(QDate.fromString(product["created_at"], "yyyy-MM-dd"))
            if product.get("updated_at"):
                self.updated_at_edit.setDate(QDate.fromString(product["updated_at"], "yyyy-MM-dd"))

    def toggle_show_more_basic(self):
        checked = self.show_more_btn.isChecked()
        self.advanced_basic_widget.setVisible(checked)
        self.show_more_btn.setText(self.tr("إخفاء المزيد ⯅") if checked else self.tr("عرض المزيد ⯆"))

    def _auto_expand_unit_advanced(self, unit):
        # الوحدات التي تحتاج فتح الحقول المنطوية تلقائياً
        auto_units = [
            "كرتون", "علبة", "صندوق", "رزمة", "باكيت", "شوال", "عبوة", "رول", "دستة", "درزن"
        ]
        unit_clean = unit.strip().lower()
        match = any(unit_clean == u.lower() for u in auto_units)
        if match:
            if not self.unit_expand_btn.isChecked():
                self.unit_expand_btn.setChecked(True)
        else:
            if self.unit_expand_btn.isChecked():
                self.unit_expand_btn.setChecked(False)

    def _update_locations_for_selected_warehouse(self):
        idx = self.warehouse_combo.currentIndex()
        if idx < 0 or idx >= len(self.warehouses):
            self.location_combo.clear()
            return
        warehouse = self.warehouses[idx]
        # إذا كان الموقع نص فقط
        if isinstance(warehouse.get("location"), str):
            locations = [warehouse["location"]] if warehouse["location"] else []
        else:
            locations = warehouse.get("location", [])
        self.location_combo.clear()
        self.location_combo.addItems(locations)

class ProductListTab(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("ProductListTab")
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout(self)
        # شريط البحث المتقدم
        search_layout = QHBoxLayout()
        self.search_box = QLineEdit(self)
        self.search_box.setPlaceholderText(self.tr("بحث عن منتج بالاسم أو الباركود..."))
        self.category_filter = QComboBox(self)
        self.category_filter.addItem(self.tr("كل الفئات"))
        self.category_filter.addItems(["فئة 1", "فئة 2"])
        self.status_filter = QComboBox(self)
        self.status_filter.addItems([self.tr("الكل"), self.tr("نشط"), self.tr("غير نشط")])
        search_layout.addWidget(QLabel(self.tr("بحث:")))
        search_layout.addWidget(self.search_box)
        search_layout.addWidget(QLabel(self.tr("الفئة:")))
        search_layout.addWidget(self.category_filter)
        search_layout.addWidget(QLabel(self.tr("الحالة:")))
        search_layout.addWidget(self.status_filter)
        layout.addLayout(search_layout)
        # أزرار أعلى الجدول
        btn_layout = QHBoxLayout()
        self.add_btn = QPushButton(self.tr("إضافة منتج جديد"), self)
        self.add_btn.clicked.connect(self.add_product)
        self.export_btn = QPushButton(self.tr("تصدير"), self)
        self.delete_selected_btn = QPushButton(self.tr("حذف جماعي"), self)
        btn_layout.addWidget(self.add_btn)
        btn_layout.addWidget(self.export_btn)
        btn_layout.addWidget(self.delete_selected_btn)
        btn_layout.addStretch()
        layout.addLayout(btn_layout)
        # جدول المنتجات
        self.table = QTableWidget(self)
        self.table.setColumnCount(15)
        self.table.setHorizontalHeaderLabels([
            self.tr("اسم المنتج"), self.tr("الباركود"), self.tr("الفئة"), self.tr("الوحدة"),
            self.tr("سعر الشراء"), self.tr("سعر البيع"), self.tr("الكمية"), self.tr("الحد الأدنى"), self.tr("الحد الأقصى"),
            self.tr("طريقة التقييم"), self.tr("الوصف"), self.tr("الحالة"), self.tr("تاريخ الإنشاء"), self.tr("تاريخ التحديث"), self.tr("إجراءات")
        ])
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setSelectionMode(QTableWidget.MultiSelection)
        layout.addWidget(self.table)
        self.setLayout(layout)
        # بيانات تجريبية
        self.products = [
            {"name": "منتج 1", "sku": "123456", "category": "فئة 1", "unit": "قطعة", "purchase_price": 10.5, "sale_price": 15.0, "qty": 100, "min_qty": 10, "max_qty": 200, "valuation_method": self.tr("FIFO"), "description": "منتج عالي الجودة", "is_active": True, "created_at": "2024-06-01", "updated_at": "2024-06-10"},
            {"name": "منتج 2", "sku": "789012", "category": "فئة 2", "unit": "كرتون", "purchase_price": 20.0, "sale_price": 30.0, "qty": 5, "min_qty": 10, "max_qty": 100, "valuation_method": self.tr("LIFO"), "description": "منتج موسمي", "is_active": False, "created_at": "2024-06-02", "updated_at": "2024-06-11"},
        ]
        self.refresh_table()
        self.search_box.textChanged.connect(self.filter_table)
        self.category_filter.currentTextChanged.connect(self.filter_table)
        self.status_filter.currentTextChanged.connect(self.filter_table)
        self.delete_selected_btn.clicked.connect(self.delete_selected)

    def refresh_table(self):
        self.table.setRowCount(0)
        for product in self.products:
            row = self.table.rowCount()
            self.table.insertRow(row)
            self.table.setItem(row, 0, QTableWidgetItem(product["name"]))
            self.table.setItem(row, 1, QTableWidgetItem(product["sku"]))
            self.table.setItem(row, 2, QTableWidgetItem(product["category"]))
            self.table.setItem(row, 3, QTableWidgetItem(product["unit"]))
            self.table.setItem(row, 4, QTableWidgetItem(str(product["purchase_price"])))
            self.table.setItem(row, 5, QTableWidgetItem(str(product["sale_price"])))
            qty_item = QTableWidgetItem(str(product["qty"]))
            if product["qty"] < product["min_qty"]:
                qty_item.setBackground(Qt.red)
            self.table.setItem(row, 6, qty_item)
            self.table.setItem(row, 7, QTableWidgetItem(str(product["min_qty"])))
            self.table.setItem(row, 8, QTableWidgetItem(str(product["max_qty"])))
            self.table.setItem(row, 9, QTableWidgetItem(product.get("valuation_method", "")))
            self.table.setItem(row, 10, QTableWidgetItem(product.get("description", "")))
            status = self.tr("نشط") if product["is_active"] else self.tr("غير نشط")
            status_item = QTableWidgetItem(status)
            status_item.setForeground(Qt.green if product["is_active"] else Qt.gray)
            self.table.setItem(row, 11, status_item)
            self.table.setItem(row, 12, QTableWidgetItem(product["created_at"]))
            self.table.setItem(row, 13, QTableWidgetItem(product.get("updated_at", "")))
            actions_widget = QWidget()
            actions_layout = QHBoxLayout(actions_widget)
            actions_layout.setContentsMargins(0, 0, 0, 0)
            # زر التعديل (أزرق)
            edit_btn = QPushButton()
            edit_btn.setIcon(QIcon('accounting_app/resources/icons/material/edit-173.svg'))
            edit_btn.setIconSize(QSize(22, 22))
            edit_btn.setStyleSheet("QPushButton { background: #e3f0ff; color: #1565c0; border-radius: 6px; padding: 4px; } QPushButton:focus { outline: none; }")
            edit_btn.clicked.connect(lambda _, r=row: self.edit_product(r))
            # زر الحذف (أحمر)
            del_btn = QPushButton()
            del_btn.setIcon(QIcon('accounting_app/resources/icons/material/red-trash-can-icon.svg'))
            del_btn.setIconSize(QSize(22, 22))
            del_btn.setStyleSheet("QPushButton { background: #ffebee; color: #c62828; border-radius: 6px; padding: 4px; } QPushButton:focus { outline: none; }")
            del_btn.clicked.connect(lambda _, r=row: self.delete_product(r))
            # زر التفاصيل (تقرير)
            details_btn = QPushButton()
            details_btn.setIcon(QIcon('accounting_app/resources/icons/material/report-document-file-svgrepo-com.svg'))
            details_btn.setIconSize(QSize(22, 22))
            details_btn.setStyleSheet("QPushButton { background: #f5f5f5; color: #333; border-radius: 6px; padding: 4px; } QPushButton:focus { outline: none; }")
            details_btn.clicked.connect(lambda _, r=row: self.show_details(r))
            actions_layout.addWidget(edit_btn)
            actions_layout.addWidget(del_btn)
            actions_layout.addWidget(details_btn)
            actions_widget.setLayout(actions_layout)
            self.table.setCellWidget(row, 14, actions_widget)

    def filter_table(self):
        text = self.search_box.text().strip()
        cat = self.category_filter.currentText()
        status = self.status_filter.currentText()
        for row in range(self.table.rowCount()):
            match = True
            if text:
                match &= any(text in (self.table.item(row, col).text() if self.table.item(row, col) else "") for col in [0, 1])
            if cat != self.tr("كل الفئات"):
                match &= (self.table.item(row, 2) and self.table.item(row, 2).text() == cat)
            if status != self.tr("الكل"):
                is_active = self.table.item(row, 11) and self.table.item(row, 11).text() == self.tr("نشط")
                match &= (is_active if status == self.tr("نشط") else not is_active)
            self.table.setRowHidden(row, not match)

    def add_product(self):
        dialog = ProductDialog(self)
        if dialog.exec() == QDialog.Accepted:
            data = dialog.get_data()
            now = datetime.now().strftime("%Y-%m-%d")
            self.products.append({
                "name": data["name"],
                "sku": data["sku"],
                "category": data["category"],
                "unit": data["unit"],
                "purchase_price": float(data["purchase_price"] or 0),
                "sale_price": float(data["sale_price"] or 0),
                "tax": float(data["tax"] or 0),
                "qty": int(data["qty"] or 0),
                "min_qty": int(data["min_qty"] or 0),
                "max_qty": int(data["max_qty"] or 0),
                "valuation_method": data["valuation_method"],
                "description": data["description"],
                "is_active": data["is_active"],
                "created_at": now,
                "updated_at": now,
            })
            self.refresh_table()

    def edit_product(self, row):
        product = self.products[row]
        dialog = ProductDialog(self, product)
        if dialog.exec() == QDialog.Accepted:
            data = dialog.get_data()
            now = datetime.now().strftime("%Y-%m-%d")
            self.products[row] = {
                "name": data["name"],
                "sku": data["sku"],
                "category": data["category"],
                "unit": data["unit"],
                "purchase_price": float(data["purchase_price"] or 0),
                "sale_price": float(data["sale_price"] or 0),
                "tax": float(data["tax"] or 0),
                "qty": int(data["qty"] or 0),
                "min_qty": int(data["min_qty"] or 0),
                "max_qty": int(data["max_qty"] or 0),
                "valuation_method": data["valuation_method"],
                "description": data["description"],
                "is_active": data["is_active"],
                "created_at": product["created_at"],
                "updated_at": now,
            }
            self.refresh_table()

    def delete_product(self, row):
        reply = QMessageBox.question(self, self.tr("تأكيد الحذف"), self.tr("هل أنت متأكد من حذف المنتج؟"), QMessageBox.Yes | QMessageBox.No)
        if reply == QMessageBox.Yes:
            self.products.pop(row)
            self.refresh_table()

    def delete_selected(self):
        rows = sorted(set(idx.row() for idx in self.table.selectedIndexes()), reverse=True)
        if not rows:
            QMessageBox.information(self, self.tr("تنبيه"), self.tr("لم يتم تحديد أي منتج للحذف."))
            return
        reply = QMessageBox.question(self, self.tr("تأكيد الحذف الجماعي"), self.tr("هل أنت متأكد من حذف المنتجات المحددة؟"), QMessageBox.Yes | QMessageBox.No)
        if reply == QMessageBox.Yes:
            for row in rows:
                self.products.pop(row)
            self.refresh_table()

    def show_details(self, row):
        product = self.products[row]
        details = f"""
{self.tr('اسم المنتج')}: {product['name']}
{self.tr('الباركود')}: {product['sku']}
{self.tr('الفئة')}: {product['category']}
{self.tr('الوحدة')}: {product['unit']}
{self.tr('سعر الشراء')}: {product['purchase_price']}
{self.tr('سعر البيع')}: {product['sale_price']}
{self.tr('الضريبة')}: {product['tax']}%
{self.tr('الكمية')}: {product['qty']}
{self.tr('الحد الأدنى')}: {product['min_qty']}
{self.tr('الحد الأقصى')}: {product['max_qty']}
{self.tr('طريقة التقييم')}: {product.get('valuation_method', '')}
{self.tr('الوصف')}: {product.get('description', '')}
{self.tr('الحالة')}: {self.tr('نشط') if product['is_active'] else self.tr('غير نشط')}
{self.tr('تاريخ الإنشاء')}: {product['created_at']}
{self.tr('تاريخ التحديث')}: {product.get('updated_at', '')}
"""
        QMessageBox.information(self, self.tr("تفاصيل المنتج"), details) 