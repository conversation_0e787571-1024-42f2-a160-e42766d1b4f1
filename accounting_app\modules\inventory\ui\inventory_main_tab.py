from PySide6.QtWidgets import QWidget, QVBoxLayout, QTabWidget
from accounting_app.modules.inventory.ui.product_view import ProductListTab
from accounting_app.modules.inventory.ui.stock_movements_view import StockMovementsTab
from accounting_app.modules.inventory.ui.inventory_adjustment_view import InventoryAdjustmentTab
from accounting_app.modules.inventory.ui.stock_alerts_view import StockAlertsTab
from accounting_app.modules.inventory.ui.warehouses_main_tab import WarehousesMainTab

class InventoryMainTab(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("InventoryMainTab")
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout(self)
        self.tabs = QTabWidget(self)
        self.tabs.addTab(ProductListTab(self), self.tr("المنتجات"))
        self.tabs.addTab(WarehousesMainTab(self), self.tr("إدارة المستودعات"))
        self.tabs.addTab(StockMovementsTab(self), self.tr("حركات المخزون"))
        self.tabs.addTab(InventoryAdjustmentTab(self), self.tr("الجرد"))
        self.tabs.addTab(StockAlertsTab(self), self.tr("تنبيهات المخزون"))
        layout.addWidget(self.tabs)
        self.setLayout(layout) 