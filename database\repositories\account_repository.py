from sqlalchemy.orm import Session
from database.models import Account

class AccountRepository:
    def __init__(self, db: Session):
        self.db = db

    def get_by_id(self, account_id):
        return self.db.query(Account).filter(Account.account_id == account_id).first()

    def get_by_company(self, company_id):
        return self.db.query(Account).filter(Account.company_id == company_id).all()

    def create(self, account: Account):
        self.db.add(account)
        self.db.commit()
        self.db.refresh(account)
        return account

    def update(self, account: Account):
        self.db.commit()
        self.db.refresh(account)
        return account

    def delete(self, account: Account):
        self.db.delete(account)
        self.db.commit() 