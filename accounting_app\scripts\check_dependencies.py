#!/usr/bin/env python3
"""
Dependency Checker for Smart Accounting App
فاحص التبعيات لبرنامج المحاسبة الذكي
"""

import os
import sys
import json
import subprocess
import pkg_resources
from pathlib import Path
from typing import Dict, List, Tuple, Optional

class DependencyChecker:
    """فاحص التبعيات"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.requirements_files = {
            'production': 'requirements.txt',
            'minimal': 'requirements-minimal.txt',
            'development': 'dev-requirements.txt',
            'locked': 'requirements-lock.txt'
        }
        
    def check_file_exists(self, filename: str) -> bool:
        """فحص وجود الملف"""
        file_path = self.project_root / filename
        return file_path.exists()
    
    def parse_requirements(self, filename: str) -> List[Tuple[str, str]]:
        """تحليل ملف التبعيات"""
        file_path = self.project_root / filename
        if not file_path.exists():
            return []
        
        requirements = []
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    # إزالة التعليقات في نهاية السطر
                    if '#' in line:
                        line = line.split('#')[0].strip()
                    
                    if line.startswith('-r '):
                        # تبعية مرجعية
                        ref_file = line[3:].strip()
                        requirements.extend(self.parse_requirements(ref_file))
                    elif '==' in line:
                        # إصدار محدد
                        package, version = line.split('==', 1)
                        requirements.append((package.strip(), f"=={version.strip()}"))
                    elif '>=' in line:
                        # إصدار أدنى
                        package, version = line.split('>=', 1)
                        requirements.append((package.strip(), f">={version.strip()}"))
                    elif '>' in line:
                        # إصدار أكبر من
                        package, version = line.split('>', 1)
                        requirements.append((package.strip(), f">{version.strip()}"))
                    elif '<=' in line:
                        # إصدار أقل من أو يساوي
                        package, version = line.split('<=', 1)
                        requirements.append((package.strip(), f"<={version.strip()}"))
                    elif '<' in line:
                        # إصدار أقل من
                        package, version = line.split('<', 1)
                        requirements.append((package.strip(), f"<{version.strip()}"))
                    else:
                        # إصدار غير محدد
                        requirements.append((line, ""))
        
        return requirements
    
    def get_installed_packages(self) -> Dict[str, str]:
        """الحصول على التبعيات المثبتة"""
        installed = {}
        for dist in pkg_resources.working_set:
            installed[dist.project_name] = dist.version
        return installed
    
    def check_package_installed(self, package: str) -> Optional[str]:
        """فحص تثبيت الحزمة"""
        try:
            return pkg_resources.get_distribution(package).version
        except pkg_resources.DistributionNotFound:
            return None
    
    def check_version_compatibility(self, package: str, required_version: str, installed_version: str) -> bool:
        """فحص توافق الإصدار"""
        if not required_version:
            return True
        
        try:
            if required_version.startswith('=='):
                return installed_version == required_version[2:]
            elif required_version.startswith('>='):
                return pkg_resources.parse_version(installed_version) >= pkg_resources.parse_version(required_version[2:])
            elif required_version.startswith('>'):
                return pkg_resources.parse_version(installed_version) > pkg_resources.parse_version(required_version[1:])
            elif required_version.startswith('<='):
                return pkg_resources.parse_version(installed_version) <= pkg_resources.parse_version(required_version[2:])
            elif required_version.startswith('<'):
                return pkg_resources.parse_version(installed_version) < pkg_resources.parse_version(required_version[1:])
            else:
                return True
        except Exception:
            return False
    
    def check_security_vulnerabilities(self) -> List[Dict]:
        """فحص الثغرات الأمنية"""
        vulnerabilities = []
        
        try:
            # محاولة تشغيل safety إذا كان متوفراً
            result = subprocess.run(
                ['safety', 'check', '--json'],
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.returncode == 0:
                try:
                    data = json.loads(result.stdout)
                    for vuln in data:
                        vulnerabilities.append({
                            'package': vuln.get('package', 'Unknown'),
                            'installed_version': vuln.get('installed_version', 'Unknown'),
                            'vulnerable_spec': vuln.get('vulnerable_spec', 'Unknown'),
                            'description': vuln.get('description', 'No description'),
                            'severity': vuln.get('severity', 'Unknown')
                        })
                except json.JSONDecodeError:
                    pass
        except (subprocess.TimeoutExpired, FileNotFoundError):
            pass
        
        return vulnerabilities
    
    def check_outdated_packages(self) -> List[Dict]:
        """فحص التبعيات القديمة"""
        outdated = []
        
        try:
            result = subprocess.run(
                [sys.executable, '-m', 'pip', 'list', '--outdated', '--format=json'],
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.returncode == 0:
                try:
                    data = json.loads(result.stdout)
                    for package in data:
                        outdated.append({
                            'package': package.get('name', 'Unknown'),
                            'current_version': package.get('version', 'Unknown'),
                            'latest_version': package.get('latest_version', 'Unknown'),
                            'latest_filetype': package.get('latest_filetype', 'Unknown')
                        })
                except json.JSONDecodeError:
                    pass
        except subprocess.TimeoutExpired:
            pass
        
        return outdated
    
    def run_full_check(self) -> Dict:
        """تشغيل فحص شامل"""
        print("🔍 فحص التبعيات...")
        
        results = {
            'files_exist': {},
            'requirements': {},
            'installed_packages': {},
            'missing_packages': {},
            'version_mismatches': {},
            'security_vulnerabilities': [],
            'outdated_packages': [],
            'recommendations': []
        }
        
        # فحص وجود الملفات
        print("📁 فحص ملفات التبعيات...")
        for env, filename in self.requirements_files.items():
            exists = self.check_file_exists(filename)
            results['files_exist'][env] = exists
            if not exists:
                results['recommendations'].append(f"إنشاء ملف {filename}")
        
        # فحص التبعيات المطلوبة
        print("📦 فحص التبعيات المطلوبة...")
        for env, filename in self.requirements_files.items():
            if results['files_exist'][env]:
                requirements = self.parse_requirements(filename)
                results['requirements'][env] = requirements
        
        # فحص التبعيات المثبتة
        print("🔧 فحص التبعيات المثبتة...")
        installed = self.get_installed_packages()
        results['installed_packages'] = installed
        
        # فحص التبعيات المفقودة
        print("❌ فحص التبعيات المفقودة...")
        for env, requirements in results['requirements'].items():
            missing = []
            for package, version in requirements:
                if package not in installed:
                    missing.append((package, version))
            if missing:
                results['missing_packages'][env] = missing
        
        # فحص عدم تطابق الإصدارات
        print("⚠️ فحص عدم تطابق الإصدارات...")
        for env, requirements in results['requirements'].items():
            mismatches = []
            for package, required_version in requirements:
                if package in installed:
                    installed_version = installed[package]
                    if not self.check_version_compatibility(package, required_version, installed_version):
                        mismatches.append({
                            'package': package,
                            'required': required_version,
                            'installed': installed_version
                        })
            if mismatches:
                results['version_mismatches'][env] = mismatches
        
        # فحص الثغرات الأمنية
        print("🔒 فحص الثغرات الأمنية...")
        results['security_vulnerabilities'] = self.check_security_vulnerabilities()
        
        # فحص التبعيات القديمة
        print("📅 فحص التبعيات القديمة...")
        results['outdated_packages'] = self.check_outdated_packages()
        
        # إضافة توصيات
        if results['missing_packages']:
            results['recommendations'].append("تثبيت التبعيات المفقودة")
        
        if results['version_mismatches']:
            results['recommendations'].append("تحديث الإصدارات غير المتطابقة")
        
        if results['security_vulnerabilities']:
            results['recommendations'].append("معالجة الثغرات الأمنية")
        
        if results['outdated_packages']:
            results['recommendations'].append("تحديث التبعيات القديمة")
        
        return results
    
    def print_report(self, results: Dict):
        """طباعة التقرير"""
        print("\n" + "="*60)
        print("📊 تقرير فحص التبعيات")
        print("="*60)
        
        # ملفات التبعيات
        print("\n📁 ملفات التبعيات:")
        for env, exists in results['files_exist'].items():
            status = "✅ موجود" if exists else "❌ مفقود"
            print(f"  {env}: {status}")
        
        # التبعيات المفقودة
        if results['missing_packages']:
            print("\n❌ التبعيات المفقودة:")
            for env, missing in results['missing_packages'].items():
                print(f"  {env}:")
                for package, version in missing:
                    print(f"    - {package}{version}")
        
        # عدم تطابق الإصدارات
        if results['version_mismatches']:
            print("\n⚠️ عدم تطابق الإصدارات:")
            for env, mismatches in results['version_mismatches'].items():
                print(f"  {env}:")
                for mismatch in mismatches:
                    print(f"    - {mismatch['package']}: مطلوب {mismatch['required']}, مثبت {mismatch['installed']}")
        
        # الثغرات الأمنية
        if results['security_vulnerabilities']:
            print("\n🔒 الثغرات الأمنية:")
            for vuln in results['security_vulnerabilities']:
                print(f"  - {vuln['package']} {vuln['installed_version']}: {vuln['severity']} - {vuln['description']}")
        
        # التبعيات القديمة
        if results['outdated_packages']:
            print("\n📅 التبعيات القديمة:")
            for package in results['outdated_packages'][:10]:  # عرض أول 10 فقط
                print(f"  - {package['package']}: {package['current_version']} → {package['latest_version']}")
        
        # التوصيات
        if results['recommendations']:
            print("\n💡 التوصيات:")
            for rec in results['recommendations']:
                print(f"  - {rec}")
        
        # ملخص
        total_issues = (
            len(results['missing_packages']) +
            len(results['version_mismatches']) +
            len(results['security_vulnerabilities']) +
            len(results['outdated_packages'])
        )
        
        print(f"\n📈 ملخص:")
        print(f"  - إجمالي المشاكل: {total_issues}")
        
        if total_issues == 0:
            print("  🎉 جميع التبعيات في حالة ممتازة!")
        else:
            print("  ⚠️ هناك مشاكل تحتاج إلى معالجة")

def main():
    """الدالة الرئيسية"""
    checker = DependencyChecker()
    results = checker.run_full_check()
    checker.print_report(results)
    
    # إرجاع كود الخروج
    total_issues = (
        len(results['missing_packages']) +
        len(results['version_mismatches']) +
        len(results['security_vulnerabilities'])
    )
    
    if total_issues > 0:
        sys.exit(1)
    else:
        sys.exit(0)

if __name__ == "__main__":
    main() 