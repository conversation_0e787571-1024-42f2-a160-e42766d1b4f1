from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from database.config.connection_pool import SessionLocal
from database.services.transaction_service import TransactionService

router = APIRouter(prefix="/transactions", tags=["transactions"])

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

@router.post("/")
def create_transaction(company_id: str, description: str = "", db: Session = Depends(get_db)):
    service = TransactionService(db)
    transaction = service.create_transaction(company_id, description)
    return {"transaction_id": str(transaction.transaction_id), "company_id": str(transaction.company_id), "description": transaction.description, "date": str(transaction.date)}

@router.get("/company/{company_id}")
def get_transactions_by_company(company_id: str, db: Session = Depends(get_db)):
    service = TransactionService(db)
    transactions = service.get_transactions_by_company(company_id)
    return [{"transaction_id": str(t.transaction_id), "company_id": str(t.company_id), "description": t.description, "date": str(t.date)} for t in transactions]

@router.get("/{transaction_id}")
def get_transaction(transaction_id: str, db: Session = Depends(get_db)):
    service = TransactionService(db)
    transaction = service.get_transaction(transaction_id)
    if not transaction:
        raise HTTPException(status_code=404, detail="Transaction not found")
    return {"transaction_id": str(transaction.transaction_id), "company_id": str(transaction.company_id), "description": transaction.description, "date": str(transaction.date)} 