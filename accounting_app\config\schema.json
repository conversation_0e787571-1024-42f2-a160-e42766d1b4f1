{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Smart Accounting App Configuration Schema", "description": "مخطط إعدادات برنامج المحاسبة الذكي", "type": "object", "required": ["app_info", "user_preferences", "ai_settings", "notification_settings", "database_settings", "security_settings", "ui_settings", "performance_settings"], "properties": {"app_info": {"type": "object", "title": "معلومات التطبيق", "description": "معلومات أساسية عن التطبيق", "required": ["app_name", "app_version", "app_description", "environment"], "properties": {"app_name": {"type": "string", "title": "اسم التطبيق", "description": "اسم التطبيق المعروض للمستخدم", "minLength": 1, "maxLength": 100}, "app_version": {"type": "string", "title": "إصدار التطبيق", "description": "رقم إصدار التطبيق", "pattern": "^\\d+\\.\\d+\\.\\d+$"}, "app_description": {"type": "string", "title": "وصف التطبيق", "description": "وصف مختصر للتطبيق", "minLength": 1, "maxLength": 500}, "environment": {"type": "string", "title": "بيئة التشغيل", "description": "بيئة تشغيل التطبيق", "enum": ["development", "testing", "production"]}, "base_path": {"type": "string", "title": "المسار الأساسي", "description": "المسار الأساسي للتطبيق"}, "resources_path": {"type": "string", "title": "مسا<PERSON> الموارد", "description": "م<PERSON>ا<PERSON> مج<PERSON>د الموارد", "default": "resources"}, "translations_path": {"type": "string", "title": "مسار الترجمات", "description": "مسار مجلد ملفات الترجمة", "default": "translations"}}, "additionalProperties": false}, "user_preferences": {"type": "object", "title": "تفضيلات المستخدم", "description": "إعدادات تفضيلات المستخدم", "required": ["language", "theme", "date_format", "time_format", "currency"], "properties": {"language": {"type": "string", "title": "اللغة", "description": "لغة واجهة المستخدم", "enum": ["ar", "en", "fr"], "default": "ar"}, "theme": {"type": "string", "title": "المظهر", "description": "مظهر واجهة المستخدم", "enum": ["dark", "light", "auto", "default"], "default": "default"}, "date_format": {"type": "string", "title": "تنسيق التاريخ", "description": "تنسيق عرض التاريخ", "enum": ["dd/mm/yyyy", "mm/dd/yyyy", "yyyy-mm-dd"], "default": "dd/mm/yyyy"}, "time_format": {"type": "string", "title": "تنسيق الوقت", "description": "تنسيق عرض الوقت", "enum": ["12h", "24h"], "default": "24h"}, "currency": {"type": "string", "title": "العملة", "description": "العملة الافتراضية", "enum": ["SAR", "USD", "EUR", "GBP"], "default": "SAR"}, "decimal_separator": {"type": "string", "title": "فاصل الكسور العشرية", "description": "الرمز المستخدم لفصل الكسور العشرية", "enum": [".", ","], "default": "."}, "thousands_separator": {"type": "string", "title": "فاصل الآلاف", "description": "الرمز المستخدم لفصل الآلاف", "enum": [",", ".", " "], "default": ","}}, "additionalProperties": false}, "ai_settings": {"type": "object", "title": "إعدادات الذكاء الاصطناعي", "description": "إعدادات المساعد الذكي", "required": ["enabled", "model", "max_tokens", "temperature"], "properties": {"enabled": {"type": "boolean", "title": "تفعيل المساعد الذكي", "description": "تفعيل أو إلغاء تفعيل المساعد الذكي", "default": true}, "model": {"type": "string", "title": "نموذج الذكاء الاصطناعي", "description": "نموذج الذكاء الاصطناعي المستخدم", "enum": ["gpt-3.5-turbo", "gpt-4", "local"], "default": "gpt-3.5-turbo"}, "api_key": {"type": "string", "title": "مفتاح API", "description": "مفتاح API للخدمة المستخدمة", "minLength": 0}, "max_tokens": {"type": "integer", "title": "ال<PERSON><PERSON> الأقصى للرموز", "description": "الح<PERSON> الأق<PERSON>ى لعدد الرموز في الاستجابة", "minimum": 100, "maximum": 4000, "default": 1000}, "temperature": {"type": "number", "title": "درجة الحرارة", "description": "درجة الإبداع في الاستجابة (0-2)", "minimum": 0.0, "maximum": 2.0, "default": 0.7}, "context_window": {"type": "integer", "title": "نافذة السياق", "description": "عد<PERSON> الرسائل المحفوظة في السياق", "minimum": 1, "maximum": 50, "default": 10}, "auto_suggest": {"type": "boolean", "title": "الاقتراح التلقائي", "description": "تفعيل الاقتراحات التلقائية", "default": true}}, "additionalProperties": false}, "notification_settings": {"type": "object", "title": "إعدادات الإشعارات", "description": "إعدادات نظام الإشعارات", "required": ["enabled", "sound_enabled", "desktop_notifications"], "properties": {"enabled": {"type": "boolean", "title": "تفعيل الإشعارات", "description": "تفعيل أو إلغاء تفعيل الإشعارات", "default": true}, "sound_enabled": {"type": "boolean", "title": "تفعيل الصوت", "description": "تفعيل أو إلغاء تفعيل صوت الإشعارات", "default": true}, "sound_file": {"type": "string", "title": "مل<PERSON> الصوت", "description": "مسار ملف صوت الإشعارات", "default": "notification.wav"}, "desktop_notifications": {"type": "boolean", "title": "إشعارات سطح المكتب", "description": "عرض الإشعارات على سطح المكتب", "default": true}, "notification_duration": {"type": "integer", "title": "مدة الإشعار", "description": "مدة عرض الإشعار بالثواني", "minimum": 1, "maximum": 30, "default": 5}, "notification_position": {"type": "string", "title": "موضع الإشعار", "description": "موضع عرض الإشعارات", "enum": ["top-right", "top-left", "bottom-right", "bottom-left"], "default": "top-right"}, "auto_hide": {"type": "boolean", "title": "الإخفاء التلقائي", "description": "إخفاء الإشعارات تلقائياً", "default": true}}, "additionalProperties": false}, "database_settings": {"type": "object", "title": "إعدادات قاعدة البيانات", "description": "إعدادات قاعدة البيانات", "required": ["type", "auto_backup"], "properties": {"type": {"type": "string", "title": "نوع قاعدة البيانات", "description": "نوع قاعدة البيانات المستخدمة", "enum": ["sqlite", "postgresql", "mysql"], "default": "sqlite"}, "host": {"type": "string", "title": "<PERSON><PERSON><PERSON> قاعدة البيانات", "description": "عنوان خادم قاعدة البيانات", "default": "localhost"}, "port": {"type": "integer", "title": "من<PERSON><PERSON> قاعدة البيانات", "description": "من<PERSON><PERSON> خادم قاعدة البيانات", "minimum": 1, "maximum": 65535, "default": 5432}, "database_name": {"type": "string", "title": "اسم قاعدة البيانات", "description": "اسم قاعدة البيانات", "default": "accounting_app"}, "username": {"type": "string", "title": "اسم المستخدم", "description": "اسم مستخدم قاعدة البيانات"}, "password": {"type": "string", "title": "كلمة المرور", "description": "كلمة مرور قاعدة البيانات"}, "auto_backup": {"type": "boolean", "title": "النسخ الاحتياطي التلقائي", "description": "تفعيل النسخ الاحتياطي التلقائي", "default": true}, "backup_interval": {"type": "integer", "title": "فترة النسخ الاحتياطي", "description": "فترة النسخ الاحتياطي بالأيام", "minimum": 1, "maximum": 365, "default": 7}, "max_backups": {"type": "integer", "title": "ال<PERSON><PERSON> الأقصى للنسخ الاحتياطية", "description": "الح<PERSON> الأق<PERSON>ى لعدد النسخ الاحتياطية المحفوظة", "minimum": 1, "maximum": 100, "default": 10}}, "additionalProperties": false}, "security_settings": {"type": "object", "title": "إعدادات الأمان", "description": "إعدادات أمان التطبيق", "required": ["session_timeout", "password_min_length"], "properties": {"session_timeout": {"type": "integer", "title": "مهلة الجلسة", "description": "مهلة انتهاء الجلسة بالدقائق", "minimum": 5, "maximum": 1440, "default": 30}, "password_min_length": {"type": "integer", "title": "الح<PERSON> الأدنى لطول كلمة المرور", "description": "الح<PERSON> الأدنى لطول كلمة المرور", "minimum": 6, "maximum": 50, "default": 8}, "require_special_chars": {"type": "boolean", "title": "طلب رموز خاصة", "description": "طلب رموز خاصة في كلمة المرور", "default": true}, "max_login_attempts": {"type": "integer", "title": "الحد الأقصى لمحاولات تسجيل الدخول", "description": "الحد الأقصى لمحاولات تسجيل الدخول", "minimum": 3, "maximum": 10, "default": 5}, "lockout_duration": {"type": "integer", "title": "مدة الحظر", "description": "مدة حظر الحساب بالدقائق", "minimum": 5, "maximum": 1440, "default": 15}, "encrypt_sensitive_data": {"type": "boolean", "title": "تشفير البيانات الحساسة", "description": "تشفير البيانات الحساسة في قاعدة البيانات", "default": true}}, "additionalProperties": false}, "ui_settings": {"type": "object", "title": "إعدادات واجهة المستخدم", "description": "إعدادات واجهة المستخدم", "required": ["window_size", "sidebar_width"], "properties": {"window_size": {"type": "object", "title": "حجم النافذة", "description": "حجم النافذة الرئيسية", "required": ["width", "height"], "properties": {"width": {"type": "integer", "title": "العرض", "description": "عرض النافذة بالبكسل", "minimum": 800, "maximum": 3000, "default": 1200}, "height": {"type": "integer", "title": "الارتفاع", "description": "ارتفاع النافذة بالبكسل", "minimum": 600, "maximum": 2000, "default": 800}}, "additionalProperties": false}, "sidebar_width": {"type": "integer", "title": "عرض الشريط الجانبي", "description": "عرض الشريط الجانبي بالبكسل", "minimum": 200, "maximum": 400, "default": 250}, "font_size": {"type": "integer", "title": "حج<PERSON> الخط", "description": "حجم الخط الأساسي", "minimum": 8, "maximum": 24, "default": 12}, "font_family": {"type": "string", "title": "نوع الخط", "description": "نوع الخط المستخدم", "enum": ["<PERSON><PERSON>", "Times New Roman", "Courier New", "<PERSON><PERSON><PERSON>"], "default": "<PERSON><PERSON>"}, "show_tooltips": {"type": "boolean", "title": "عرض التلميحات", "description": "عرض تلميحات الأدوات", "default": true}, "show_status_bar": {"type": "boolean", "title": "عرض شريط الحالة", "description": "عرض شريط الحالة", "default": true}, "auto_save_ui_state": {"type": "boolean", "title": "حفظ حالة الواجهة تلقائياً", "description": "حفظ حالة النوافذ والمواضع تلقائياً", "default": true}}, "additionalProperties": false}, "performance_settings": {"type": "object", "title": "إعدادات الأداء", "description": "إعدادات أداء التطبيق", "required": ["log_level", "cache_enabled"], "properties": {"log_level": {"type": "string", "title": "مستوى التسجيل", "description": "مستوى تفصيل السجلات", "enum": ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"], "default": "INFO"}, "cache_enabled": {"type": "boolean", "title": "تفعيل التخزين المؤقت", "description": "تفعيل نظام التخزين المؤقت", "default": true}, "cache_size": {"type": "integer", "title": "حجم التخزين المؤقت", "description": "حجم التخزين المؤقت بالميجابايت", "minimum": 10, "maximum": 1000, "default": 100}, "max_memory_usage": {"type": "integer", "title": "الحد الأقصى لاستخدام الذاكرة", "description": "الحد الأقصى لاستخدام الذاكرة بالميجابايت", "minimum": 100, "maximum": 8000, "default": 512}, "auto_cleanup": {"type": "boolean", "title": "التنظيف التلقائي", "description": "تنظيف الملفات المؤقتة تلقائياً", "default": true}, "cleanup_interval": {"type": "integer", "title": "فترة التنظيف", "description": "فترة التنظيف التلقائي بالأيام", "minimum": 1, "maximum": 30, "default": 7}}, "additionalProperties": false}}, "additionalProperties": false}