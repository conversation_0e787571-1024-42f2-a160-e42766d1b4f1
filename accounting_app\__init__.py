"""
Smart Accounting App - برنامج المحاسبة الذكي
A comprehensive accounting application built with Python and PySide6
"""

__version__ = "1.0.0"
__author__ = "Smart Accounting Team"
__email__ = "<EMAIL>"

# الاستيرادات الأساسية للحزمة
from accounting_app.core.app_initializer import AppInitializer
from accounting_app.core.settings import AppSettings
from accounting_app.core.event_bus import event_bus, EventBus
from accounting_app.core.events import AppEvents
from accounting_app.core.style_manager import StyleManager
from accounting_app.utils import apply_safe_listview_fix
from accounting_app.core.logger import setup_logging, get_logger
from accounting_app.core.error_handler import error_handler, handle_error

# استيراد المكونات الرئيسية
from accounting_app.ui.main_window import MainWindow
from accounting_app.controllers.language_controller import LanguageController
from accounting_app.controllers.ai_controller import AIController
from accounting_app.controllers.notification_controller import NotificationController
from accounting_app.controllers.sidebar_controller import SidebarController

# استيراد النماذج
from accounting_app.models.user import User, UserRole

# قائمة المكونات المتاحة للاستيراد
__all__ = [
    # Core components
    'AppInitializer',
    'AppSettings', 
    'event_bus',
    'EventBus',
    'AppEvents',
    'StyleManager',
    'apply_safe_listview_fix',
    'setup_logging',
    'get_logger',
    'error_handler',
    'handle_error',
    
    # UI components
    'MainWindow',
    
    # Controllers
    'LanguageController',
    'AIController', 
    'NotificationController',
    'SidebarController',
    
    # Models
    'User',
    'UserRole',
]

def get_version():
    """الحصول على إصدار التطبيق"""
    return __version__

def get_app_info():
    """الحصول على معلومات التطبيق"""
    return {
        'name': 'Smart Accounting App',
        'version': __version__,
        'author': __author__,
        'email': __email__,
        'description': 'برنامج محاسبة ذكي مبني بـ Python و PySide6'
    } 