#!/usr/bin/env python3
"""
نظام إدارة الأخطاء الشامل
Comprehensive Error Handling System
"""

import sys
import traceback
import logging
from typing import Optional, Callable, Any, Dict
from functools import wraps
from enum import Enum
from datetime import datetime
from pathlib import Path

from .logger import get_logger

class ErrorSeverity(Enum):
    """مستويات خطورة الأخطاء"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class ErrorType(Enum):
    """أنواع الأخطاء"""
    VALIDATION = "validation"
    NETWORK = "network"
    DATABASE = "database"
    UI = "ui"
    AUTHENTICATION = "authentication"
    AUTHORIZATION = "authorization"
    SYSTEM = "system"
    UNKNOWN = "unknown"

class AppError(Exception):
    """خطأ مخصص للتطبيق"""
    
    def __init__(
        self, 
        message: str, 
        error_type: ErrorType = ErrorType.UNKNOWN,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        context: Optional[Dict[str, Any]] = None,
        original_error: Optional[Exception] = None
    ):
        super().__init__(message)
        self.message = message
        self.error_type = error_type
        self.severity = severity
        self.context = context or {}
        self.original_error = original_error
        self.timestamp = datetime.now()
        self.traceback = traceback.format_exc()

class ErrorHandler:
    """مدير الأخطاء الرئيسي"""
    
    def __init__(self):
        self.logger = get_logger("ErrorHandler")
        self.error_callbacks: Dict[ErrorType, list[Callable]] = {
            error_type: [] for error_type in ErrorType
        }
        self.global_error_callbacks: list[Callable] = []
        self.error_history: list[AppError] = []
        self.max_history_size = 1000
        
        # إعداد معالج الأخطاء العام
        sys.excepthook = self.handle_uncaught_exception
    
    def register_error_callback(
        self, 
        error_type: ErrorType, 
        callback: Callable[[AppError], None]
    ):
        """تسجيل دالة معالجة لخطأ معين"""
        if error_type not in self.error_callbacks:
            self.error_callbacks[error_type] = []
        self.error_callbacks[error_type].append(callback)
    
    def register_global_callback(self, callback: Callable[[AppError], None]):
        """تسجيل دالة معالجة عامة"""
        self.global_error_callbacks.append(callback)
    
    def handle_error(
        self, 
        error: Exception, 
        error_type: ErrorType = ErrorType.UNKNOWN,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        context: Optional[Dict[str, Any]] = None
    ) -> AppError:
        """معالجة خطأ"""
        # تحويل الخطأ إلى AppError إذا لم يكن كذلك
        if not isinstance(error, AppError):
            app_error = AppError(
                str(error),
                error_type=error_type,
                severity=severity,
                context=context,
                original_error=error
            )
        else:
            app_error = error
            if context:
                app_error.context.update(context)
        
        # تسجيل الخطأ
        self._log_error(app_error)
        
        # إضافة للتاريخ
        self._add_to_history(app_error)
        
        # استدعاء معالجات الأخطاء
        self._call_error_handlers(app_error)
        
        return app_error
    
    def handle_uncaught_exception(
        self, 
        exc_type: type, 
        exc_value: Exception, 
        exc_traceback: traceback
    ):
        """معالجة الأخطاء غير الممسوكة"""
        app_error = AppError(
            str(exc_value),
            error_type=ErrorType.SYSTEM,
            severity=ErrorSeverity.CRITICAL,
            original_error=exc_value
        )
        
        self.handle_error(app_error)
        
        # طباعة الخطأ في حالة عدم وجود معالج
        if not self.global_error_callbacks:
            print(f"خطأ غير ممسوك: {exc_value}")
            traceback.print_exception(exc_type, exc_value, exc_traceback)
    
    def _log_error(self, error: AppError):
        """تسجيل الخطأ"""
        log_message = f"خطأ {error.error_type.value}: {error.message}"
        
        if error.severity == ErrorSeverity.CRITICAL:
            self.logger.critical(log_message, exc_info=True)
        elif error.severity == ErrorSeverity.HIGH:
            self.logger.error(log_message, exc_info=True)
        elif error.severity == ErrorSeverity.MEDIUM:
            self.logger.warning(log_message)
        else:
            self.logger.info(log_message)
        
        # تسجيل السياق إذا كان موجوداً
        if error.context:
            self.logger.debug(f"سياق الخطأ: {error.context}")
    
    def _add_to_history(self, error: AppError):
        """إضافة الخطأ للتاريخ"""
        self.error_history.append(error)
        
        # إزالة الأخطاء القديمة إذا تجاوز العدد الحد الأقصى
        if len(self.error_history) > self.max_history_size:
            self.error_history.pop(0)
    
    def _call_error_handlers(self, error: AppError):
        """استدعاء معالجات الأخطاء"""
        # استدعاء المعالجات العامة
        for callback in self.global_error_callbacks:
            try:
                callback(error)
            except Exception as e:
                self.logger.error(f"خطأ في معالج الأخطاء العام: {e}")
        
        # استدعاء معالجات النوع المحدد
        if error.error_type in self.error_callbacks:
            for callback in self.error_callbacks[error.error_type]:
                try:
                    callback(error)
                except Exception as e:
                    self.logger.error(f"خطأ في معالج الأخطاء المحدد: {e}")
    
    def get_error_history(
        self, 
        error_type: Optional[ErrorType] = None,
        severity: Optional[ErrorSeverity] = None,
        limit: Optional[int] = None
    ) -> list[AppError]:
        """الحصول على تاريخ الأخطاء"""
        filtered_errors = self.error_history
        
        if error_type:
            filtered_errors = [e for e in filtered_errors if e.error_type == error_type]
        
        if severity:
            filtered_errors = [e for e in filtered_errors if e.severity == severity]
        
        if limit:
            filtered_errors = filtered_errors[-limit:]
        
        return filtered_errors
    
    def clear_error_history(self):
        """مسح تاريخ الأخطاء"""
        self.error_history.clear()
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الأخطاء"""
        stats = {
            'total_errors': len(self.error_history),
            'by_type': {},
            'by_severity': {},
            'recent_errors': len([e for e in self.error_history 
                                if (datetime.now() - e.timestamp).seconds < 3600])
        }
        
        # إحصائيات حسب النوع
        for error_type in ErrorType:
            stats['by_type'][error_type.value] = len([
                e for e in self.error_history if e.error_type == error_type
            ])
        
        # إحصائيات حسب الخطورة
        for severity in ErrorSeverity:
            stats['by_severity'][severity.value] = len([
                e for e in self.error_history if e.severity == severity
            ])
        
        return stats

# إنشاء مثيل عام لمدير الأخطاء
error_handler = ErrorHandler()

def handle_error(
    error: Exception, 
    error_type: ErrorType = ErrorType.UNKNOWN,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    context: Optional[Dict[str, Any]] = None
) -> AppError:
    """دالة مساعدة لمعالجة الأخطاء"""
    return error_handler.handle_error(error, error_type, severity, context)

def error_handler_decorator(
    error_type: ErrorType = ErrorType.UNKNOWN,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM
):
    """مُزين لمعالجة الأخطاء في الدوال"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                handle_error(e, error_type, severity, {
                    'function': func.__name__,
                    'args': args,
                    'kwargs': kwargs
                })
                raise
        return wrapper
    return decorator

def safe_execute(
    func: Callable, 
    *args, 
    error_type: ErrorType = ErrorType.UNKNOWN,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    default_return: Any = None,
    **kwargs
) -> Any:
    """تنفيذ دالة بأمان مع معالجة الأخطاء"""
    try:
        return func(*args, **kwargs)
    except Exception as e:
        handle_error(e, error_type, severity, {
            'function': func.__name__,
            'args': args,
            'kwargs': kwargs
        })
        return default_return

# دوال مساعدة لإنشاء أخطاء محددة
def create_validation_error(message: str, field: str = None) -> AppError:
    """إنشاء خطأ تحقق"""
    context = {'field': field} if field else {}
    return AppError(message, ErrorType.VALIDATION, ErrorSeverity.LOW, context)

def create_network_error(message: str, url: str = None) -> AppError:
    """إنشاء خطأ شبكة"""
    context = {'url': url} if url else {}
    return AppError(message, ErrorType.NETWORK, ErrorSeverity.MEDIUM, context)

def create_database_error(message: str, query: str = None) -> AppError:
    """إنشاء خطأ قاعدة بيانات"""
    context = {'query': query} if query else {}
    return AppError(message, ErrorType.DATABASE, ErrorSeverity.HIGH, context)

def create_ui_error(message: str, component: str = None) -> AppError:
    """إنشاء خطأ واجهة المستخدم"""
    context = {'component': component} if component else {}
    return AppError(message, ErrorType.UI, ErrorSeverity.MEDIUM, context)

def create_auth_error(message: str, user: str = None) -> AppError:
    """إنشاء خطأ مصادقة"""
    context = {'user': user} if user else {}
    return AppError(message, ErrorType.AUTHENTICATION, ErrorSeverity.HIGH, context)

def create_system_error(message: str, component: str = None) -> AppError:
    """إنشاء خطأ نظام"""
    context = {'component': component} if component else {}
    return AppError(message, ErrorType.SYSTEM, ErrorSeverity.CRITICAL, context) 