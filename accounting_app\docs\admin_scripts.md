# الأدوات الإدارية (<PERSON><PERSON> Scripts)

هذا الملف يشرح كيفية استخدام السكريبتات الإدارية لإدارة المستخدمين وكلمات المرور في النظام.

---

## 1. توليد Hash لكلمة مرور (hash_password.py)

يستخدم لتوليد hash آمن لكلمة مرور لاستخدامه في قاعدة البيانات أو التهيئة اليدوية.

**طريقة الاستخدام:**
```bash
python scripts/hash_password.py
```
سيطلب منك إدخال كلمة المرور (input مخفي)، ثم يطبع الـ hash الناتج.

---

## 2. إدارة المستخدمين (manage_user.py)

أداة لإضافة، تحديث كلمة مرور، أو حذف مستخدم من قاعدة البيانات مباشرة.

### إضافة مستخدم جديد:
```bash
python scripts/manage_user.py add --username USERNAME --role ROLE
```
- سيطلب كلمة المرور بشكل آمن.
- `--role` اختياري (افتراضي accountant)، يمكن أن يكون admin أو accountant.

### تحديث كلمة مرور مستخدم:
```bash
python scripts/manage_user.py update-password --username USERNAME
```
- سيطلب كلمة المرور الجديدة.

### حذف مستخدم:
```bash
python scripts/manage_user.py delete --username USERNAME
```

---

## 3. تهيئة بيانات تجريبية (seed_demo_data.py)

يستخدم لإضافة بيانات تجريبية تلقائيًا لقاعدة البيانات (مستخدمين، شركة، حسابات، عملية محاسبية) لتسهيل الاختبار أو العرض.

**طريقة الاستخدام:**
```bash
python scripts/seed_demo_data.py
```
- ينشئ مستخدم admin (admin/admin123) وaccountant (accountant/acc123) إذا لم يكونا موجودين.
- ينشئ شركة تجريبية وحسابات أساسية وقيود محاسبية تجريبية.

**متى يُستخدم؟**
- عند إعداد قاعدة بيانات جديدة لأول مرة.
- عند الحاجة لبيانات عرض أو اختبار سريع.

---

## المتطلبات
- يجب تشغيل السكريبتات من جذر المشروع.
- يجب أن تكون متغيرات البيئة (قاعدة البيانات) مضبوطة بشكل صحيح.
- يجب تثبيت الحزم المطلوبة (`pip install -r requirements.txt`).

---

**ملاحظة:** هذه الأدوات مخصصة للإدارة فقط ويجب استخدامها بحذر على بيئة الإنتاج. 