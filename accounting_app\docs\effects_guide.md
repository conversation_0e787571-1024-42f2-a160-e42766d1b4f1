# دليل استخدام التأثيرات البصرية

## نظرة عامة

تم إنشاء نظام تأثيرات بصرية احترافي باستخدام `QGraphicsDropShadowEffect` بدلاً من `box-shadow` في CSS، لأن Qt لا يدعم خاصية `box-shadow` القياسية.

## الملف الرئيسي: `ui/effects.py`

### الفئة الرئيسية: `ShadowEffects`

#### الطرق المتاحة:

1. **`apply_button_shadow(widget, blur_radius=8, x_offset=0, y_offset=2, color=None)`**
   - تطبيق ظل على الأزرار
   - `blur_radius`: نصف قطر التمويه (افتراضي: 8)
   - `x_offset`: الإزاحة الأفقية (افتراضي: 0)
   - `y_offset`: الإزاحة الرأسية (افتراضي: 2)
   - `color`: لون الظل (افتراضي: أسود شفاف 60%)

2. **`apply_card_shadow(widget, blur_radius=12, x_offset=0, y_offset=4, color=None)`**
   - تطبيق ظل على البطاقات والعناصر الكبيرة
   - إعدادات أكثر وضوحاً للعناصر الكبيرة

3. **`apply_toast_shadow(widget, blur_radius=16, x_offset=0, y_offset=6, color=None)`**
   - تطبيق ظل على رسائل Toast
   - ظل أكثر وضوحاً للرسائل المهمة

4. **`apply_dialog_shadow(widget, blur_radius=20, x_offset=0, y_offset=8, color=None)`**
   - تطبيق ظل على النوافذ المنبثقة
   - ظل قوي للعناصر المهمة

5. **`remove_shadow(widget)`**
   - إزالة تأثير الظل من العنصر

6. **`apply_hover_effect(widget, normal_shadow=None, hover_shadow=None)`**
   - تطبيق تأثير hover مع تغيير الظل
   - يتطلب إعداد `enterEvent` و `leaveEvent`

## أمثلة الاستخدام

### تطبيق ظل بسيط على زر:
```python
from ui.effects import ShadowEffects

button = QPushButton("زر")
ShadowEffects.apply_button_shadow(button)
```

### تطبيق ظل مخصص:
```python
from PySide6.QtGui import QColor

# ظل أزرق شفاف
blue_shadow = QColor(0, 0, 255, 80)
ShadowEffects.apply_button_shadow(button, blur_radius=10, color=blue_shadow)
```

### تطبيق تأثير hover:
```python
# ظل عادي
normal_shadow = ShadowEffects.apply_button_shadow(button)

# ظل عند hover (أكثر وضوحاً)
hover_shadow = ShadowEffects.apply_button_shadow(button, blur_radius=12, y_offset=4)

# تطبيق تأثير hover
ShadowEffects.apply_hover_effect(button, normal_shadow, hover_shadow)
```

## الملفات المحدثة

تم تحديث الملفات التالية لاستخدام التأثيرات الجديدة:

1. **`ui/toast.py`** - رسائل Toast
2. **`ui/topbar.py`** - أزرار الشريط العلوي
3. **`ui/sidebar.py`** - أزرار الشريط الجانبي
4. **`ui/content_area.py`** - أزرار التبويبات
5. **`ui/login_dialog.py`** - نافذة تسجيل الدخول
6. **`ui/ai_assistant.py`** - أزرار مساعد الذكاء الاصطناعي

## ملفات CSS المحدثة

تم إزالة `box-shadow` من:
- `ui/styles_dark.qss`
- `ui/styles_light.qss`
- `ui/styles.qss`

## المزايا

1. **دعم كامل من Qt** - لا توجد تحذيرات CSS
2. **أداء أفضل** - تأثيرات محسنة
3. **مرونة عالية** - تخصيص سهل للألوان والأحجام
4. **قابلية إعادة الاستخدام** - فئة واحدة لجميع التأثيرات
5. **سهولة الصيانة** - كود منظم ومركزي

## نصائح للاستخدام

1. **استخدم التأثير المناسب** - لا تستخدم `dialog_shadow` للأزرار الصغيرة
2. **احترم التسلسل الهرمي** - العناصر المهمة تحتاج ظلالاً أكثر وضوحاً
3. **تجنب الإفراط** - الكثير من الظلال قد يشتت الانتباه
4. **اختبر على خلفيات مختلفة** - تأكد من وضوح الظلال
5. **استخدم ألوان مخصصة** - للعلامات التجارية أو التمييز البصري 