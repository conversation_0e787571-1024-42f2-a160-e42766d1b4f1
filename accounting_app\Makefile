# Makefile for Smart Accounting App
# ملف Makefile لبرنامج المحاسبة الذكي

.PHONY: help install install-dev install-prod test lint format clean run build docs

# Default target
help:
	@echo "Smart Accounting App - Available Commands:"
	@echo ""
	@echo "Installation:"
	@echo "  install      - Install production dependencies"
	@echo "  install-dev  - Install development dependencies"
	@echo "  install-prod - Install minimal production dependencies"
	@echo ""
	@echo "Development:"
	@echo "  run          - Run the application"
	@echo "  test         - Run tests"
	@echo "  lint         - Run linting checks"
	@echo "  format       - Format code with black and isort"
	@echo "  type-check   - Run type checking with mypy"
	@echo ""
	@echo "Build & Deploy:"
	@echo "  build        - Build the application"
	@echo "  clean        - Clean build artifacts"
	@echo "  docs         - Build documentation"
	@echo ""
	@echo "Translation:"
	@echo "  convert-ts   - Convert .ts files to .qm"
	@echo "  test-trans   - Test translation system"
	@echo ""
	@echo "Dependencies:"
	@echo "  check-deps   - Check dependencies health"
	@echo "  update-deps  - Update dependencies"
	@echo "  lock-deps    - Lock current dependencies"
	@echo ""

# Installation targets
install:
	pip install -r requirements.txt

install-dev:
	pip install -r dev-requirements.txt

install-prod:
	pip install -r requirements-minimal.txt

# Development targets
run:
	python -m accounting_app.main

test:
	pytest tests/ -v --cov=accounting_app --cov-report=html

lint:
	flake8 accounting_app/ --max-line-length=88 --extend-ignore=E203,W503
	black --check accounting_app/
	isort --check-only accounting_app/

format:
	black accounting_app/
	isort accounting_app/

type-check:
	mypy accounting_app/ --ignore-missing-imports

# Build targets
build:
	python setup.py build

clean:
	rm -rf build/
	rm -rf dist/
	rm -rf *.egg-info/
	rm -rf htmlcov/
	rm -rf .coverage
	find . -type d -name __pycache__ -delete
	find . -type f -name "*.pyc" -delete

docs:
	cd docs && make html

# Translation targets
convert-ts:
	python scripts/convert_ts_to_qm.py

test-trans:
	python scripts/test_translation.py

# Testing targets
test-core:
	pytest tests/test_core.py -v

test-controllers:
	pytest tests/test_controllers.py -v

test-all:
	pytest tests/ -v --cov=accounting_app --cov-report=html

test-coverage:
	pytest tests/ --cov=accounting_app --cov-report=html --cov-report=term

# Configuration targets
init-config:
	python -c "from accounting_app.config.app_config import save_config; save_config()"

validate-config:
	python -c "from accounting_app.config.app_config import get_config; print('Config valid:', get_config().validate())"

# Error handling targets
error-stats:
	python -c "from accounting_app import error_handler; print('Error statistics:', error_handler.get_error_statistics())"

clear-errors:
	python -c "from accounting_app import error_handler; error_handler.clear_error_history(); print('Error history cleared')"

# Dependency management
check-deps:
	python scripts/check_dependencies.py

update-deps:
	pip install --upgrade -r requirements.txt

lock-deps:
	pip freeze > requirements-lock.txt

# Security and quality
security-check:
	bandit -r accounting_app/ -f json -o bandit-report.json

# Pre-commit setup
setup-pre-commit:
	pre-commit install
	pre-commit install --hook-type commit-msg

# Environment setup
setup-env:
	python -m venv .venv
	@echo "Virtual environment created. Activate it with:"
	@echo "  source .venv/bin/activate  # Linux/Mac"
	@echo "  .venv\\Scripts\\activate     # Windows"

# Full development setup
setup-dev: setup-env
	@echo "Installing development dependencies..."
	pip install -r dev-requirements.txt
	pre-commit install
	@echo "Development environment setup complete!"

# Production setup
setup-prod: setup-env
	@echo "Installing production dependencies..."
	pip install -r requirements-minimal.txt
	@echo "Production environment setup complete!"

# Quick start for new developers
quick-start: setup-dev
	@echo "Converting translation files..."
	python scripts/convert_ts_to_qm.py
	@echo "Running tests..."
	pytest tests/ -v
	@echo "Starting application..."
	python -m accounting_app.main

# Package installation
install-package:
	pip install -e .

# Package uninstall
uninstall-package:
	pip uninstall smart-accounting-app -y

# Run with package
run-package:
	python -m accounting_app.main

# Run with console script (if installed)
run-script:
	smart-accounting 