CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- جدول المستخدمين
CREATE TABLE IF NOT EXISTS app_user (
    user_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VA<PERSON>HA<PERSON>(50) UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    role VARCHAR(20) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- جدول الشركات
CREATE TABLE IF NOT EXISTS company (
    company_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    owner_id UUID REFERENCES app_user(user_id),
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- جدول الحسابات
CREATE TABLE IF NOT EXISTS account (
    account_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID REFERENCES company(company_id),
    name VARCHAR(100) NOT NULL,
    type VARCHAR(50) NOT NULL,
    balance DECIMAL(18,2) DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- جدول العمليات المحاسبية
CREATE TABLE IF NOT EXISTS transaction (
    transaction_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID REFERENCES company(company_id),
    description TEXT,
    date TIMESTAMPTZ DEFAULT NOW()
);

-- جدول تفاصيل القيود
CREATE TABLE IF NOT EXISTS transaction_entry (
    entry_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    transaction_id UUID REFERENCES transaction(transaction_id),
    account_id UUID REFERENCES account(account_id),
    amount DECIMAL(18,2) NOT NULL,
    is_debit BOOLEAN NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);
