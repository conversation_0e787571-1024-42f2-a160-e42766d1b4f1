from PySide6.QtWidgets import QWidget, QVBoxLayout, QTabWidget, QLabel
from PySide6.QtCore import Qt

class PurchasesMainTab(QWidget):
    """التبويب الرئيسي لقسم المشتريات"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("PurchasesMainTab")
        self.init_ui()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)
        self.tabs = QTabWidget(self)

        # إضافة تبويبات بسيطة للاختبار
        # تبويب أوامر الشراء
        orders_tab = QWidget()
        orders_layout = QVBoxLayout(orders_tab)
        orders_layout.addWidget(QLabel("أوامر الشراء - قيد التطوير"))
        self.tabs.addTab(orders_tab, "أوامر الشراء")

        # تبويب فواتير الشراء
        invoices_tab = QWidget()
        invoices_layout = QVBoxLayout(invoices_tab)
        invoices_layout.addWidget(QLabel("فواتير الشراء - قيد التطوير"))
        self.tabs.addTab(invoices_tab, "فواتير الشراء")

        # تبويب مرتجعات الشراء
        returns_tab = QWidget()
        returns_layout = QVBoxLayout(returns_tab)
        returns_layout.addWidget(QLabel("مرتجعات الشراء - قيد التطوير"))
        self.tabs.addTab(returns_tab, "مرتجعات الشراء")

        # تبويب إدارة الموردين
        suppliers_tab = QWidget()
        suppliers_layout = QVBoxLayout(suppliers_tab)
        suppliers_layout.addWidget(QLabel("إدارة الموردين - قيد التطوير"))
        self.tabs.addTab(suppliers_tab, "إدارة الموردين")

        # تبويب التقارير
        reports_tab = QWidget()
        reports_layout = QVBoxLayout(reports_tab)
        reports_layout.addWidget(QLabel("تقارير المشتريات - قيد التطوير"))
        self.tabs.addTab(reports_tab, "تقارير المشتريات")

        layout.addWidget(self.tabs)
        self.setLayout(layout)