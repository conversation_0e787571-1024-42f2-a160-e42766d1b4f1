import logging
from PySide6.QtWidgets import QApplication, QWidget, QLayout
import os

logger = logging.getLogger(__name__)

# استيراد الدوال من utils
from accounting_app.utils import get_resource_path, safe_update, apply_safe_listview_fix

class StyleManager:
    """إدارة أنماط التطبيق"""
    
    def __init__(self, app: QApplication, theme: str = "default"):
        self.app = app
        self.theme = theme.lower()

    def apply_style(self):
        """تطبيق النمط المحدد"""
        print(f"🎨 [DEBUG] Applying style for theme: {self.theme}")
        
        base_path = os.path.join(os.path.dirname(__file__), "..", "ui")
        default_qss = os.path.join(base_path, "styles.qss")
        theme_qss = None
        if self.theme == "dark":
            theme_qss = os.path.join(base_path, "styles_dark.qss")
        elif self.theme == "light":
            theme_qss = os.path.join(base_path, "styles_light.qss")
        # 1. حمل الأنماط الافتراضية دائماً
        style = ""
        if os.path.exists(default_qss):
            try:
                with open(default_qss, "r", encoding="utf-8") as f:
                    style = f.read()
                print(f"✅ [DEBUG] Loaded default style file: {default_qss}")
            except Exception as e:
                logger.error(f"Failed to load default style file: {e}")
                print(f"❌ [DEBUG] Failed to load default style file: {e}")
        # 2. إذا كان هناك ثيم إضافي، أضفه فوق الافتراضي
        if theme_qss and os.path.exists(theme_qss):
            try:
                with open(theme_qss, "r", encoding="utf-8") as f:
                    style += "\n" + f.read()
                print(f"✅ [DEBUG] Loaded theme style file: {theme_qss}")
            except Exception as e:
                logger.error(f"Failed to load theme style file: {e}")
                print(f"❌ [DEBUG] Failed to load theme style file: {e}")
        else:
            print(f"ℹ️ [DEBUG] No theme file for '{self.theme}', using default only")
            
        self.app.setStyleSheet(style)
        print(f"🎨 [DEBUG] Style applied: default + {self.theme if self.theme != 'default' else 'only default'}")
        logger.info(f"Style applied: default + {self.theme if self.theme != 'default' else 'only default'}")
        
        # تطبيق الحل الجذري على جميع عناصر QListView الموجودة
        apply_safe_listview_fix() 