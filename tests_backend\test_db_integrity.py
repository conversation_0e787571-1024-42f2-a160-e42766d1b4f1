import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from database.models import Base, AppUser, Company, Account, Transaction, TransactionEntry, AccountType
import uuid

def get_test_session():
    engine = create_engine('sqlite:///:memory:', echo=False)
    Base.metadata.create_all(engine)
    Session = sessionmaker(bind=engine)
    return Session()

def test_cascade_delete_company():
    session = get_test_session()
    # إضافة مستخدم
    user = AppUser(user_id=uuid.uuid4(), username='testuser', password_hash='x', role='admin')
    session.add(user)
    session.commit()
    # إضافة شركة مرتبطة بالمستخدم
    company = Company(company_id=uuid.uuid4(), name='Test Co', owner_id=user.user_id)
    session.add(company)
    session.commit()
    # إضافة حساب ومعاملة للشركة
    account = Account(account_id=uuid.uuid4(), company_id=company.company_id, name='Cash', type=AccountType.ASSET, balance=1000)
    session.add(account)
    transaction = Transaction(transaction_id=uuid.uuid4(), company_id=company.company_id, description='Init')
    session.add(transaction)
    session.commit()
    # إضافة قيد محاسبي
    entry = TransactionEntry(entry_id=uuid.uuid4(), transaction_id=transaction.transaction_id, account_id=account.account_id, amount=1000, is_debit=True)
    session.add(entry)
    session.commit()
    # تحقق من وجود جميع البيانات
    assert session.query(Company).count() == 1
    assert session.query(Account).count() == 1
    assert session.query(Transaction).count() == 1
    assert session.query(TransactionEntry).count() == 1
    # حذف الشركة
    session.delete(company)
    session.commit()
    # تحقق من حذف جميع البيانات المرتبطة
    assert session.query(Company).count() == 0
    assert session.query(Account).count() == 0
    assert session.query(Transaction).count() == 0
    assert session.query(TransactionEntry).count() == 0
    session.close() 