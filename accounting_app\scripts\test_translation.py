#!/usr/bin/env python3
"""
سكريبت لاختبار نظام الترجمة
يتحقق من وجود ملفات الترجمة ويختبر تحميلها
"""

import sys
import os
from pathlib import Path
import logging

# إضافة مجلد المشروع إلى مسار Python
PROJECT_ROOT = Path(__file__).parent.parent
sys.path.insert(0, str(PROJECT_ROOT))

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTranslator, QLocale
from controllers.language_controller import LanguageController
from accounting_app.core.event_bus import event_bus
from accounting_app.core.settings import AppSettings

def setup_logging():
    """إعداد نظام التسجيل"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def test_translation_files():
    """اختبار وجود ملفات الترجمة"""
    print("🔍 اختبار وجود ملفات الترجمة...")
    
    translation_dir = PROJECT_ROOT / "translations"
    required_files = [
        "ar.ts", "en.ts", "fr.ts",
        "ar.qm", "en.qm", "fr.qm"
    ]
    
    missing_files = []
    existing_files = []
    
    for file_name in required_files:
        file_path = translation_dir / file_name
        if file_path.exists():
            existing_files.append(file_name)
            print(f"  ✅ {file_name}")
        else:
            missing_files.append(file_name)
            print(f"  ❌ {file_name} - مفقود")
    
    print(f"\n📊 النتائج:")
    print(f"  الملفات الموجودة: {len(existing_files)}/{len(required_files)}")
    print(f"  الملفات المفقودة: {len(missing_files)}")
    
    if missing_files:
        print(f"\n⚠️  الملفات المفقودة: {', '.join(missing_files)}")
        print("   قم بتشغيل: python scripts/update_translations.py")
        return False
    
    return True

def test_translator_loading():
    """اختبار تحميل المترجم"""
    print("\n🔧 اختبار تحميل المترجم...")
    
    app = QApplication.instance()
    if app is None:
        app = QApplication([])
    
    translator = QTranslator()
    
    # اختبار تحميل كل لغة
    languages = ["ar", "en", "fr"]
    
    for lang in languages:
        qm_file = f"translations/{lang}.qm"
        qm_path = PROJECT_ROOT / qm_file
        
        if not qm_path.exists():
            print(f"  ❌ {lang}: ملف {qm_file} غير موجود")
            continue
        
        if translator.load(str(qm_path)):
            print(f"  ✅ {lang}: تم تحميل {qm_file} بنجاح")
        else:
            print(f"  ❌ {lang}: فشل في تحميل {qm_file}")
    
    return True

def test_language_controller():
    """اختبار LanguageController"""
    print("\n🎛️  اختبار LanguageController...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        event_bus = event_bus
        settings = AppSettings()
        
        controller = LanguageController(app, event_bus, settings)
        
        print(f"  ✅ تم إنشاء LanguageController بنجاح")
        print(f"  اللغة الافتراضية: {controller.get_current_language()}")
        print(f"  اللغات المدعومة: {controller.get_supported_languages()}")
        
        # اختبار تغيير اللغة
        for lang in ["en", "fr", "ar"]:
            print(f"  🔄 اختبار تغيير اللغة إلى {lang}...")
            controller.set_language(lang)
            current_lang = controller.get_current_language()
            print(f"    اللغة الحالية: {current_lang}")
            
            if current_lang == lang:
                print(f"    ✅ تم تغيير اللغة إلى {lang} بنجاح")
            else:
                print(f"    ❌ فشل في تغيير اللغة إلى {lang}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ خطأ في LanguageController: {e}")
        return False

def test_translation_functionality():
    """اختبار وظائف الترجمة"""
    print("\n🌐 اختبار وظائف الترجمة...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        event_bus = event_bus
        settings = AppSettings()
        controller = LanguageController(app, event_bus, settings)
        
        # اختبار بعض النصوص
        test_texts = [
            "حفظ",
            "إلغاء", 
            "الإعدادات",
            "تسجيل الدخول"
        ]
        
        for lang in ["ar", "en", "fr"]:
            print(f"  🌍 اختبار اللغة: {lang}")
            controller.set_language(lang)
            
            for text in test_texts:
                # محاكاة ترجمة النص
                translated = app.translate("TestContext", text)
                if translated and translated != text:
                    print(f"    ✅ '{text}' → '{translated}'")
                else:
                    print(f"    ⚠️  '{text}' → (غير مترجم)")
        
        return True
        
    except Exception as e:
        print(f"  ❌ خطأ في اختبار الترجمة: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    setup_logging()
    print("🚀 بدء اختبار نظام الترجمة\n")
    
    tests = [
        ("ملفات الترجمة", test_translation_files),
        ("تحميل المترجم", test_translator_loading),
        ("LanguageController", test_language_controller),
        ("وظائف الترجمة", test_translation_functionality)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"📋 اختبار: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"  ❌ خطأ في الاختبار: {e}")
            results.append((test_name, False))
        print()
    
    # ملخص النتائج
    print("📊 ملخص النتائج:")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print("=" * 50)
    print(f"النتيجة النهائية: {passed}/{total} اختبارات نجحت")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! نظام الترجمة يعمل بشكل صحيح.")
        return True
    else:
        print("⚠️  بعض الاختبارات فشلت. راجع الأخطاء أعلاه.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 