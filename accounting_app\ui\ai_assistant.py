from PySide6.QtWidgets import QDockWidget, QWidget, QVBoxLayout, QHBoxLayout, QTextEdit, QLineEdit, QPushButton, QMenu
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QIcon, QAction
from .effects import ShadowEffects
from accounting_app.core.style_manager import get_resource_path

class AIAssistantDock(QDockWidget):
    """مساعد الذكاء الاصطناعي"""
    
    def __init__(self, parent=None):
        super().__init__(self.tr("مساعد الذكاء الصناعي"), parent)
        self.setObjectName("AIAssistantDock")
        self.setAllowedAreas(Qt.RightDockWidgetArea | Qt.LeftDockWidgetArea)
        self.setFeatures(QDockWidget.DockWidgetClosable)
        self.init_ui()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        main_widget = QWidget()
        layout = QVBoxLayout()
        
        # منطقة المحادثة
        self.chat_display = QTextEdit()
        self.chat_display.setReadOnly(True)
        
        # منطقة الإدخال
        input_layout = QHBoxLayout()
        self.input_line = QLineEdit()
        self.input_line.setPlaceholderText(self.tr("اكتب سؤالك..."))
        
        # أزرار التحكم
        self.send_btn = QPushButton(self.tr("إرسال"))
        self.send_btn.clicked.connect(self.handle_send)
        
        self.mic_btn = QPushButton()
        self.mic_btn.setFixedSize(36, 32)
        self.mic_btn.setCursor(Qt.PointingHandCursor)
        
        # زر الدعم
        self.support_btn = QPushButton(self.tr("الدعم"))
        self.support_btn.setFixedSize(80, 32)
        self.support_btn.setCursor(Qt.PointingHandCursor)
        
        # تحميل الأيقونات مع معالجة الأخطاء
        try:
            self.mic_btn.setIcon(QIcon(get_resource_path("mic.svg")))
            self.support_btn.setIcon(QIcon(get_resource_path("support.svg")))
        except Exception as e:
            import logging
            logging.warning(f"Failed to load AI assistant icons: {e}")
        
        # ربط الأحداث
        self.mic_btn.clicked.connect(self.handle_voice)
        self.setup_support_menu()
        
        # تجميع عناصر الإدخال
        input_layout.addWidget(self.input_line)
        input_layout.addWidget(self.send_btn)
        input_layout.addWidget(self.mic_btn)
        input_layout.addWidget(self.support_btn)
        
        # تجميع التخطيط الرئيسي
        layout.addWidget(self.chat_display)
        layout.addLayout(input_layout)
        main_widget.setLayout(layout)
        self.setWidget(main_widget)
        
        # تطبيق تأثيرات الظل
        ShadowEffects.apply_button_shadow(self.send_btn)
        ShadowEffects.apply_button_shadow(self.mic_btn)
        ShadowEffects.apply_button_shadow(self.support_btn)

    def setup_support_menu(self):
        """إعداد قائمة الدعم"""
        support_menu = QMenu()
        
        action_whatsapp = QAction(self.tr("واتساب الدعم"), self)
        action_email = QAction(self.tr("بريد الدعم"), self)
        action_faq = QAction(self.tr("الأسئلة الشائعة"), self)
        
        support_menu.addAction(action_whatsapp)
        support_menu.addAction(action_email)
        support_menu.addAction(action_faq)
        
        self.support_btn.setMenu(support_menu)

    def handle_send(self):
        """معالجة إرسال الرسالة"""
        text = self.input_line.text().strip()
        if text:
            self.add_message(text, "user")
            self.input_line.clear()
            
            # البحث عن AIController في النافذة الرئيسية
            main_window = self.parent()
            if hasattr(main_window, 'ai_controller'):
                main_window.ai_controller.process_message(text)
            else:
                self.add_message("(سيتم الرد الذكي هنا)", "assistant")

    def handle_voice(self):
        """معالجة الأوامر الصوتية"""
        self.add_message("[🎤] (سيتم دعم الأوامر الصوتية قريباً)", "assistant")

    def add_message(self, message: str, sender: str = "assistant"):
        """إضافة رسالة للعرض"""
        if sender == "assistant":
            self.chat_display.append(self.tr("المساعد: {}").format(message))
        else:
            self.chat_display.append(self.tr("أنت: {}").format(message))
        
        # التمرير إلى أسفل
        scrollbar = self.chat_display.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

    def reload_texts(self, lang_code=None):
        """إعادة تحميل النصوص عند تغيير اللغة"""
        try:
            # تحديث عنوان النافذة
            self.setWindowTitle(self.tr("مساعد الذكاء الصناعي"))
            
            # تحديث النصوص
            self.input_line.setPlaceholderText(self.tr("اكتب سؤالك..."))
            self.send_btn.setText(self.tr("إرسال"))
            self.support_btn.setText(self.tr("الدعم"))
            
            # إعادة إعداد قائمة الدعم
            self.setup_support_menu()
            
            import logging
            logging.info(f"AIAssistantDock texts reloaded for language: {lang_code or 'current'}")
        except Exception as e:
            import logging
            logging.exception("فشل تحديث نصوص مساعد الذكاء الاصطناعي") 