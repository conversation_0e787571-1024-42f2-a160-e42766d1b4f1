from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from database.config.connection_pool import SessionLocal
from database.services.account_service import AccountService

router = APIRouter(prefix="/accounts", tags=["accounts"])

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

@router.post("/")
def create_account(company_id: str, name: str, type: str, balance: float = 0, db: Session = Depends(get_db)):
    service = AccountService(db)
    account = service.create_account(company_id, name, type, balance)
    return {"account_id": str(account.account_id), "name": account.name, "type": account.type, "balance": float(account.balance)}

@router.get("/company/{company_id}")
def get_accounts_by_company(company_id: str, db: Session = Depends(get_db)):
    service = AccountService(db)
    accounts = service.get_accounts_by_company(company_id)
    return [{"account_id": str(a.account_id), "name": a.name, "type": a.type, "balance": float(a.balance)} for a in accounts]

@router.get("/{account_id}")
def get_account(account_id: str, db: Session = Depends(get_db)):
    service = AccountService(db)
    account = service.get_account(account_id)
    if not account:
        raise HTTPException(status_code=404, detail="Account not found")
    return {"account_id": str(account.account_id), "name": account.name, "type": account.type, "balance": float(account.balance)} 