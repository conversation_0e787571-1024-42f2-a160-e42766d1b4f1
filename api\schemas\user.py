from pydantic import BaseModel
from uuid import UUID

class UserCreate(BaseModel):
    username: str
    password: str
    role: str = "accountant"

class UserLogin(BaseModel):
    username: str
    password: str

class UserOut(BaseModel):
    user_id: UUID
    username: str
    role: str

class TokenResponse(BaseModel):
    access_token: str
    token_type: str = "bearer"
    user_id: str
    username: str 