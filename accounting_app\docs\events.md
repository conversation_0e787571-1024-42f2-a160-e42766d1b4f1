# أحداث التطبيق (AppEvents)

هذا الملف يوثق جميع الأحداث المستخدمة في التطبيق وكيفية استخدامها.

## نظرة عامة

جميع الأحداث يتم تعريفها في `core/events.py` كثوابت لتجنب الأخطاء وتسهيل الصيانة.

## قائمة الأحداث

### أحداث اللغة والترجمة
| الحدث | الوصف | البيانات المرسلة |
|-------|--------|------------------|
| `LANGUAGE_CHANGED` | عند تغيير لغة الواجهة | `lang_code` (string) |

### أحداث الإشعارات
| الحدث | الوصف | البيانات المرسلة |
|-------|--------|------------------|
| `NOTIFICATION_NEW` | إشعار جديد لعرضه | `{"type": "info", "message": "text"}` |
| `NOTIFY` | إشعار في NotificationPanel | `{"type": "info", "title": "title", "message": "text"}` |
| `SHOW_TOAST` | رسالة toast عابرة | `{"type": "success", "message": "text"}` |

### أحداث الذكاء الاصطناعي
| الحدث | الوصف | البيانات المرسلة |
|-------|--------|------------------|
| `AI_REQUEST` | طلب للذكاء الاصطناعي | `{"query": "user_text"}` |
| `AI_RESPONSE` | رد من الذكاء الاصطناعي | `{"response": "ai_text"}` |
| `AI_MESSAGE_SENT` | رسالة تم إرسالها للذكاء الاصطناعي | `{"message": "text"}` |
| `AI_MESSAGE_RECEIVED` | رسالة تم استقبالها من الذكاء الاصطناعي | `{"message": "text"}` |

### أحداث التبويبات والمحتوى
| الحدث | الوصف | البيانات المرسلة |
|-------|--------|------------------|
| `OPEN_TAB` | فتح تبويب جديد | `{"name": "tab_name", "title": "Tab Title", "icon": "icon.svg"}` |
| `CLOSE_TAB` | إغلاق تبويب | `{"index": 0}` |
| `TAB_CHANGED` | تغيير التبويب النشط | `{"index": 0}` |

### أحداث المستخدم والجلسة
| الحدث | الوصف | البيانات المرسلة |
|-------|--------|------------------|
| `USER_LOGGED_IN` | تسجيل دخول المستخدم | `{"username": "user", "role": "admin"}` |
| `USER_LOGGED_OUT` | تسجيل خروج المستخدم | `{}` |
| `USER_PROFILE_UPDATED` | تحديث ملف المستخدم | `{"username": "user", "changes": {...}}` |

### أحداث الإعدادات والثيم
| الحدث | الوصف | البيانات المرسلة |
|-------|--------|------------------|
| `SETTINGS_CHANGED` | تغيير الإعدادات | `{"setting": "value"}` |
| `THEME_CHANGED` | تغيير الثيم | `{"theme": "dark"}` |

### أحداث الاتصال والحالة
| الحدث | الوصف | البيانات المرسلة |
|-------|--------|------------------|
| `CONNECTION_STATUS_CHANGED` | تغيير حالة الاتصال | `{"status": "connected"}` |
| `DATABASE_STATUS_CHANGED` | تغيير حالة قاعدة البيانات | `{"status": "connected"}` |

### أحداث التحميل
| الحدث | الوصف | البيانات المرسلة |
|-------|--------|------------------|
| `SHOW_LOADING` | إظهار شاشة التحميل | `"Loading text"` |
| `HIDE_LOADING` | إخفاء شاشة التحميل | `{}` |

### أحداث الأخطاء
| الحدث | الوصف | البيانات المرسلة |
|-------|--------|------------------|
| `SHOW_ERROR` | عرض خطأ | `{"message": "error_text"}` |
| `ERROR_OCCURRED` | حدوث خطأ | `{"error": "error_details"}` |

### أحداث البحث
| الحدث | الوصف | البيانات المرسلة |
|-------|--------|------------------|
| `SEARCH_REQUESTED` | طلب بحث | `{"query": "search_text"}` |
| `SEARCH_RESULTS_UPDATED` | تحديث نتائج البحث | `{"results": [...]}` |

### أحداث الطباعة والتقرير
| الحدث | الوصف | البيانات المرسلة |
|-------|--------|------------------|
| `PRINT_REQUESTED` | طلب طباعة | `{"document": "doc_name"}` |
| `REPORT_GENERATED` | تم إنشاء تقرير | `{"report": "report_name"}` |

### أحداث النسخ الاحتياطي
| الحدث | الوصف | البيانات المرسلة |
|-------|--------|------------------|
| `BACKUP_STARTED` | بدء النسخ الاحتياطي | `{"type": "full"}` |
| `BACKUP_COMPLETED` | اكتمال النسخ الاحتياطي | `{"file": "backup.zip"}` |
| `BACKUP_FAILED` | فشل النسخ الاحتياطي | `{"error": "error_details"}` |

## أمثلة الاستخدام

### إرسال حدث
```python
from core.events import AppEvents

# إرسال حدث تغيير اللغة
self.event_bus.emit(AppEvents.LANGUAGE_CHANGED, "ar")

# إرسال إشعار
self.event_bus.emit(AppEvents.NOTIFY, {
    "type": "info",
    "title": "أهلاً بك!",
    "message": "مرحبًا بك في البرنامج"
})
```

### الاستماع لحدث
```python
from core.events import AppEvents

# الاشتراك في حدث
self.event_bus.subscribe(AppEvents.LANGUAGE_CHANGED, self.handle_language_change)

def handle_language_change(self, lang_code):
    # معالجة تغيير اللغة
    pass
```

## أفضل الممارسات

1. **استخدم الثوابت دائماً:** لا تكتب أسماء الأحداث كـ strings مباشرة
2. **وثق البيانات:** تأكد من أن البيانات المرسلة واضحة ومتسقة
3. **معالجة الأخطاء:** استخدم try/catch في معالجات الأحداث
4. **التسجيل:** استخدم logging لتتبع الأحداث المهمة
5. **التنظيم:** رتب الأحداث في مجموعات منطقية

## إضافة أحداث جديدة

لإضافة حدث جديد:

1. أضف الثابت في `core/events.py`
2. وثق الحدث في هذا الملف
3. استخدم الحدث في الكود
4. اختبر الحدث للتأكد من عمله 