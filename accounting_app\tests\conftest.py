#!/usr/bin/env python3
"""
إعدادات pytest للاختبارات
Pytest Configuration for Tests
"""

import pytest
from unittest.mock import Mock, MagicMock

from PySide6.QtWidgets import QApplication

# استيراد من الحزمة
from accounting_app import (
    AppInitializer,
    AppSettings,
    event_bus,
    setup_logging,
    User,
    UserRole
)


@pytest.fixture(scope="session")
def qt_app():
    """إنشاء تطبيق Qt للاختبارات"""
    app = QApplication.instance()
    if app is None:
        app = QApplication([])
    
    # إعداد التسجيل للاختبارات
    setup_logging(level="WARNING")
    
    yield app
    
    # تنظيف بعد الاختبارات
    app.quit()


@pytest.fixture
def app_settings():
    """إنشاء إعدادات للاختبارات"""
    return AppSettings()


@pytest.fixture
def app_initializer(qt_app):
    """إنشاء مُهيئ التطبيق للاختبارات"""
    initializer = AppInitializer(qt_app)
    initializer.initialize()
    return initializer


@pytest.fixture
def mock_notification_panel():
    """إنشاء لوحة إشعارات وهمية"""
    panel = Mock()
    panel.add_notification = Mock()
    panel.clear_notifications = Mock()
    panel.get_notification_count = Mock(return_value=0)
    return panel


@pytest.fixture
def mock_ai_assistant():
    """إنشاء مساعد ذكي وهمي"""
    assistant = Mock()
    assistant.send_message = Mock()
    assistant.receive_message = Mock()
    assistant.is_enabled = Mock(return_value=True)
    assistant.get_response = Mock(return_value="AI response")
    return assistant


@pytest.fixture
def mock_sidebar():
    """إنشاء شريط جانبي وهمي"""
    sidebar = Mock()
    sidebar.buttons = [Mock(), Mock(), Mock()]
    sidebar.window = Mock(return_value=Mock())
    
    # إعداد الأزرار
    for i, button in enumerate(sidebar.buttons):
        button.text = Mock(return_value=f"Button {i}")
        button.clicked = Mock()
    
    return sidebar


@pytest.fixture
def mock_content_area():
    """إنشاء منطقة محتوى وهمية"""
    content_area = Mock()
    content_area.count = Mock(return_value=2)
    content_area.tabText = Mock(side_effect=lambda i: f"Tab {i}")
    content_area.setCurrentIndex = Mock()
    content_area.addTab = Mock()
    content_area.removeTab = Mock()
    content_area.currentIndex = Mock(return_value=0)
    return content_area


@pytest.fixture
def mock_user():
    """إنشاء مستخدم وهمي"""
    user = Mock(spec=User)
    user.username = "test_user"
    user.email = "<EMAIL>"
    user.role = UserRole.ACCOUNTANT
    user.permissions = ["read", "write", "reports"]
    user.is_active = True
    user.has_permission = Mock(return_value=True)
    
    return user


@pytest.fixture
def sample_config_data():
    """بيانات تكوين نموذجية للاختبارات"""
    return {
        "app_name": "Test App",
        "app_version": "1.0.0",
        "environment": "testing",
        "database": {
            "host": "localhost",
            "port": 5432,
            "name": "test_db",
            "user": "test_user",
            "password": "test_pass"
        },
        "ui": {
            "theme": "dark",
            "language": "en",
            "font_size": 12
        },
        "ai": {
            "enabled": True,
            "model": "test-model",
            "max_tokens": 500
        }
    }


@pytest.fixture
def temp_config_file(tmp_path):
    """إنشاء ملف تكوين مؤقت للاختبارات"""
    config_file = tmp_path / "test_config.json"
    return str(config_file)


@pytest.fixture
def mock_translation_files(tmp_path):
    """إنشاء ملفات ترجمة وهمية للاختبارات"""
    translations_dir = tmp_path / "translations"
    translations_dir.mkdir()
    
    # إنشاء ملفات .ts
    for lang in ["ar", "en", "fr"]:
        ts_file = translations_dir / f"{lang}.ts"
        ts_file.write_text(f"<?xml version='1.0' encoding='utf-8'?>\n<TS version='2.1' language='{lang}'>\n</TS>")
    
    # إنشاء ملفات .qm
    for lang in ["ar", "en", "fr"]:
        qm_file = translations_dir / f"{lang}.qm"
        qm_file.write_text(f"Mock QM file for {lang}")
    
    return translations_dir


@pytest.fixture
def mock_resources(tmp_path):
    """إنشاء موارد وهمية للاختبارات"""
    resources_dir = tmp_path / "resources"
    resources_dir.mkdir()
    
    # إنشاء ملفات SVG وهمية
    svg_files = ["icon.svg", "logo.svg", "button.svg"]
    for svg_file in svg_files:
        (resources_dir / svg_file).write_text("<svg></svg>")
    
    # إنشاء ملف صوت وهمي
    (resources_dir / "notification.wav").write_text("Mock audio file")
    
    return resources_dir


@pytest.fixture
def mock_logs_dir(tmp_path):
    """إنشاء مجلد سجلات وهمي للاختبارات"""
    logs_dir = tmp_path / "logs"
    logs_dir.mkdir()
    
    # إنشاء ملفات سجلات وهمية
    log_files = ["app.log", "errors.log", "debug.log"]
    for log_file in log_files:
        (logs_dir / log_file).write_text("Mock log content")
    
    return logs_dir


# إعدادات pytest
def pytest_configure(config):
    """إعداد pytest"""
    # إضافة علامات مخصصة
    config.addinivalue_line(
        "markers", "slow: marks tests as slow (deselect with '-m \"not slow\"')"
    )
    config.addinivalue_line(
        "markers", "integration: marks tests as integration tests"
    )
    config.addinivalue_line(
        "markers", "unit: marks tests as unit tests"
    )
    config.addinivalue_line(
        "markers", "ui: marks tests as UI tests"
    )


def pytest_collection_modifyitems(config, items):
    """تعديل عناصر الاختبار"""
    for item in items:
        # إضافة علامة unit للاختبارات التي لا تحتوي على علامات
        if not any(item.iter_markers()):
            item.add_marker(pytest.mark.unit)


# دوال مساعدة للاختبارات
def create_mock_event_bus():
    """إنشاء event bus وهمي"""
    mock_bus = Mock()
    mock_bus.subscribe = Mock()
    mock_bus.unsubscribe = Mock()
    mock_bus.emit = Mock()
    mock_bus.get_event_history = Mock(return_value=[])
    return mock_bus


def create_mock_settings():
    """إنشاء إعدادات وهمية"""
    mock_settings = Mock(spec=AppSettings)
    mock_settings.language = "ar"
    mock_settings.theme = "dark"
    mock_settings.APP_TITLE = "Test App"
    mock_settings.get = Mock(return_value="default_value")
    mock_settings.set = Mock()
    mock_settings.save = Mock()
    mock_settings.load = Mock()
    return mock_settings


def assert_error_handled(error_handler, error_type, severity):
    """التحقق من معالجة الخطأ"""
    assert error_handler.error_type == error_type
    assert error_handler.severity == severity


def assert_log_message_contains(logger, message_part):
    """التحقق من وجود رسالة في السجل"""
    log_calls = logger.info.call_args_list + logger.error.call_args_list
    for call in log_calls:
        if message_part in str(call):
            return True
    return False 