"""
Core package for Smart Accounting App
حزمة النواة الأساسية لبرنامج المحاسبة الذكي
"""

# استيراد المكونات الأساسية
from .app_initializer import AppInitializer
from .settings import AppSettings
from .event_bus import EventBus, event_bus
from .events import AppEvents
from .style_manager import StyleManager, apply_safe_listview_fix
from .logger import setup_logging, get_logger, AppLogger
from .error_handler import error_handler, handle_error, AppError

# قائمة المكونات المتاحة
__all__ = [
    'AppInitializer',
    'AppSettings',
    'EventBus',
    'event_bus',
    'AppEvents',
    'StyleManager',
    'apply_safe_listview_fix',
    'setup_logging',
    'get_logger',
    'AppLogger',
    'error_handler',
    'handle_error',
    'AppError'
] 