"""
واجهة تقارير المشتريات - واجهة شاملة لجميع تقارير وإحصائيات المشتريات
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QPushButton, QLineEdit, QComboBox, QLabel, QGroupBox, QFormLayout,
    QDialog, QDialogButtonBox, QTabWidget, QTextEdit, QSpinBox,
    QDoubleSpinBox, QDateEdit, QCheckBox, QMessageBox, QHeaderView,
    QFrame, QScrollArea, QGridLayout, QSplitter, QProgressBar,
    QTreeWidget, QTreeWidgetItem, QListWidget, QListWidgetItem
)
from PySide6.QtCore import Qt, QDate, Signal
from PySide6.QtGui import QIcon, QFont, QPainter
from PySide6.QtCharts import QChart, QChartView, QPieSeries, QBarSeries, QBarSet, QLineSeries
from datetime import datetime, date
import logging


class PurchaseReportsTab(QWidget):
    """التبويب الرئيسي لتقارير المشتريات"""

    def tr(self, text):
        """دالة ترجمة بسيطة"""
        return text

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("PurchaseReportsTab")
        self.init_ui()
        self.load_sample_data()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)
        
        # شريط الأدوات العلوي
        toolbar = self.create_toolbar()
        layout.addWidget(toolbar)
        
        # المحتوى الرئيسي
        splitter = QSplitter(Qt.Horizontal)
        
        # قائمة التقارير (الجانب الأيسر)
        reports_list = self.create_reports_list()
        splitter.addWidget(reports_list)
        
        # منطقة عرض التقرير (الجانب الأيمن)
        report_area = self.create_report_area()
        splitter.addWidget(report_area)
        
        splitter.setSizes([300, 900])
        layout.addWidget(splitter)
        
        # شريط الحالة
        status_bar = self.create_status_bar()
        layout.addWidget(status_bar)
    
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar = QFrame()
        toolbar.setFrameStyle(QFrame.StyledPanel)
        layout = QHBoxLayout(toolbar)
        
        # أزرار العمليات
        self.generate_report_btn = QPushButton(self.tr("إنشاء التقرير"))
        self.generate_report_btn.setIcon(QIcon("accounting_app/resources/icons/report.svg"))
        self.generate_report_btn.clicked.connect(self.generate_report)
        
        self.export_excel_btn = QPushButton(self.tr("تصدير Excel"))
        self.export_excel_btn.setIcon(QIcon("accounting_app/resources/icons/excel.svg"))
        self.export_excel_btn.clicked.connect(self.export_to_excel)
        
        self.export_pdf_btn = QPushButton(self.tr("تصدير PDF"))
        self.export_pdf_btn.setIcon(QIcon("accounting_app/resources/icons/pdf.svg"))
        self.export_pdf_btn.clicked.connect(self.export_to_pdf)
        
        self.print_report_btn = QPushButton(self.tr("طباعة"))
        self.print_report_btn.setIcon(QIcon("accounting_app/resources/icons/print.svg"))
        self.print_report_btn.clicked.connect(self.print_report)
        
        # فترة التقرير
        self.date_from = QDateEdit()
        self.date_from.setDate(QDate.currentDate().addMonths(-1))
        self.date_to = QDateEdit()
        self.date_to.setDate(QDate.currentDate())
        
        # إضافة العناصر
        layout.addWidget(QLabel(self.tr("من:")))
        layout.addWidget(self.date_from)
        layout.addWidget(QLabel(self.tr("إلى:")))
        layout.addWidget(self.date_to)
        layout.addWidget(QFrame())  # فاصل
        layout.addWidget(self.generate_report_btn)
        layout.addWidget(self.export_excel_btn)
        layout.addWidget(self.export_pdf_btn)
        layout.addWidget(self.print_report_btn)
        layout.addStretch()
        
        return toolbar
    
    def create_reports_list(self):
        """إنشاء قائمة التقارير"""
        list_group = QGroupBox(self.tr("قائمة التقارير"))
        layout = QVBoxLayout(list_group)
        
        # شجرة التقارير
        self.reports_tree = QTreeWidget()
        self.reports_tree.setHeaderLabel(self.tr("التقارير"))
        self.reports_tree.itemClicked.connect(self.on_report_selected)
        
        # إضافة فئات التقارير
        self.populate_reports_tree()
        
        layout.addWidget(self.reports_tree)
        
        return list_group
    
    def populate_reports_tree(self):
        """ملء شجرة التقارير"""
        # تقارير المشتريات
        purchases_category = QTreeWidgetItem(self.reports_tree)
        purchases_category.setText(0, self.tr("تقارير المشتريات"))
        purchases_category.setExpanded(True)
        
        # تقارير فرعية للمشتريات
        purchase_reports = [
            self.tr("تقرير المشتريات الشهري"),
            self.tr("تقرير المشتريات حسب المورد"),
            self.tr("تقرير المشتريات حسب المنتج"),
            self.tr("تقرير أوامر الشراء"),
            self.tr("تقرير فواتير الشراء"),
            self.tr("تقرير المرتجعات")
        ]
        
        for report_name in purchase_reports:
            report_item = QTreeWidgetItem(purchases_category)
            report_item.setText(0, report_name)
        
        # تقارير الموردين
        suppliers_category = QTreeWidgetItem(self.reports_tree)
        suppliers_category.setText(0, self.tr("تقارير الموردين"))
        suppliers_category.setExpanded(True)
        
        supplier_reports = [
            self.tr("قائمة الموردين"),
            self.tr("تقييم الموردين"),
            self.tr("أرصدة الموردين"),
            self.tr("تقرير أداء الموردين"),
            self.tr("تقرير مدفوعات الموردين")
        ]
        
        for report_name in supplier_reports:
            report_item = QTreeWidgetItem(suppliers_category)
            report_item.setText(0, report_name)
        
        # التقارير المالية
        financial_category = QTreeWidgetItem(self.reports_tree)
        financial_category.setText(0, self.tr("التقارير المالية"))
        financial_category.setExpanded(True)
        
        financial_reports = [
            self.tr("تقرير التكاليف"),
            self.tr("تقرير الضرائب"),
            self.tr("تقرير الخصومات"),
            self.tr("تحليل الربحية"),
            self.tr("تقرير التدفق النقدي")
        ]
        
        for report_name in financial_reports:
            report_item = QTreeWidgetItem(financial_category)
            report_item.setText(0, report_name)
        
        # التقارير الإحصائية
        stats_category = QTreeWidgetItem(self.reports_tree)
        stats_category.setText(0, self.tr("التقارير الإحصائية"))
        stats_category.setExpanded(True)
        
        stats_reports = [
            self.tr("إحصائيات المشتريات"),
            self.tr("اتجاهات الشراء"),
            self.tr("تحليل الموسمية"),
            self.tr("مؤشرات الأداء الرئيسية"),
            self.tr("تقرير المقارنات")
        ]
        
        for report_name in stats_reports:
            report_item = QTreeWidgetItem(stats_category)
            report_item.setText(0, report_name)
    
    def create_report_area(self):
        """إنشاء منطقة عرض التقرير"""
        area_group = QGroupBox(self.tr("عرض التقرير"))
        layout = QVBoxLayout(area_group)
        
        # تبويبات عرض التقرير
        self.report_tabs = QTabWidget()
        
        # تبويب الجدول
        table_tab = self.create_table_tab()
        self.report_tabs.addTab(table_tab, self.tr("عرض جدولي"))
        
        # تبويب الرسوم البيانية
        chart_tab = self.create_chart_tab()
        self.report_tabs.addTab(chart_tab, self.tr("رسوم بيانية"))
        
        # تبويب الملخص
        summary_tab = self.create_summary_tab()
        self.report_tabs.addTab(summary_tab, self.tr("ملخص تنفيذي"))
        
        layout.addWidget(self.report_tabs)
        
        return area_group
    
    def create_table_tab(self):
        """إنشاء تبويب العرض الجدولي"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # جدول البيانات
        self.report_table = QTableWidget()
        self.report_table.setAlternatingRowColors(True)
        
        layout.addWidget(self.report_table)
        
        return tab
    
    def create_chart_tab(self):
        """إنشاء تبويب الرسوم البيانية"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # أزرار اختيار نوع الرسم البياني
        chart_buttons = QHBoxLayout()
        
        self.pie_chart_btn = QPushButton(self.tr("دائري"))
        self.pie_chart_btn.clicked.connect(lambda: self.show_chart("pie"))
        
        self.bar_chart_btn = QPushButton(self.tr("أعمدة"))
        self.bar_chart_btn.clicked.connect(lambda: self.show_chart("bar"))
        
        self.line_chart_btn = QPushButton(self.tr("خطي"))
        self.line_chart_btn.clicked.connect(lambda: self.show_chart("line"))
        
        chart_buttons.addWidget(self.pie_chart_btn)
        chart_buttons.addWidget(self.bar_chart_btn)
        chart_buttons.addWidget(self.line_chart_btn)
        chart_buttons.addStretch()
        
        layout.addLayout(chart_buttons)
        
        # منطقة عرض الرسم البياني
        self.chart_view = QChartView()
        layout.addWidget(self.chart_view)
        
        return tab
    
    def create_summary_tab(self):
        """إنشاء تبويب الملخص التنفيذي"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # بطاقات المؤشرات الرئيسية
        kpi_layout = QGridLayout()
        
        # إجمالي المشتريات
        total_purchases_card = self.create_kpi_card(
            self.tr("إجمالي المشتريات"),
            "1,250,000 ريال",
            "+15%",
            Qt.green
        )
        kpi_layout.addWidget(total_purchases_card, 0, 0)
        
        # عدد الطلبات
        total_orders_card = self.create_kpi_card(
            self.tr("عدد الطلبات"),
            "342",
            "+8%",
            Qt.green
        )
        kpi_layout.addWidget(total_orders_card, 0, 1)
        
        # متوسط قيمة الطلب
        avg_order_card = self.create_kpi_card(
            self.tr("متوسط قيمة الطلب"),
            "3,655 ريال",
            "+3%",
            Qt.green
        )
        kpi_layout.addWidget(avg_order_card, 0, 2)
        
        # عدد الموردين النشطين
        active_suppliers_card = self.create_kpi_card(
            self.tr("الموردين النشطين"),
            "28",
            "+2",
            Qt.green
        )
        kpi_layout.addWidget(active_suppliers_card, 0, 3)
        
        layout.addLayout(kpi_layout)
        
        # منطقة النص التفصيلي
        self.summary_text = QTextEdit()
        self.summary_text.setReadOnly(True)
        self.summary_text.setMaximumHeight(200)
        
        layout.addWidget(QLabel(self.tr("الملخص التفصيلي:")))
        layout.addWidget(self.summary_text)
        
        layout.addStretch()
        
        return tab
    
    def create_kpi_card(self, title, value, change, change_color):
        """إنشاء بطاقة مؤشر أداء رئيسي"""
        card = QGroupBox()
        card.setFixedHeight(120)
        layout = QVBoxLayout(card)
        
        # العنوان
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("Arial", 10))
        
        # القيمة
        value_label = QLabel(value)
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setFont(QFont("Arial", 16, QFont.Bold))
        
        # التغيير
        change_label = QLabel(change)
        change_label.setAlignment(Qt.AlignCenter)
        change_label.setStyleSheet(f"color: {change_color.name()};")
        change_label.setFont(QFont("Arial", 12))
        
        layout.addWidget(title_label)
        layout.addWidget(value_label)
        layout.addWidget(change_label)
        
        return card
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_bar = QFrame()
        status_bar.setFrameStyle(QFrame.StyledPanel)
        layout = QHBoxLayout(status_bar)
        
        self.status_label = QLabel(self.tr("جاهز"))
        layout.addWidget(self.status_label)
        layout.addStretch()
        
        return status_bar
    
    def load_sample_data(self):
        """تحميل بيانات وهمية للاختبار"""
        # تحديث الملخص التنفيذي
        summary_text = """
        تقرير المشتريات للفترة من 2024-01-01 إلى 2024-01-31
        
        النقاط الرئيسية:
        • ارتفاع في إجمالي المشتريات بنسبة 15% مقارنة بالشهر السابق
        • زيادة في عدد الطلبات بنسبة 8% مما يدل على نشاط متزايد
        • تحسن في متوسط قيمة الطلب بنسبة 3%
        • إضافة مورديْن جديديْن إلى قاعدة الموردين
        
        التوصيات:
        • مراجعة أسعار الموردين الحاليين للحصول على أفضل العروض
        • تقييم أداء الموردين الجدد بعد فترة تجريبية
        • تحسين عملية الموافقة على الطلبات لتقليل وقت المعالجة
        """
        
        self.summary_text.setPlainText(summary_text)
    
    def on_report_selected(self, item, column):
        """عند اختيار تقرير من الشجرة"""
        if item.parent():  # إذا كان عنصر فرعي (تقرير فعلي)
            report_name = item.text(0)
            self.status_label.setText(self.tr(f"تم اختيار: {report_name}"))
            self.load_report_data(report_name)
    
    def load_report_data(self, report_name):
        """تحميل بيانات التقرير المختار"""
        # بيانات وهمية للاختبار
        if "المشتريات الشهري" in report_name:
            self.load_monthly_purchases_report()
        elif "حسب المورد" in report_name:
            self.load_supplier_purchases_report()
        else:
            # تقرير افتراضي
            self.load_default_report()
    
    def load_monthly_purchases_report(self):
        """تحميل تقرير المشتريات الشهري"""
        # إعداد الجدول
        self.report_table.setColumnCount(4)
        self.report_table.setHorizontalHeaderLabels([
            self.tr("الشهر"),
            self.tr("عدد الطلبات"),
            self.tr("إجمالي المبلغ"),
            self.tr("متوسط الطلب")
        ])
        
        # بيانات وهمية
        data = [
            ["يناير 2024", "85", "320,500", "3,770"],
            ["فبراير 2024", "92", "365,200", "3,970"],
            ["مارس 2024", "78", "295,800", "3,792"],
            ["أبريل 2024", "87", "342,100", "3,932"]
        ]
        
        self.report_table.setRowCount(len(data))
        for row, row_data in enumerate(data):
            for col, cell_data in enumerate(row_data):
                self.report_table.setItem(row, col, QTableWidgetItem(cell_data))
        
        # تخصيص الجدول
        header = self.report_table.horizontalHeader()
        header.setStretchLastSection(True)
    
    def load_supplier_purchases_report(self):
        """تحميل تقرير المشتريات حسب المورد"""
        # إعداد الجدول
        self.report_table.setColumnCount(5)
        self.report_table.setHorizontalHeaderLabels([
            self.tr("المورد"),
            self.tr("عدد الطلبات"),
            self.tr("إجمالي المبلغ"),
            self.tr("متوسط الطلب"),
            self.tr("التقييم")
        ])
        
        # بيانات وهمية
        data = [
            ["شركة المواد الغذائية المتقدمة", "25", "125,000", "5,000", "4.5"],
            ["مؤسسة التقنية الحديثة", "18", "95,500", "5,306", "4.2"],
            ["أحمد محمد للتجارة", "12", "45,200", "3,767", "3.8"]
        ]
        
        self.report_table.setRowCount(len(data))
        for row, row_data in enumerate(data):
            for col, cell_data in enumerate(row_data):
                self.report_table.setItem(row, col, QTableWidgetItem(cell_data))
    
    def load_default_report(self):
        """تحميل تقرير افتراضي"""
        self.report_table.setColumnCount(3)
        self.report_table.setHorizontalHeaderLabels([
            self.tr("البند"),
            self.tr("القيمة"),
            self.tr("النسبة")
        ])
        
        data = [
            ["إجمالي المشتريات", "1,250,000", "100%"],
            ["المشتريات المحلية", "875,000", "70%"],
            ["المشتريات المستوردة", "375,000", "30%"]
        ]
        
        self.report_table.setRowCount(len(data))
        for row, row_data in enumerate(data):
            for col, cell_data in enumerate(row_data):
                self.report_table.setItem(row, col, QTableWidgetItem(cell_data))
    
    def show_chart(self, chart_type):
        """عرض الرسم البياني"""
        if chart_type == "pie":
            self.show_pie_chart()
        elif chart_type == "bar":
            self.show_bar_chart()
        elif chart_type == "line":
            self.show_line_chart()
    
    def show_pie_chart(self):
        """عرض رسم بياني دائري"""
        # إنشاء بيانات وهمية
        series = QPieSeries()
        series.append("المشتريات المحلية", 70)
        series.append("المشتريات المستوردة", 30)
        
        chart = QChart()
        chart.addSeries(series)
        chart.setTitle(self.tr("توزيع المشتريات"))
        
        self.chart_view.setChart(chart)
    
    def show_bar_chart(self):
        """عرض رسم بياني بالأعمدة"""
        # إنشاء بيانات وهمية
        set0 = QBarSet(self.tr("المشتريات"))
        set0.append([320, 365, 295, 342])
        
        series = QBarSeries()
        series.append(set0)
        
        chart = QChart()
        chart.addSeries(series)
        chart.setTitle(self.tr("المشتريات الشهرية"))
        
        self.chart_view.setChart(chart)
    
    def show_line_chart(self):
        """عرض رسم بياني خطي"""
        # إنشاء بيانات وهمية
        series = QLineSeries()
        series.append(1, 320)
        series.append(2, 365)
        series.append(3, 295)
        series.append(4, 342)
        
        chart = QChart()
        chart.addSeries(series)
        chart.setTitle(self.tr("اتجاه المشتريات"))
        
        self.chart_view.setChart(chart)
    
    def generate_report(self):
        """إنشاء التقرير"""
        self.status_label.setText(self.tr("جاري إنشاء التقرير..."))
        # هنا سيتم تنفيذ منطق إنشاء التقرير
        QMessageBox.information(self, self.tr("تقرير"), self.tr("تم إنشاء التقرير بنجاح"))
        self.status_label.setText(self.tr("تم إنشاء التقرير"))
    
    def export_to_excel(self):
        """تصدير إلى Excel"""
        QMessageBox.information(self, self.tr("تصدير"), self.tr("سيتم تصدير التقرير إلى Excel"))
    
    def export_to_pdf(self):
        """تصدير إلى PDF"""
        QMessageBox.information(self, self.tr("تصدير"), self.tr("سيتم تصدير التقرير إلى PDF"))
    
    def print_report(self):
        """طباعة التقرير"""
        QMessageBox.information(self, self.tr("طباعة"), self.tr("سيتم طباعة التقرير"))
