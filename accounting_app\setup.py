#!/usr/bin/env python3
"""
Setup script for Smart Accounting App
سكريبت إعداد برنامج المحاسبة الذكي
"""

from setuptools import setup, find_packages
import os

# قراءة README
def read_readme():
    readme_path = os.path.join(os.path.dirname(__file__), 'README.md')
    if os.path.exists(readme_path):
        with open(readme_path, 'r', encoding='utf-8') as f:
            return f.read()
    return "Smart Accounting App - برنامج المحاسبة الذكي"

# قراءة requirements
def read_requirements(filename):
    requirements_path = os.path.join(os.path.dirname(__file__), filename)
    if os.path.exists(requirements_path):
        with open(requirements_path, 'r', encoding='utf-8') as f:
            return [line.strip() for line in f if line.strip() and not line.startswith('#')]
    return []

setup(
    name="smart-accounting-app",
    version="1.0.0",
    author="Smart Accounting Team",
    author_email="<EMAIL>",
    description="Smart Accounting Application with AI Assistant",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/smartaccounting/app",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Financial and Insurance Industry",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Office/Business :: Financial :: Accounting",
    ],
    python_requires=">=3.8",
    install_requires=read_requirements('requirements.txt'),
    extras_require={
        'dev': read_requirements('dev-requirements.txt'),
        'test': [
            'pytest==7.4.3',
            'pytest-qt==4.2.0',
            'pytest-cov==4.1.0',
        ],
        'docs': [
            'sphinx==7.2.6',
            'sphinx-rtd-theme==1.3.0',
        ],
    },
    entry_points={
        'console_scripts': [
            'smart-accounting=accounting_app.main:main',
        ],
    },
    include_package_data=True,
    package_data={
        'accounting_app': [
            '*.qm', '*.ts', '*.svg', '*.wav', '*.qss',
            'translations/*.qm', 'translations/*.ts',
            'resources/*.svg', 'resources/*.wav',
            'ui/*.qss'
        ],
    },
    keywords="accounting, finance, business, qt, pyside6",
    project_urls={
        "Bug Reports": "https://github.com/smartaccounting/app/issues",
        "Source": "https://github.com/smartaccounting/app",
        "Documentation": "https://smartaccounting.readthedocs.io/",
    },
) 