import logging
import os
import sys
from pathlib import Path
from datetime import datetime
from typing import Optional

class ColoredFormatter(logging.Formatter):
    """مُنسق تسجيل ملون للوحة الأوامر"""
    
    COLORS = {
        'DEBUG': '\033[36m',    # Cyan
        'INFO': '\033[32m',     # Green
        'WARNING': '\033[33m',  # Yellow
        'ERROR': '\033[31m',    # Red
        'CRITICAL': '\033[35m', # Magenta
        'RESET': '\033[0m'      # Reset
    }
    
    def format(self, record):
        # إضافة اللون للرسالة
        if record.levelname in self.COLORS:
            record.levelname = f"{self.COLORS[record.levelname]}{record.levelname}{self.COLORS['RESET']}"
        
        return super().format(record)

class AppLogger:
    """مدير تسجيل متقدم للتطبيق"""
    
    def __init__(self, name: str = "SmartAccountingApp"):
        self.name = name
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.DEBUG)
        
        # منع تكرار التسجيل
        if not self.logger.handlers:
            self._setup_handlers()
    
    def _setup_handlers(self):
        """إعداد معالجات التسجيل"""
        # إنشاء مجلد السجلات
        logs_dir = Path("logs")
        logs_dir.mkdir(exist_ok=True)
        
        # معالج الملف العام
        general_handler = logging.FileHandler(
            logs_dir / "app.log", 
            encoding='utf-8'
        )
        general_handler.setLevel(logging.INFO)
        general_formatter = logging.Formatter(
            '%(asctime)s [%(levelname)s] %(name)s: %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        general_handler.setFormatter(general_formatter)
        
        # معالج ملف الأخطاء
        error_handler = logging.FileHandler(
            logs_dir / "errors.log", 
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_formatter = logging.Formatter(
            '%(asctime)s [%(levelname)s] %(name)s: %(message)s\n'
            'File: %(filename)s:%(lineno)d\n'
            'Function: %(funcName)s\n'
            'Traceback: %(exc_info)s\n',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        error_handler.setFormatter(error_formatter)
        
        # معالج ملف التصحيح
        debug_handler = logging.FileHandler(
            logs_dir / "debug.log", 
            encoding='utf-8'
        )
        debug_handler.setLevel(logging.DEBUG)
        debug_formatter = logging.Formatter(
            '%(asctime)s [%(levelname)s] %(name)s:%(lineno)d: %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        debug_handler.setFormatter(debug_formatter)
        
        # معالج لوحة الأوامر (ملون)
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_formatter = ColoredFormatter(
            '%(asctime)s [%(levelname)s] %(name)s: %(message)s',
            datefmt='%H:%M:%S'
        )
        console_handler.setFormatter(console_formatter)
        
        # إضافة المعالجات
        self.logger.addHandler(general_handler)
        self.logger.addHandler(error_handler)
        self.logger.addHandler(debug_handler)
        self.logger.addHandler(console_handler)
    
    def get_logger(self, name: Optional[str] = None) -> logging.Logger:
        """الحصول على مسجل مع اسم محدد"""
        if name:
            return logging.getLogger(f"{self.name}.{name}")
        return self.logger
    
    def set_level(self, level: str):
        """تعيين مستوى التسجيل"""
        level_map = {
            'DEBUG': logging.DEBUG,
            'INFO': logging.INFO,
            'WARNING': logging.WARNING,
            'ERROR': logging.ERROR,
            'CRITICAL': logging.CRITICAL
        }
        
        if level.upper() in level_map:
            self.logger.setLevel(level_map[level.upper()])
            for handler in self.logger.handlers:
                if isinstance(handler, logging.StreamHandler) and handler.stream == sys.stdout:
                    handler.setLevel(level_map[level.upper()])
    
    def log_startup(self):
        """تسجيل بدء التطبيق"""
        self.logger.info("=" * 60)
        self.logger.info("🚀 بدء تشغيل Smart Accounting App")
        self.logger.info(f"⏰ الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        self.logger.info(f"🐍 إصدار Python: {sys.version}")
        self.logger.info(f"📁 مجلد العمل: {os.getcwd()}")
        self.logger.info("=" * 60)
    
    def log_shutdown(self):
        """تسجيل إغلاق التطبيق"""
        self.logger.info("=" * 60)
        self.logger.info("🛑 إغلاق Smart Accounting App")
        self.logger.info(f"⏰ الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        self.logger.info("=" * 60)
    
    def log_performance(self, operation: str, duration: float):
        """تسجيل أداء العمليات"""
        if duration > 1.0:
            self.logger.warning(f"⏱️  {operation} استغرق {duration:.2f} ثانية")
        else:
            self.logger.debug(f"⏱️  {operation} استغرق {duration:.3f} ثانية")
    
    def log_user_action(self, user: str, action: str, details: str = ""):
        """تسجيل إجراءات المستخدم"""
        message = f"👤 المستخدم '{user}' قام بـ: {action}"
        if details:
            message += f" - {details}"
        self.logger.info(message)
    
    def log_error_with_context(self, error: Exception, context: str = ""):
        """تسجيل خطأ مع السياق"""
        message = f"❌ خطأ في {context}: {str(error)}" if context else f"❌ خطأ: {str(error)}"
        self.logger.error(message, exc_info=True)
    
    def log_security_event(self, event_type: str, details: str):
        """تسجيل أحداث الأمان"""
        self.logger.warning(f"🔒 حدث أمان: {event_type} - {details}")

# إنشاء مثيل عام للمسجل
app_logger = AppLogger()

def setup_logging(log_file: str = "app.log", level: str = "INFO") -> AppLogger:
    """إعداد نظام التسجيل للتطبيق (للتوافق مع الكود القديم)"""
    app_logger.set_level(level)
    app_logger.log_startup()
    return app_logger

def get_logger(name: Optional[str] = None) -> logging.Logger:
    """الحصول على مسجل (للتوافق مع الكود القديم)"""
    return app_logger.get_logger(name)

# دوال مساعدة للاستخدام السريع
def log_info(message: str, logger_name: Optional[str] = None):
    """تسجيل رسالة معلومات"""
    get_logger(logger_name).info(message)

def log_warning(message: str, logger_name: Optional[str] = None):
    """تسجيل تحذير"""
    get_logger(logger_name).warning(message)

def log_error(message: str, logger_name: Optional[str] = None):
    """تسجيل خطأ"""
    get_logger(logger_name).error(message)

def log_debug(message: str, logger_name: Optional[str] = None):
    """تسجيل تصحيح"""
    get_logger(logger_name).debug(message)

def log_critical(message: str, logger_name: Optional[str] = None):
    """تسجيل خطأ حرج"""
    get_logger(logger_name).critical(message) 