"""
نموذج أمر الشراء - يحتوي على جميع المعلومات المتعلقة بأوامر الشراء
"""

from dataclasses import dataclass, field
from typing import Optional, List, Dict, Any
from datetime import datetime, date
from enum import Enum
from decimal import Decimal


class PurchaseOrderStatus(Enum):
    """حالة أمر الشراء"""
    DRAFT = "مسودة"
    PENDING_APPROVAL = "في انتظار الموافقة"
    APPROVED = "موافق عليه"
    SENT = "مرسل"
    CONFIRMED = "مؤكد"
    PARTIALLY_RECEIVED = "مستلم جزئياً"
    FULLY_RECEIVED = "مستلم بالكامل"
    CANCELLED = "ملغي"
    CLOSED = "مغلق"


class PurchaseOrderPriority(Enum):
    """أولوية أمر الشراء"""
    LOW = "منخفضة"
    NORMAL = "عادية"
    HIGH = "عالية"
    URGENT = "عاجل"


class DeliveryMethod(Enum):
    """طريقة التوصيل"""
    PICKUP = "استلام من المورد"
    DELIVERY = "توصيل"
    SHIPPING = "شحن"
    COURIER = "بريد سريع"


@dataclass
class PurchaseOrderItem:
    """عنصر في أمر الشراء"""
    id: Optional[int] = None
    purchase_order_id: Optional[int] = None
    product_id: Optional[int] = None
    product_code: str = ""
    product_name: str = ""
    product_description: str = ""
    
    # الكميات والأسعار
    quantity_ordered: Decimal = Decimal('0')
    quantity_received: Decimal = Decimal('0')
    quantity_pending: Decimal = Decimal('0')
    unit_price: Decimal = Decimal('0')
    
    # الخصومات والضرائب
    discount_percentage: Decimal = Decimal('0')
    discount_amount: Decimal = Decimal('0')
    tax_percentage: Decimal = Decimal('15')  # ضريبة القيمة المضافة
    tax_amount: Decimal = Decimal('0')
    
    # المجاميع
    subtotal: Decimal = Decimal('0')  # الكمية × السعر
    total_after_discount: Decimal = Decimal('0')  # بعد الخصم
    total_with_tax: Decimal = Decimal('0')  # المجموع النهائي
    
    # معلومات إضافية
    unit_of_measure: str = "قطعة"
    specifications: str = ""
    quality_requirements: str = ""
    delivery_date: Optional[date] = None
    notes: str = ""
    
    # معلومات النظام
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    def __post_init__(self):
        """حساب المجاميع تلقائياً"""
        self.calculate_totals()
        if not self.created_at:
            self.created_at = datetime.now()
        self.updated_at = datetime.now()
        self.quantity_pending = self.quantity_ordered - self.quantity_received
    
    def calculate_totals(self):
        """حساب المجاميع"""
        self.subtotal = self.quantity_ordered * self.unit_price
        
        # حساب الخصم
        if self.discount_percentage > 0:
            self.discount_amount = self.subtotal * (self.discount_percentage / 100)
        
        self.total_after_discount = self.subtotal - self.discount_amount
        
        # حساب الضريبة
        if self.tax_percentage > 0:
            self.tax_amount = self.total_after_discount * (self.tax_percentage / 100)
        
        self.total_with_tax = self.total_after_discount + self.tax_amount
    
    def is_fully_received(self) -> bool:
        """التحقق من استلام الكمية كاملة"""
        return self.quantity_received >= self.quantity_ordered
    
    def get_pending_quantity(self) -> Decimal:
        """الحصول على الكمية المعلقة"""
        return max(Decimal('0'), self.quantity_ordered - self.quantity_received)


@dataclass
class PurchaseOrder:
    """نموذج أمر الشراء الرئيسي"""
    
    # المعلومات الأساسية
    id: Optional[int] = None
    order_number: str = ""
    reference_number: str = ""  # رقم مرجعي داخلي
    supplier_id: Optional[int] = None
    supplier_name: str = ""
    
    # التواريخ
    order_date: date = field(default_factory=date.today)
    expected_delivery_date: Optional[date] = None
    requested_delivery_date: Optional[date] = None
    actual_delivery_date: Optional[date] = None
    
    # الحالة والأولوية
    status: PurchaseOrderStatus = PurchaseOrderStatus.DRAFT
    priority: PurchaseOrderPriority = PurchaseOrderPriority.NORMAL
    
    # معلومات التوصيل
    delivery_method: DeliveryMethod = DeliveryMethod.DELIVERY
    delivery_address: str = ""
    delivery_contact_name: str = ""
    delivery_contact_phone: str = ""
    
    # المعلومات المالية
    currency: str = "SAR"
    exchange_rate: Decimal = Decimal('1')
    
    # المجاميع
    subtotal: Decimal = Decimal('0')
    total_discount: Decimal = Decimal('0')
    total_tax: Decimal = Decimal('0')
    shipping_cost: Decimal = Decimal('0')
    handling_cost: Decimal = Decimal('0')
    other_charges: Decimal = Decimal('0')
    total_amount: Decimal = Decimal('0')
    
    # شروط الدفع
    payment_terms: str = "30 يوم"
    payment_method: str = "تحويل بنكي"
    
    # معلومات الموافقة
    approval_status: str = "في انتظار الموافقة"
    approved_by: str = ""
    approved_at: Optional[datetime] = None
    approval_notes: str = ""
    
    # العناصر
    items: List[PurchaseOrderItem] = field(default_factory=list)
    
    # ملاحظات
    notes: str = ""
    internal_notes: str = ""
    terms_and_conditions: str = ""
    
    # معلومات النظام
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    created_by: str = ""
    updated_by: str = ""
    
    # معلومات إضافية
    department: str = ""
    project_code: str = ""
    cost_center: str = ""
    
    def __post_init__(self):
        """تهيئة إضافية بعد إنشاء الكائن"""
        if not self.created_at:
            self.created_at = datetime.now()
        self.updated_at = datetime.now()
        
        # إنشاء رقم الطلب إذا لم يكن موجوداً
        if not self.order_number:
            self.order_number = self.generate_order_number()
        
        # حساب المجاميع
        self.calculate_totals()
    
    def generate_order_number(self) -> str:
        """إنشاء رقم أمر الشراء تلقائياً"""
        today = date.today()
        year = today.year
        month = today.month
        import random
        sequence = random.randint(1000, 9999)
        return f"PO-{year}{month:02d}-{sequence}"
    
    def add_item(self, item: PurchaseOrderItem):
        """إضافة عنصر إلى أمر الشراء"""
        item.purchase_order_id = self.id
        self.items.append(item)
        self.calculate_totals()
    
    def remove_item(self, item_id: int):
        """حذف عنصر من أمر الشراء"""
        self.items = [item for item in self.items if item.id != item_id]
        self.calculate_totals()
    
    def calculate_totals(self):
        """حساب المجاميع الإجمالية"""
        self.subtotal = sum(item.subtotal for item in self.items)
        self.total_discount = sum(item.discount_amount for item in self.items)
        self.total_tax = sum(item.tax_amount for item in self.items)
        
        self.total_amount = (self.subtotal - self.total_discount + 
                           self.total_tax + self.shipping_cost + 
                           self.handling_cost + self.other_charges)
    
    def get_total_items(self) -> int:
        """الحصول على عدد العناصر"""
        return len(self.items)
    
    def get_total_quantity(self) -> Decimal:
        """الحصول على إجمالي الكمية"""
        return sum(item.quantity_ordered for item in self.items)
    
    def get_received_percentage(self) -> float:
        """الحصول على نسبة الاستلام"""
        if not self.items:
            return 0.0
        
        total_ordered = sum(item.quantity_ordered for item in self.items)
        total_received = sum(item.quantity_received for item in self.items)
        
        if total_ordered == 0:
            return 0.0
        
        return float(total_received / total_ordered * 100)
    
    def is_fully_received(self) -> bool:
        """التحقق من استلام الطلب كاملاً"""
        return all(item.is_fully_received() for item in self.items)
    
    def can_be_cancelled(self) -> bool:
        """التحقق من إمكانية إلغاء الطلب"""
        return self.status in [PurchaseOrderStatus.DRAFT, 
                              PurchaseOrderStatus.PENDING_APPROVAL,
                              PurchaseOrderStatus.APPROVED]
    
    def can_be_modified(self) -> bool:
        """التحقق من إمكانية تعديل الطلب"""
        return self.status in [PurchaseOrderStatus.DRAFT,
                              PurchaseOrderStatus.PENDING_APPROVAL]
    
    def approve(self, approved_by: str, notes: str = ""):
        """الموافقة على الطلب"""
        self.status = PurchaseOrderStatus.APPROVED
        self.approval_status = "موافق عليه"
        self.approved_by = approved_by
        self.approved_at = datetime.now()
        self.approval_notes = notes
        self.updated_at = datetime.now()
    
    def cancel(self, reason: str = ""):
        """إلغاء الطلب"""
        if self.can_be_cancelled():
            self.status = PurchaseOrderStatus.CANCELLED
            self.internal_notes += f"\nتم الإلغاء: {reason}" if reason else "\nتم الإلغاء"
            self.updated_at = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """تحويل الكائن إلى قاموس"""
        return {
            'id': self.id,
            'order_number': self.order_number,
            'reference_number': self.reference_number,
            'supplier_id': self.supplier_id,
            'supplier_name': self.supplier_name,
            'order_date': self.order_date.isoformat(),
            'expected_delivery_date': self.expected_delivery_date.isoformat() if self.expected_delivery_date else None,
            'status': self.status.value,
            'priority': self.priority.value,
            'delivery_method': self.delivery_method.value,
            'currency': self.currency,
            'subtotal': float(self.subtotal),
            'total_discount': float(self.total_discount),
            'total_tax': float(self.total_tax),
            'shipping_cost': float(self.shipping_cost),
            'total_amount': float(self.total_amount),
            'payment_terms': self.payment_terms,
            'approval_status': self.approval_status,
            'approved_by': self.approved_by,
            'notes': self.notes,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'created_by': self.created_by,
            'updated_by': self.updated_by,
            'items': [item.to_dict() if hasattr(item, 'to_dict') else item.__dict__ for item in self.items]
        }
