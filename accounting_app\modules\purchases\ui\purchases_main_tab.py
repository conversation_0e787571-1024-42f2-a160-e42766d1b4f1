from PySide6.QtWidgets import QWidget, QVBoxLayout, QTabWidget
from PySide6.QtCore import Qt

class PurchasesMainTab(QWidget):
    """التبويب الرئيسي لقسم المشتريات"""

    def tr(self, text):
        """دالة ترجمة بسيطة"""
        return text

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("PurchasesMainTab")
        self.init_ui()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)
        self.tabs = QTabWidget(self)

        # إضافة التبويبات الفرعية بشكل آمن
        try:
            from accounting_app.modules.purchases.ui.purchase_orders_view import PurchaseOrdersTab
            self.tabs.addTab(PurchaseOrdersTab(self), "أوامر الشراء")
        except Exception as e:
            print(f"خطأ في تحميل تبويب أوامر الشراء: {e}")
            # إضافة تبويب بديل
            from PySide6.QtWidgets import QLabel
            placeholder = QWidget()
            placeholder_layout = QVBoxLayout(placeholder)
            placeholder_layout.addWidget(QLabel("أوامر الشراء - قيد التطوير"))
            self.tabs.addTab(placeholder, "أوامر الشراء")

        try:
            from accounting_app.modules.purchases.ui.suppliers_view import SuppliersTab
            self.tabs.addTab(SuppliersTab(self), "إدارة الموردين")
        except Exception as e:
            print(f"خطأ في تحميل تبويب الموردين: {e}")
            from PySide6.QtWidgets import QLabel
            placeholder = QWidget()
            placeholder_layout = QVBoxLayout(placeholder)
            placeholder_layout.addWidget(QLabel("إدارة الموردين - قيد التطوير"))
            self.tabs.addTab(placeholder, "إدارة الموردين")

        # إضافة تبويبات أخرى بنفس الطريقة
        placeholder_tabs = [
            ("فواتير الشراء", "فواتير الشراء - قيد التطوير"),
            ("مرتجعات الشراء", "مرتجعات الشراء - قيد التطوير"),
            ("تقارير المشتريات", "تقارير المشتريات - قيد التطوير")
        ]

        for tab_name, label_text in placeholder_tabs:
            from PySide6.QtWidgets import QLabel
            placeholder = QWidget()
            placeholder_layout = QVBoxLayout(placeholder)
            placeholder_layout.addWidget(QLabel(label_text))
            self.tabs.addTab(placeholder, tab_name)

        layout.addWidget(self.tabs)
        self.setLayout(layout)