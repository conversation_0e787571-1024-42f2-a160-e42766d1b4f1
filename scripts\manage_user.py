import sys
import os
import argparse
import getpass

# إضافة مسار المشروع الجذري لمسار الاستيراد
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from database.config.connection_pool import SessionLocal
from database.repositories.user_repository import UserRepository
from database.models import AppUser
from database.utils import hash_password

def add_user(username, password, role):
    db = SessionLocal()
    repo = UserRepository(db)
    if repo.get_by_username(username):
        print(f"❌ Username '{username}' already exists.")
        return
    user = AppUser(username=username, password_hash=hash_password(password), role=role)
    repo.create(user)
    print(f"✓ User '{username}' created with role '{role}'.")
    db.close()

def update_password(username, password):
    db = SessionLocal()
    repo = UserRepository(db)
    user = repo.get_by_username(username)
    if not user:
        print(f"❌ Username '{username}' not found.")
        return
    user.password_hash = hash_password(password)
    repo.update(user)
    print(f"✓ Password updated for user '{username}'.")
    db.close()

def delete_user(username):
    db = SessionLocal()
    repo = UserRepository(db)
    user = repo.get_by_username(username)
    if not user:
        print(f"❌ Username '{username}' not found.")
        return
    repo.delete(user)
    print(f"✓ User '{username}' deleted.")
    db.close()

def main():
    parser = argparse.ArgumentParser(description="User management tool")
    subparsers = parser.add_subparsers(dest="command")

    # Add user
    add_parser = subparsers.add_parser("add", help="Add a new user")
    add_parser.add_argument("--username", required=True)
    add_parser.add_argument("--role", default="accountant")

    # Update password
    upd_parser = subparsers.add_parser("update-password", help="Update user password")
    upd_parser.add_argument("--username", required=True)

    # Delete user
    del_parser = subparsers.add_parser("delete", help="Delete a user")
    del_parser.add_argument("--username", required=True)

    args = parser.parse_args()

    if args.command == "add":
        password = getpass.getpass("Enter password: ")
        add_user(args.username, password, args.role)
    elif args.command == "update-password":
        password = getpass.getpass("Enter new password: ")
        update_password(args.username, password)
    elif args.command == "delete":
        delete_user(args.username)
    else:
        parser.print_help()

if __name__ == "__main__":
    main() 