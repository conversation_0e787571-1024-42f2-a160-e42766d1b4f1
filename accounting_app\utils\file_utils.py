"""
File Utilities for Smart Accounting App
أدوات إدارة الملفات لبرنامج المحاسبة الذكي
"""

import os
import logging
from pathlib import Path
from typing import Union, Optional

logger = logging.getLogger(__name__)

def get_resource_path(resource_name: str) -> str:
    """الحصول على المسار الصحيح لملف مورد معين"""
    try:
        base_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        resource_path = os.path.join(base_path, "resources", resource_name)
        
        # التحقق من وجود الملف
        if not os.path.exists(resource_path):
            logger.warning(f"Resource file not found: {resource_path}")
            # إرجاع مسار فارغ أو مسار أيقونة افتراضية
            return ""
        
        return resource_path
    except Exception as e:
        logger.error(f"Error getting resource path for {resource_name}: {e}")
        return ""

def ensure_directory(directory_path: Union[str, Path]) -> bool:
    """التأكد من وجود المجلد وإنشاؤه إذا لم يكن موجوداً"""
    try:
        path = Path(directory_path)
        path.mkdir(parents=True, exist_ok=True)
        return True
    except Exception as e:
        logger.error(f"Failed to create directory {directory_path}: {e}")
        return False

def get_app_data_path() -> str:
    """الحصول على مسار بيانات التطبيق"""
    try:
        if os.name == 'nt':  # Windows
            app_data = os.path.join(os.getenv('APPDATA', ''), 'SmartAccountingApp')
        else:  # Linux/Mac
            app_data = os.path.join(os.path.expanduser('~'), '.smart_accounting_app')
        
        ensure_directory(app_data)
        return app_data
    except Exception as e:
        logger.error(f"Failed to get app data path: {e}")
        return os.getcwd()

def get_logs_path() -> str:
    """الحصول على مسار ملفات السجلات"""
    try:
        logs_path = os.path.join(get_app_data_path(), 'logs')
        ensure_directory(logs_path)
        return logs_path
    except Exception as e:
        logger.error(f"Failed to get logs path: {e}")
        return os.path.join(os.getcwd(), 'logs')

def get_config_path() -> str:
    """الحصول على مسار ملفات الإعدادات"""
    try:
        config_path = os.path.join(get_app_data_path(), 'config')
        ensure_directory(config_path)
        return config_path
    except Exception as e:
        logger.error(f"Failed to get config path: {e}")
        return os.path.join(os.getcwd(), 'config')

def safe_file_read(file_path: Union[str, Path], encoding: str = 'utf-8') -> Optional[str]:
    """قراءة ملف بشكل آمن"""
    try:
        with open(file_path, 'r', encoding=encoding) as f:
            return f.read()
    except Exception as e:
        logger.error(f"Failed to read file {file_path}: {e}")
        return None

def safe_file_write(file_path: Union[str, Path], content: str, encoding: str = 'utf-8') -> bool:
    """كتابة ملف بشكل آمن"""
    try:
        # التأكد من وجود المجلد
        ensure_directory(os.path.dirname(file_path))
        
        with open(file_path, 'w', encoding=encoding) as f:
            f.write(content)
        return True
    except Exception as e:
        logger.error(f"Failed to write file {file_path}: {e}")
        return False

def get_file_size(file_path: Union[str, Path]) -> int:
    """الحصول على حجم الملف بالبايت"""
    try:
        return os.path.getsize(file_path)
    except Exception as e:
        logger.error(f"Failed to get file size for {file_path}: {e}")
        return 0

def is_file_readable(file_path: Union[str, Path]) -> bool:
    """التحقق من إمكانية قراءة الملف"""
    try:
        return os.path.isfile(file_path) and os.access(file_path, os.R_OK)
    except Exception as e:
        logger.error(f"Failed to check file readability for {file_path}: {e}")
        return False

def backup_file(file_path: Union[str, Path], backup_suffix: str = '.backup') -> bool:
    """إنشاء نسخة احتياطية من الملف"""
    try:
        if not os.path.exists(file_path):
            return False
        
        backup_path = str(file_path) + backup_suffix
        import shutil
        shutil.copy2(file_path, backup_path)
        logger.info(f"Backup created: {backup_path}")
        return True
    except Exception as e:
        logger.error(f"Failed to create backup for {file_path}: {e}")
        return False 