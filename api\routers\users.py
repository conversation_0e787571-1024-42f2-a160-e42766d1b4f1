from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from api.dependencies import get_db
from database.services.auth_service import AuthService
from api.schemas.user import UserCreate, UserLogin, UserOut, TokenResponse
from datetime import timedelta

router = APIRouter(prefix="/users", tags=["users"])

class UserAlreadyExists(Exception):
    pass

@router.post("/register", response_model=UserOut)
def register(user_in: UserCreate, db: Session = Depends(get_db)):
    service = AuthService(db)
    try:
        user = service.register_user(user_in.username, user_in.password, user_in.role)
        return UserOut(user_id=user.user_id, username=user.username, role=user.role)
    except UserAlreadyExists:
        raise HTTPException(status_code=409, detail="Username already exists")
    except ValueError as e:
        # fallback لأي خطأ آخر
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/login", response_model=TokenResponse)
def login(user_in: UserLogin, db: Session = Depends(get_db)):
    import asyncio
    service = AuthService(db)
    user = service.authenticate_user(user_in.username, user_in.password)
    if not user:
        # تأخير أمني بسيط
        asyncio.run(asyncio.sleep(0.5))
        raise HTTPException(status_code=401, detail="Invalid credentials")
    access_token = service.create_access_token(user.username, expires_delta=timedelta(hours=1))
    # يمكن إضافة logging هنا عند نجاح الدخول
    return TokenResponse(access_token=access_token, token_type="bearer", user_id=str(user.user_id), username=user.username) 