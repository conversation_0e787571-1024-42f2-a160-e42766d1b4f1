import pytest
from accounting_app.services.auth_service import AuthService
from datetime import timedelta

class DummyUserRepo:
    def __init__(self):
        self.users = {}
    def get_by_username(self, username):
        return self.users.get(username)
    def create(self, user):
        self.users[user.username] = user
        return user

class DummyUser:
    def __init__(self, id, username, password_hash, role):
        self.id = id
        self.username = username
        self.password_hash = password_hash
        self.role = role

def test_hash_and_verify_password():
    auth = AuthService(DummyUserRepo())
    password = "secret123"
    hashed = auth.hash_password(password)
    assert auth.verify_password(password, hashed)
    assert not auth.verify_password("wrong", hashed)

def test_create_and_decode_access_token():
    auth = AuthService(DummyUserRepo())
    user = DummyUser(id=1, username="test", password_hash="x", role="admin")
    token = auth.create_access_token(user, expires_delta=timedelta(minutes=5))
    payload = auth.decode_access_token(token)
    assert payload["sub"] == "test"
    assert payload["user_id"] == 1
    assert payload["role"] == "admin"
    assert "exp" in payload and "iat" in payload and "nbf" in payload 