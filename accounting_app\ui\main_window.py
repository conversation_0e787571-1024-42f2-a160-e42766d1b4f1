from PySide6.QtWidgets import <PERSON><PERSON><PERSON><PERSON><PERSON>ow, QWidget, QHB<PERSON>Layout, QVBoxLayout, QApplication
from PySide6.QtCore import Qt
from .sidebar import Sidebar
from .topbar import TopBar
from .content_area import ContentArea
from .notification_panel import NotificationPanel
from .ai_assistant import AIAssistantDock
from .footer import Footer
from .loading_overlay import LoadingOverlay
from .toast import ToastWidget
from accounting_app.controllers.sidebar_controller import <PERSON>bar<PERSON><PERSON>roller
from accounting_app.controllers.notification_controller import NotificationController
from accounting_app.controllers.ai_controller import AIController
from accounting_app.models.user import User
import logging
from accounting_app.core.style_manager import StyleManager
from accounting_app.core.events import AppEvents

class MainWindow(QMainWindow):
    """النافذة الرئيسية للتطبيق"""
    
    def __init__(self, language_controller, event_bus, settings, parent=None):
        super().__init__(parent)
        self.language_controller = language_controller
        self.event_bus = event_bus
        self.settings = settings
        self.setWindowTitle(self.settings.APP_TITLE)
        self.resize(1200, 800)
        self.setObjectName("MainWindow")
        
        # تطبيق الأنماط عند بداية التشغيل
        from accounting_app.core.style_manager import StyleManager
        from PySide6.QtWidgets import QApplication
        StyleManager(QApplication.instance(), theme=self.settings.theme).apply_style()
        
        # إعداد الأحداث
        self.setup_event_subscriptions()
        
        # تهيئة الواجهة
        self.init_ui()
        
        logging.info("MainWindow initialized successfully")

    def setup_event_subscriptions(self):
        """إعداد الاشتراكات في أحداث EventBus"""
        self.event_bus.subscribe(AppEvents.LANGUAGE_CHANGED, self.on_language_change)
        self.event_bus.subscribe(AppEvents.THEME_CHANGED, self.on_theme_change)
        self.event_bus.subscribe(AppEvents.SHOW_TOAST, self.on_show_toast)
        self.event_bus.subscribe(AppEvents.SHOW_LOADING, self.on_show_loading)
        self.event_bus.subscribe(AppEvents.HIDE_LOADING, self.on_hide_loading)
        logging.info("MainWindow event subscriptions setup completed")

    def teardown_event_subscriptions(self):
        """إلغاء الاشتراك من جميع أحداث EventBus"""
        self.event_bus.unsubscribe(AppEvents.LANGUAGE_CHANGED, self.on_language_change)
        self.event_bus.unsubscribe(AppEvents.THEME_CHANGED, self.on_theme_change)
        self.event_bus.unsubscribe(AppEvents.SHOW_TOAST, self.on_show_toast)
        self.event_bus.unsubscribe(AppEvents.SHOW_LOADING, self.on_show_loading)
        self.event_bus.unsubscribe(AppEvents.HIDE_LOADING, self.on_hide_loading)
        logging.info("MainWindow event subscriptions teardown completed")

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        central = QWidget()
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # الشريط العلوي
        self.topbar = TopBar(self.language_controller, self.event_bus, self.settings)
        main_layout.addWidget(self.topbar)
        
        # المحتوى مع الشريط الجانبي
        content_layout = QHBoxLayout()
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(0)
        
        # الشريط الجانبي
        self.sidebar = Sidebar(self.event_bus, self.language_controller, self.settings)
        content_layout.addWidget(self.sidebar)
        
        # منطقة المحتوى الرئيسية
        self.content_area = ContentArea(self.event_bus, self.language_controller, self.settings)
        content_layout.addWidget(self.content_area, 1)
        
        # لوحة الإشعارات
        self.notification_panel = NotificationPanel(self.event_bus, self.language_controller, self.settings)
        self.notification_panel.hide()
        self.notification_controller = NotificationController(self.notification_panel)
        content_layout.addWidget(self.notification_panel)
        
        # ربط زر الإشعارات
        self.topbar.notif_btn.clicked.connect(self.toggle_notification_panel)
        
        # تحكم الشريط الجانبي
        self.sidebar_controller = SidebarController(self.sidebar, self.content_area)
        main_layout.addLayout(content_layout)
        
        # الشريط السفلي
        self.footer = Footer(self.event_bus, self.language_controller, self.settings)
        main_layout.addWidget(self.footer)
        
        central.setLayout(main_layout)
        self.setCentralWidget(central)
        
        # مساعد الذكاء الاصطناعي
        self.setup_ai_assistant()
        
        # عناصر إضافية
        self.setup_additional_components()
        
        # إعداد المستخدم
        self.setup_user()

    def setup_ai_assistant(self):
        """إعداد مساعد الذكاء الاصطناعي"""
        self.ai_dock = AIAssistantDock(self)
        self.addDockWidget(Qt.RightDockWidgetArea, self.ai_dock)
        self.ai_dock.hide()
        self.ai_controller = AIController(self.ai_dock, self.event_bus)
        
        # ربط إشارات AIController مع AIAssistantDock
        self.ai_controller.message_received.connect(self.ai_dock.add_message)
        self.ai_controller.error_occurred.connect(lambda msg: self.event_bus.emit(AppEvents.SHOW_TOAST, {
            "message": msg,
            "type": "error"
        }))
        
        self.topbar.ai_btn.clicked.connect(self.toggle_ai_assistant)

    def setup_additional_components(self):
        """إعداد المكونات الإضافية"""
        # شاشة التحميل
        self.loading_overlay = LoadingOverlay(self)
        
        # رسائل Toast
        self.toast = ToastWidget(self)
        
        # ربط الأزرار
        self.topbar.exit_btn.clicked.connect(self.close)

    def setup_user(self):
        """إعداد المستخدم الافتراضي"""
        self.current_user = User("admin", "admin", email="<EMAIL>")
        self.customize_ui_for_role()
        self.unread_notif_count = 0
        
        # إشعار ترحيبي
        self.event_bus.emit(AppEvents.SHOW_TOAST, {
            "message": self.tr("مرحبًا بك في برنامج المحاسبة الذكي!"),
            "type": "success"
        })

    def on_language_change(self, lang_code):
        """معالج حدث تغيير اللغة"""
        try:
            logging.info(f"Language change event received: {lang_code}")
            
            if self.language_controller.current_lang == lang_code:
                logging.info(f"Language {lang_code} is already active, skipping update")
                return
                
            # حفظ اللغة الجديدة في الإعدادات
            self.settings.language = lang_code
            self.settings.save()
            
            # تحديث اتجاه الواجهة
            self.update_ui_direction()
            
            # إعادة ترجمة الواجهة
            self.retranslateUi()
            
            logging.info(f"Language changed to {lang_code} successfully")
            
        except Exception as e:
            logging.error(f"Failed to change language to {lang_code}: {e}")
            self.event_bus.emit(AppEvents.SHOW_TOAST, {
                "message": self.tr("فشل في تغيير اللغة إلى {}").format(lang_code),
                "type": "error"
            })

    def restart_window(self):
        """إعادة تشغيل النافذة بالكامل"""
        try:
            from PySide6.QtWidgets import QApplication
            
            # حفظ الحالة المطلوبة
            lang = self.language_controller.current_lang
            event_bus = self.event_bus
            settings = self.settings
            language_controller = self.language_controller
            
            # حفظ موقع وحجم النافذة
            current_geometry = self.geometry()
            current_state = self.windowState()
            
            # تعيين اتجاه التطبيق
            QApplication.instance().setLayoutDirection(
                self.language_controller.get_language_direction(lang)
            )
            
            # إلغاء الاشتراك من الأحداث
            self.teardown_event_subscriptions()
            
            # إغلاق النافذة الحالية
            self.close()
            self.deleteLater()
            
            # إنشاء نافذة جديدة
            from .main_window import MainWindow
            new_window = MainWindow(language_controller, event_bus, settings)
            
            # استعادة موقع وحجم النافذة
            new_window.setGeometry(current_geometry)
            new_window.setWindowState(current_state)
            new_window.show()
            
            # إظهار رسالة نجاح
            new_window.event_bus.emit(AppEvents.SHOW_TOAST, {
                "message": new_window.tr("تم تغيير اللغة إلى {}").format(lang),
                "type": "success"
            })
            
            logging.info(f"Window restarted successfully with language: {lang}")
            
        except Exception as e:
            logging.error(f"Failed to restart window: {e}")
            self.update_ui_direction()

    def on_theme_change(self, theme_data=None):
        """معالج حدث تغيير الثيم"""
        try:
            if hasattr(self, 'show_toast'):
                self.show_toast("تم استقبال حدث تغيير الثيم", True)
            from accounting_app.core.style_manager import StyleManager
            from PySide6.QtWidgets import QApplication, QWidget
            app = QApplication.instance()
            StyleManager(app, theme=self.settings.theme).apply_style()
            # تحديث النمط لجميع العناصر المفتوحة
            for widget in app.allWidgets():
                try:
                    widget.style().unpolish(widget)
                    widget.style().polish(widget)
                    # إصلاح: استدعاء update بشكل آمن مع QListView
                    from PySide6.QtWidgets import QListView
                    if isinstance(widget, QListView):
                        widget.update()
                    else:
                        widget.update()
                    widget.repaint()
                except Exception as e:
                    import logging
                    logging.warning(f"تعذر تلميع العنصر {type(widget)}: {e}")
        except Exception as e:
            import logging
            logging.error(f"حدث خطأ أثناء تحديث الثيم: {e}")

    def on_show_toast(self, toast_data):
        """معالج حدث إظهار toast"""
        try:
            message = toast_data.get("message", "")
            toast_type = toast_data.get("type", "info")
            success = toast_type == "success"
            self.show_toast(message, success)
        except Exception as e:
            logging.error(f"Failed to show toast: {e}")

    def on_show_loading(self, loading_data):
        """معالج حدث إظهار التحميل"""
        try:
            text = loading_data if isinstance(loading_data, str) else self.tr("جاري التحميل...")
            self.show_loading(text)
        except Exception as e:
            logging.error(f"Failed to show loading: {e}")

    def on_hide_loading(self, data=None):
        """معالج حدث إخفاء التحميل"""
        try:
            self.hide_loading()
        except Exception as e:
            logging.error(f"Failed to hide loading: {e}")

    def update_ui_direction(self):
        """تحديث اتجاه التخطيط لجميع العناصر"""
        direction = self.language_controller.get_language_direction(
            self.language_controller.current_lang
        )
        self.setLayoutDirection(direction)
        
        # تحديث كل العناصر الرئيسية
        for widget in [self.topbar, self.sidebar, self.content_area, 
                      self.footer, self.notification_panel, self.ai_dock]:
            if widget:
                self._update_widget_direction(widget, direction)

    def _update_widget_direction(self, widget, direction):
        """تحديث اتجاه عنصر معين"""
        try:
            widget.setLayoutDirection(direction)
            for child in widget.findChildren(QWidget):
                child.setLayoutDirection(direction)
        except Exception as e:
            logging.error(f"Failed to update widget direction: {e}")

    def toggle_ai_assistant(self):
        """تبديل حالة مساعد الذكاء الاصطناعي"""
        if self.ai_dock.isVisible():
            self.ai_dock.hide()
        else:
            self.ai_dock.show()

    def toggle_notification_panel(self):
        """تبديل حالة لوحة الإشعارات"""
        if self.notification_panel.isVisible():
            self.notification_panel.hide()
        else:
            self.notification_panel.show()

    def notify_with_panel_show(self, text, notif_type="تنبيه"):
        """إضافة إشعار مع إظهار اللوحة"""
        self.notification_controller.notify(text, notif_type)
        self.notification_panel.show()
        self.unread_notif_count += 1
        self.topbar.set_notif_badge(self.unread_notif_count)

    def show_loading(self, text=None):
        """إظهار شاشة التحميل"""
        self.loading_overlay.show_overlay(text)

    def hide_loading(self):
        """إخفاء شاشة التحميل"""
        self.loading_overlay.hide_overlay()

    def show_toast(self, text, success=True, duration=2000):
        """إظهار رسالة toast"""
        self.toast.show_toast(text, success, duration)

    def customize_ui_for_role(self):
        """تخصيص الواجهة حسب دور المستخدم"""
        try:
            if not self.current_user:
                return
                
            # إخفاء/إظهار العناصر حسب الصلاحيات
            if not self.current_user.can_access_module("settings"):
                for btn in self.sidebar.buttons:
                    if btn.text() == self.tr("الإعدادات"):
                        btn.setVisible(False)
                        break
            
            if not self.current_user.can_access_module("reports"):
                for btn in self.sidebar.buttons:
                    if btn.text() == self.tr("التقارير"):
                        btn.setVisible(False)
                        break
            
            if not self.current_user.can_access_module("pos"):
                for btn in self.sidebar.buttons:
                    if btn.text() == self.tr("نقطة البيع"):
                        btn.setVisible(False)
                        break
            
            # تحديث معلومات المستخدم في الشريط السفلي
            if hasattr(self, 'footer'):
                self.footer.set_user(self.current_user.username, self.current_user.role.value)
            
            logging.info(f"UI customized for user role: {self.current_user.role.value}")
            
        except Exception as e:
            logging.error(f"Error customizing UI for role: {e}")

    def set_user_and_role(self, username, role):
        """تعيين المستخدم ودوره"""
        try:
            self.current_user = User(username, role)
            self.customize_ui_for_role()
            logging.info(f"User set to: {username} with role: {role}")
        except Exception as e:
            logging.error(f"Error setting user and role: {e}")

    def show_login_dialog(self):
        """إظهار نافذة تسجيل الدخول"""
        try:
            from .login_dialog import LoginDialog
            dialog = LoginDialog(self)
            if dialog.exec() == LoginDialog.Accepted:
                # معالجة تسجيل الدخول
                username = dialog.user_input.text()
                password = dialog.pass_input.text()
                # يمكن إضافة منطق التحقق من صحة البيانات هنا
                self.set_user_and_role(username, "admin")
        except Exception as e:
            logging.error(f"Error showing login dialog: {e}")

    def eventFilter(self, obj, event):
        """فلتر الأحداث لإخفاء لوحة الإشعارات عند الضغط خارجها"""
        if (event.type() == event.MouseButtonPress and 
            self.notification_panel.isVisible() and 
            not self.notification_panel.geometry().contains(event.globalPos())):
            self.notification_panel.hide()
        return super().eventFilter(obj, event)

    def retranslateUi(self):
        """إعادة ترجمة جميع عناصر الواجهة"""
        try:
            # إعادة ترجمة عنوان النافذة
            self.setWindowTitle(self.settings.APP_TITLE)
            
            # إعادة ترجمة الشريط العلوي
            if hasattr(self, 'topbar'):
                self.topbar.reload_texts()
            
            # إعادة ترجمة الشريط الجانبي
            if hasattr(self, 'sidebar'):
                self.sidebar.reload_texts()
            
            # إعادة ترجمة منطقة المحتوى
            if hasattr(self, 'content_area'):
                self.content_area.reload_texts()
            
            # إعادة ترجمة الشريط السفلي
            if hasattr(self, 'footer'):
                self.footer.status_label.setText(self.tr("🟢 متصل بقاعدة البيانات"))
                self.footer.user_label.setText(self.tr("المستخدم: {} ({})").format(
                    self.current_user.username, self.current_user.role.value
                ))
                self.footer.version_label.setText(self.tr("الإصدار: {}").format(self.settings.APP_VERSION))
                self.footer.space_label.setText(self.tr("المساحة الحرة: 120GB"))
            
            # إعادة ترجمة لوحة الإشعارات
            if hasattr(self, 'notification_panel'):
                current_filter = self.notification_panel.filter_box.currentText()
                self.notification_panel.filter_box.clear()
                self.notification_panel.filter_box.addItems([
                    self.tr("الكل"), self.tr("تنبيه"), self.tr("نظام"), self.tr("AI")
                ])
                index = self.notification_panel.filter_box.findText(current_filter)
                if index >= 0:
                    self.notification_panel.filter_box.setCurrentIndex(index)
            
            # إعادة ترجمة مساعد الذكاء الاصطناعي
            if hasattr(self, 'ai_dock'):
                self.ai_dock.setWindowTitle(self.tr("مساعد الذكاء الصناعي"))
                if hasattr(self.ai_dock, 'input_line'):
                    self.ai_dock.input_line.setPlaceholderText(self.tr("اكتب سؤالك..."))
                if hasattr(self.ai_dock, 'send_btn'):
                    self.ai_dock.send_btn.setText(self.tr("إرسال"))
                if hasattr(self.ai_dock, 'support_btn'):
                    self.ai_dock.support_btn.setText(self.tr("الدعم"))
            
            # تحديث اتجاه التخطيط
            self.update_ui_direction()
            
            logging.info("MainWindow UI retranslated successfully")
            
        except Exception as e:
            logging.error(f"Error during UI retranslation: {e}")
            self.event_bus.emit(AppEvents.SHOW_TOAST, {
                "message": self.tr("حدث خطأ أثناء تحديث الواجهة"),
                "type": "error"
            }) 