import logging
from functools import partial
from accounting_app.utils import safe_update

class SidebarController:
    def __init__(self, sidebar, content_area):
        self.sidebar = sidebar
        self.content_area = content_area
        self.button_map = {}
        self.setup_connections()
        logging.info("SidebarController initialized.")

    def setup_connections(self):
        for i, btn in enumerate(self.sidebar.buttons):
            # الحل الأول: استخدام functools.partial
            btn.clicked.connect(partial(self.handle_button_click, i))
            
            # الحل البديل: استخدام دالة منفصلة
            # btn.clicked.connect(self.create_button_handler(i))
            
            # الحل الثالث: استخدام lambda مع تقييم فوري
            # btn.clicked.connect(lambda checked, idx=i: self.handle_button_click(idx))
            
            self.button_map[btn] = i
        logging.debug("Sidebar buttons connected.")

    def handle_button_click(self, idx):
        tab_title = self.sidebar.buttons[idx].text()
        logging.info(f"Sidebar button clicked: {tab_title}")
        
        # معالجة خاصة لزر اختبار التحميل
        if tab_title == "اختبار التحميل":
            # استدعاء مؤشر التحميل من MainWindow
            main_window = self.sidebar.window()
            if hasattr(main_window, 'show_loading') and hasattr(main_window, 'hide_loading'):
                main_window.show_loading("جاري اختبار التحميل...")
                from PySide6.QtCore import QTimer
                QTimer.singleShot(2000, main_window.hide_loading)  # إخفاء بعد ثانيتين
            logging.info("Test loading triggered from sidebar.")
            return
        
        # معالجة خاصة لزر الإعدادات
        if tab_title == "الإعدادات":
            try:
                idx = self.content_area.add_settings_tab()
                logging.info(f"Settings tab added/activated at index: {idx}")
                return
            except Exception as e:
                import traceback
                logging.error(f"Settings tab failed: {e}\n{traceback.format_exc()}")
        
        # معالجة عامة للتبويبات الأخرى
        for i in range(self.content_area.count()):
            if self.content_area.tabText(i) == tab_title:
                self.content_area.setCurrentIndex(i)
                logging.debug(f"Switched to existing tab: {tab_title}")
                return
        
        try:
            from PySide6.QtWidgets import QWidget, QVBoxLayout, QLabel
            from PySide6.QtCore import Qt
            tab = QWidget()
            layout = QVBoxLayout()
            label = QLabel(f"محتوى {tab_title}")
            label.setAlignment(Qt.AlignCenter)
            layout.addWidget(label)
            tab.setLayout(layout)
            self.content_area.addTab(tab, tab_title)
            self.content_area.setCurrentIndex(self.content_area.count() - 1)
            logging.info(f"Added new tab: {tab_title}")
        except Exception as e:
            logging.exception(f"Error while adding tab: {tab_title}") 