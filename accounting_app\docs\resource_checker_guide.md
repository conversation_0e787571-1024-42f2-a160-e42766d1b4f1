# دليل فاحص الموارد - Resource Checker Guide

## نظرة عامة

فاحص الموارد هو نظام متكامل للتحقق من وجود وصحة جميع ملفات الموارد المطلوبة للتطبيق عند بدء التشغيل. هذا يضمن عدم حدوث أخطاء مفاجئة أثناء تشغيل التطبيق.

## الميزات

### ✅ فحص شامل للموارد
- **الأيقونات SVG**: فحص جميع الأيقونات المطلوبة
- **ملفات الصوت**: فحص ملفات الإشعارات الصوتية
- **ملفات الترجمة**: فحص ملفات الترجمة (.qm)

### 🔧 إصلاح تلقائي
- إنشاء بدائل للموارد المفقودة
- استبدال الملفات المعطوبة
- تقارير مفصلة عن حالة الموارد

### 📊 تقارير مفصلة
- إحصائيات شاملة عن الموارد
- تفاصيل الموارد المفقودة والمعطوبة
- سجل مفصل لعمليات الإصلاح

## الاستخدام

### الاستخدام الأساسي

```python
from accounting_app.utils import check_resources_on_startup

# فحص الموارد عند بدء التشغيل
success = check_resources_on_startup()
if success:
    print("جميع الموارد جاهزة")
else:
    print("هناك مشاكل في الموارد")
```

### الاستخدام المتقدم

```python
from accounting_app.utils import ResourceChecker

# إنشاء فاحص مخصص
checker = ResourceChecker()

# فحص جميع الموارد
results = checker.check_all_resources()

# الحصول على الموارد المفقودة
missing = checker.get_missing_resources()

# إصلاح الموارد المفقودة
fixed_count = checker.fix_missing_resources()
```

## الموارد المدعومة

### الأيقونات (SVG)
- `home.svg` - أيقونة الصفحة الرئيسية
- `user.svg` - أيقونة المستخدم
- `settings.svg` - أيقونة الإعدادات
- `ai.svg` - أيقونة المساعد الذكي
- `bell.svg` - أيقونة الإشعارات
- `exit.svg` - أيقونة الخروج
- `lang.svg` - أيقونة اللغة
- `mic.svg` - أيقونة الميكروفون
- `loading.svg` - أيقونة التحميل
- `support.svg` - أيقونة الدعم

### ملفات الصوت
- `notification.wav` - صوت الإشعارات

### ملفات الترجمة
- `ar.qm` - الترجمة العربية
- `en.qm` - الترجمة الإنجليزية
- `fr.qm` - الترجمة الفرنسية

## آلية الإصلاح التلقائي

### بدائل الأيقونات
عند فقدان أيقونة SVG، يقوم النظام بإنشاء بديل بسيط باستخدام بيانات SVG مشفرة:

```python
# مثال على بديل أيقونة home.svg
fallback_data = """
<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M3 12L12 3L21 12L20 13H19V20H5V13H4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
"""
```

### عملية الإصلاح
1. **الكشف**: تحديد الموارد المفقودة أو المعطوبة
2. **التقييم**: تقييم إمكانية الإصلاح
3. **الإصلاح**: إنشاء بدائل مناسبة
4. **التحقق**: التأكد من نجاح الإصلاح

## التقارير

### تقرير مفصل
```
🔍 بدء فحص موارد التطبيق...

📁 ICONS:
  ✅ home.svg (287 بايت)
  ✅ user.svg (270 بايت)
  ✅ settings.svg (964 بايت)
  ⚠️  ai.svg: أيقونة فارغة (0 بايت)
  ❌ bell.svg: أيقونة غير موجود

📁 SOUNDS:
  ✅ notification.wav (1024 بايت)

📁 TRANSLATIONS:
  ✅ ar.qm (5324 بايت)
  ✅ en.qm (8912 بايت)
  ✅ fr.qm (9876 بايت)

📈 ملخص الفحص:
  إجمالي الموارد: 11
  الموارد الموجودة: 9
  الموارد المفقودة: 1
  الموارد المعطوبة: 1
```

### رسائل الحالة
- ✅ **نجح**: المورد موجود وصحيح
- ⚠️ **تحذير**: المورد موجود لكن معطوب
- ❌ **خطأ**: المورد مفقود
- 🔧 **إصلاح**: تم إصلاح المورد

## التكامل مع التطبيق

### في AppInitializer
```python
def initialize(self):
    # فحص موارد التطبيق
    resources_ok = check_resources_on_startup()
    if not resources_ok:
        logging.warning("⚠️  تم اكتشاف مشاكل في الموارد، لكن التطبيق سيستمر في العمل")
```

### في main.py
```python
def main():
    app = QApplication(sys.argv)
    
    # تهيئة التطبيق (يشمل فحص الموارد)
    initializer = AppInitializer(app)
    initializer.initialize()
    
    # إنشاء النافذة الرئيسية
    window = MainWindow(...)
    window.show()
```

## إعدادات متقدمة

### تخصيص مسارات الموارد
```python
checker = ResourceChecker(app_root="/path/to/app")
```

### إضافة موارد جديدة
```python
# إضافة مورد جديد إلى القائمة
checker.required_resources["icons"].append("new_icon.svg")

# إضافة بديل للمورد الجديد
checker.fallback_resources["new_icon.svg"] = "data:image/svg+xml;..."
```

## استكشاف الأخطاء

### مشاكل شائعة

1. **مورد مفقود**
   ```
   ❌ icon.svg: أيقونة غير موجود
   ```
   **الحل**: تأكد من وجود الملف في المجلد الصحيح

2. **مورد معطوب**
   ```
   ⚠️  icon.svg: أيقونة فارغة (0 بايت)
   ```
   **الحل**: استبدل الملف بملف صحيح

3. **خطأ في الوصول**
   ```
   ❌ icon.svg: خطأ في فحص أيقونة: Permission denied
   ```
   **الحل**: تحقق من صلاحيات الوصول للملف

### رسائل التصحيح
```python
import logging
logging.getLogger('accounting_app.utils.resource_checker').setLevel(logging.DEBUG)
```

## أفضل الممارسات

### 1. فحص دوري
```python
# فحص الموارد عند بدء التشغيل
check_resources_on_startup()

# فحص دوري كل فترة
import threading
import time

def periodic_check():
    while True:
        time.sleep(3600)  # كل ساعة
        check_resources_on_startup()

thread = threading.Thread(target=periodic_check, daemon=True)
thread.start()
```

### 2. نسخ احتياطية
```python
# إنشاء نسخة احتياطية من الموارد
import shutil
import os

def backup_resources():
    backup_dir = "backup/resources"
    os.makedirs(backup_dir, exist_ok=True)
    shutil.copytree("resources", backup_dir, dirs_exist_ok=True)
```

### 3. مراقبة التغييرات
```python
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

class ResourceChangeHandler(FileSystemEventHandler):
    def on_modified(self, event):
        if event.is_directory:
            return
        if event.src_path.endswith(('.svg', '.wav', '.qm')):
            print(f"تم تغيير المورد: {event.src_path}")
            check_resources_on_startup()
```

## الخلاصة

فاحص الموارد يوفر:
- ✅ **حماية من الأخطاء**: منع الأخطاء المفاجئة
- 🔧 **إصلاح تلقائي**: استبدال الموارد المفقودة
- 📊 **تقارير مفصلة**: معلومات شاملة عن حالة الموارد
- 🚀 **تكامل سلس**: يعمل تلقائياً مع التطبيق
- 🛡️ **موثوقية عالية**: ضمان استقرار التطبيق

هذا النظام يضمن أن التطبيق سيعمل بشكل موثوق حتى لو كانت بعض الموارد مفقودة أو معطوبة. 