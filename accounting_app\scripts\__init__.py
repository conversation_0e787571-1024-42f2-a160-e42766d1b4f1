"""
Scripts package for Smart Accounting App
حزمة السكريبتات لبرنامج المحاسبة الذكي
"""

import os
from pathlib import Path

# الحصول على مسار مجلد السكريبتات
SCRIPTS_DIR = Path(__file__).parent

def get_script_path(filename: str) -> str:
    """الحصول على المسار الكامل للسكريبت"""
    return str(SCRIPTS_DIR / filename)

def list_scripts() -> list:
    """قائمة بجميع السكريبتات المتاحة"""
    scripts = []
    for file_path in SCRIPTS_DIR.iterdir():
        if file_path.is_file() and file_path.name != "__init__.py" and file_path.suffix == '.py':
            scripts.append(file_path.name)
    return scripts

def script_exists(filename: str) -> bool:
    """التحقق من وجود سكريبت"""
    return (SCRIPTS_DIR / filename).exists()

# قائمة السكريبتات المتاحة مع وصفها
AVAILABLE_SCRIPTS = {
    "check_dependencies.py": "فحص التبعيات والمكتبات المطلوبة",
    "convert_translations.py": "تحويل ملفات الترجمة من .ts إلى .qm",
    "convert_ts_to_qm.py": "تحويل ملفات .ts إلى .qm باستخدام lrelease",
    "create_english_qm.py": "إنشاء ملف ترجمة إنجليزي فارغ",
    "fix_english_translation.py": "إصلاح ملف الترجمة الإنجليزية",
    "fix_translations.py": "إصلاح ملفات الترجمة التالفة",
    "test_translation.py": "اختبار نظام الترجمة",
    "update_translations.py": "تحديث ملفات الترجمة"
}

def get_script_info(filename: str) -> dict:
    """الحصول على معلومات السكريبت"""
    if filename in AVAILABLE_SCRIPTS:
        return {
            "filename": filename,
            "description": AVAILABLE_SCRIPTS[filename],
            "exists": script_exists(filename),
            "path": get_script_path(filename) if script_exists(filename) else None
        }
    return None

def run_script(script_name: str, *args):
    """تشغيل سكريبت مع المعاملات"""
    if not script_exists(script_name):
        raise FileNotFoundError(f"Script not found: {script_name}")
    
    script_path = get_script_path(script_name)
    import subprocess
    import sys
    
    result = subprocess.run([sys.executable, script_path] + list(args), 
                          capture_output=True, text=True)
    return result 