import sys
import os
from datetime import datetime

# إضافة مسار المشروع الجذري لمسار الاستيراد
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from database.config.connection_pool import SessionLocal
from database.repositories.user_repository import UserRepository
from database.repositories.account_repository import AccountRepository
from database.repositories.transaction_repository import TransactionRepository
from database.models import AppUser, Company, Account, Transaction, TransactionEntry
from database.utils import hash_password


def seed():
    db = SessionLocal()
    user_repo = UserRepository(db)
    account_repo = AccountRepository(db)
    transaction_repo = TransactionRepository(db)

    # إضافة مستخدمين
    if not user_repo.get_by_username('admin'):
        admin = AppUser(username='admin', password_hash=hash_password('admin123'), role='admin')
        user_repo.create(admin)
        print('✓ Created admin user (admin/admin123)')
    else:
        print('Admin user already exists.')

    if not user_repo.get_by_username('accountant'):
        accountant = AppUser(username='accountant', password_hash=hash_password('acc123'), role='accountant')
        user_repo.create(accountant)
        print('✓ Created accountant user (accountant/acc123)')
    else:
        print('Accountant user already exists.')

    # إضافة شركة
    from sqlalchemy.orm import Session
    admin_user = user_repo.get_by_username('admin')
    company = db.query(Company).filter_by(name='Demo Company').first()
    if not company:
        company = Company(name='Demo Company', owner_id=admin_user.user_id)
        db.add(company)
        db.commit()
        db.refresh(company)
        print('✓ Created Demo Company')
    else:
        print('Demo Company already exists.')

    # إضافة حسابات أساسية
    accounts = [
        ('Cash', 'Asset'),
        ('Bank', 'Asset'),
        ('Suppliers', 'Liability'),
        ('Capital', 'Equity'),
        ('Sales', 'Revenue'),
        ('Expenses', 'Expense'),
    ]
    for name, type_ in accounts:
        acc = db.query(Account).filter_by(company_id=company.company_id, name=name).first()
        if not acc:
            acc = Account(company_id=company.company_id, name=name, type=type_, balance=0)
            account_repo.create(acc)
            print(f'✓ Created account: {name} ({type_})')
        else:
            print(f'Account {name} already exists.')

    # إضافة عملية محاسبية تجريبية
    transaction = db.query(Transaction).filter_by(company_id=company.company_id, description='Demo Transaction').first()
    if not transaction:
        transaction = Transaction(company_id=company.company_id, description='Demo Transaction', date=datetime.utcnow())
        db.add(transaction)
        db.commit()
        db.refresh(transaction)
        # إضافة قيدين (مدين ودائن)
        cash = db.query(Account).filter_by(company_id=company.company_id, name='Cash').first()
        sales = db.query(Account).filter_by(company_id=company.company_id, name='Sales').first()
        entry1 = TransactionEntry(transaction_id=transaction.transaction_id, account_id=cash.account_id, amount=1000, is_debit=True)
        entry2 = TransactionEntry(transaction_id=transaction.transaction_id, account_id=sales.account_id, amount=1000, is_debit=False)
        db.add(entry1)
        db.add(entry2)
        db.commit()
        print('✓ Created demo transaction (Cash/Sales)')
    else:
        print('Demo transaction already exists.')

    db.close()

if __name__ == '__main__':
    seed() 