import logging
from typing import Optional, Dict, Any
from PySide6.QtCore import QObject, Signal
from accounting_app.core.events import AppEvents

class AIController(QObject):
    """تحكم في وظائف الذكاء الاصطناعي"""
    
    # إشارات للتواصل مع الواجهة
    message_received = Signal(str)  # message, sender
    suggestion_ready = Signal(str)  # suggestion text
    error_occurred = Signal(str)  # error message
    
    def __init__(self, ai_dock, event_bus=None, settings=None):
        super().__init__()
        self.ai_dock = ai_dock
        self.event_bus = event_bus
        self.settings = settings
        self.conversation_history = []
        self.is_processing = False
        self.setup()
        logging.info("AIController initialized.")

    def setup(self):
        """إعداد التحكم بالذكاء الاصطناعي"""
        try:
            # ربط الإشارات
            if hasattr(self.ai_dock, 'input_line'):
                self.ai_dock.input_line.returnPressed.connect(self.handle_input)
            
            # إعداد قائمة الردود الذكية
            self.smart_responses = {
                "help": "كيف يمكنني مساعدتك؟ يمكنني مساعدتك في:\n• إعداد الحسابات\n• إنشاء التقارير\n• حل المشاكل المحاسبية\n• الإجابة على الأسئلة العامة",
                "report": "لإنشاء تقرير:\n1. اذهب إلى قسم التقارير\n2. اختر نوع التقرير المطلوب\n3. حدد الفترة الزمنية\n4. اضغط على 'إنشاء التقرير'",
                "backup": "لإنشاء نسخة احتياطية:\n1. اذهب إلى الإعدادات\n2. اختر 'النسخ الاحتياطي'\n3. اختر الموقع\n4. اضغط 'إنشاء النسخة'",
                "settings": "للوصول للإعدادات:\n1. اضغط على زر الإعدادات في الشريط الجانبي\n2. أو استخدم Ctrl+, (فاصلة)",
                "error": "إذا واجهت مشكلة:\n1. تحقق من الاتصال بالإنترنت\n2. أعد تشغيل البرنامج\n3. تواصل مع الدعم الفني"
            }
            
            logging.debug("AIController setup completed successfully")
        except Exception as e:
            logging.error(f"Failed to setup AIController: {e}")
            self.error_occurred.emit("فشل في إعداد المساعد الذكي")

    def handle_input(self):
        """معالجة المدخلات من المستخدم"""
        if not hasattr(self.ai_dock, 'input_line'):
            return
            
        text = self.ai_dock.input_line.text().strip()
        if not text:
            return
            
        # إضافة رسالة المستخدم للتاريخ
        self.add_to_history("user", text)
        
        # مسح حقل الإدخال
        self.ai_dock.input_line.clear()
        
        # معالجة الرسالة
        self.process_message(text)

    def process_message(self, message: str):
        """معالجة الرسالة وإنشاء رد ذكي"""
        try:
            self.is_processing = True
            
            # تحليل الرسالة
            response = self.generate_response(message)
            
            # إضافة الرد للتاريخ
            self.add_to_history("assistant", response)
            
            # إرسال الرد للواجهة
            self.message_received.emit(response)
            
            # إرسال حدث للواجهة الرئيسية (اختياري)
            if self.event_bus:
                self.event_bus.emit(AppEvents.AI_MESSAGE_RECEIVED, {
                    "message": response,
                    "original_message": message
                })
                
        except Exception as e:
            logging.error(f"Error processing message: {e}")
            error_msg = "عذراً، حدث خطأ في معالجة رسالتك. يرجى المحاولة مرة أخرى."
            self.error_occurred.emit(error_msg)
        finally:
            self.is_processing = False

    def generate_response(self, message: str) -> str:
        """إنشاء رد ذكي بناءً على الرسالة"""
        message_lower = message.lower()
        
        # البحث عن كلمات مفتاحية
        if any(word in message_lower for word in ["مساعدة", "help", "مساعد", "كيف"]):
            return self.smart_responses.get("help", "كيف يمكنني مساعدتك؟")
        
        elif any(word in message_lower for word in ["تقرير", "report", "تقارير"]):
            return self.smart_responses.get("report", "يمكنني مساعدتك في إنشاء التقارير.")
        
        elif any(word in message_lower for word in ["نسخة", "backup", "احتياطي"]):
            return self.smart_responses.get("backup", "يمكنني مساعدتك في إنشاء نسخة احتياطية.")
        
        elif any(word in message_lower for word in ["إعدادات", "settings", "إعداد"]):
            return self.smart_responses.get("settings", "يمكنني مساعدتك في الإعدادات.")
        
        elif any(word in message_lower for word in ["خطأ", "error", "مشكلة", "عطل"]):
            return self.smart_responses.get("error", "يمكنني مساعدتك في حل المشاكل.")
        
        elif any(word in message_lower for word in ["مرحبا", "hello", "أهلا", "hi"]):
            return "مرحباً! كيف يمكنني مساعدتك اليوم؟ 😊"
        
        elif any(word in message_lower for word in ["شكرا", "thank", "thanks"]):
            return "العفو! سعيد بمساعدتك. هل هناك شيء آخر تحتاج إليه؟"
        
        else:
            return self.generate_general_response(message)

    def generate_general_response(self, message: str) -> str:
        """إنشاء رد عام ذكي"""
        responses = [
            "أفهم سؤالك. دعني أساعدك في ذلك.",
            "هذا سؤال جيد. يمكنني مساعدتك في هذا الأمر.",
            "أرى أنك تحتاج مساعدة. دعني أوضح لك الأمر.",
            "هذا موضوع مهم. سأحاول مساعدتك بأفضل طريقة ممكنة.",
            "أفهم ما تقصده. دعني أقدم لك الحل المناسب."
        ]
        
        import random
        return random.choice(responses)

    def add_to_history(self, sender: str, message: str):
        """إضافة رسالة لتاريخ المحادثة"""
        self.conversation_history.append({
            "sender": sender,
            "message": message,
            "timestamp": self.get_current_timestamp()
        })
        
        # الاحتفاظ بآخر 50 رسالة فقط
        if len(self.conversation_history) > 50:
            self.conversation_history = self.conversation_history[-50:]

    def get_current_timestamp(self) -> str:
        """الحصول على الطابع الزمني الحالي"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    def show_suggestion(self, text: str):
        """إظهار اقتراح ذكي في الشريط الجانبي"""
        try:
            if self.event_bus:
                self.event_bus.emit(AppEvents.SHOW_TOAST, {
                    "message": f"💡 {text}",
                    "type": "info"
                })
            
            self.suggestion_ready.emit(text)
            logging.info(f"AI suggestion shown: {text}")
        except Exception as e:
            logging.error(f"Error showing suggestion: {e}")

    def get_conversation_history(self) -> list:
        """الحصول على تاريخ المحادثة"""
        return self.conversation_history.copy()

    def clear_history(self):
        """مسح تاريخ المحادثة"""
        self.conversation_history.clear()
        logging.info("AI conversation history cleared")

    def is_busy(self) -> bool:
        """التحقق من كون المساعد مشغول"""
        return self.is_processing 