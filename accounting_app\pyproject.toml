[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "smart-accounting-app"
version = "1.0.0"
description = "Smart Accounting Application with AI Assistant"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "Smart Accounting Team", email = "<EMAIL>"}
]
maintainers = [
    {name = "Smart Accounting Team", email = "<EMAIL>"}
]
keywords = ["accounting", "finance", "business", "qt", "pyside6"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Financial and Insurance Industry",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Office/Business :: Financial :: Accounting",
]
requires-python = ">=3.8"
dependencies = [
    "PySide6",
    "pygame==2.5.2",
]

[project.optional-dependencies]
dev = [
    "pytest==7.4.3",
    "pytest-qt==4.2.0",
    "pytest-cov==4.1.0",
    "coverage==7.3.2",
    "black==23.11.0",
    "flake8==6.1.0",
    "isort==5.12.0",
    "mypy==1.7.1",
    "pre-commit==3.5.0",
    "jupyter==1.0.0",
    "ipython==8.17.2",
]
test = [
    "pytest==7.4.3",
    "pytest-qt==4.2.0",
    "pytest-cov==4.1.0",
]
docs = [
    "sphinx==7.2.6",
    "sphinx-rtd-theme==1.3.0",
]

[project.scripts]
smart-accounting = "accounting_app.main:main"

[project.urls]
Homepage = "https://github.com/smartaccounting/app"
Documentation = "https://smartaccounting.readthedocs.io/"
Repository = "https://github.com/smartaccounting/app"
"Bug Tracker" = "https://github.com/smartaccounting/app/issues"

[tool.setuptools.packages.find]
where = ["."]
include = ["accounting_app*"]
exclude = ["tests*", "docs*", "scripts*"]

[tool.setuptools.package-data]
"accounting_app" = [
    "*.qm", "*.ts", "*.svg", "*.wav", "*.qss",
    "translations/*.qm", "translations/*.ts",
    "resources/*.svg", "resources/*.wav",
    "ui/*.qss"
]

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["accounting_app"]

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.coverage.run]
source = ["accounting_app"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/migrations/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
] 