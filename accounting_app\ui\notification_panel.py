from PySide6.QtWidgets import QWidget, QVBoxLayout, QLabel, QFrame, QComboBox
from PySide6.QtCore import Qt, QPropertyAnimation
import logging
from accounting_app.core.events import AppEvents

class NotificationPanel(QWidget):
    def __init__(self, event_bus=None, language_controller=None, settings=None, parent=None):
        super().__init__(parent)
        self.event_bus = event_bus
        self.language_controller = language_controller
        self.settings = settings
        self.setObjectName("NotificationPanel")
        self.setFixedWidth(280)
        self.init_ui()
        # إذا احتجت event_bus أو language_controller اربطهم هنا
        if self.event_bus:
            self.event_bus.subscribe(AppEvents.NOTIFY, self.show_notification)

    def init_ui(self):
        self.layout = QVBoxLayout()
        self.layout.setContentsMargins(8, 8, 8, 8)
        self.layout.setSpacing(8)
        self.filter_box = QComboBox()
        self.filter_box.addItems([self.tr("الكل"), self.tr("تنبيه"), self.tr("نظام"), self.tr("AI")])
        self.filter_box.currentTextChanged.connect(self.filter_notifications)
        self.layout.addWidget(self.filter_box)
        self.setLayout(self.layout)
        self.notifications = []
        self.layout.addStretch()

    def add_notification(self, text, notif_type=None):
        if notif_type is None:
            notif_type = self.tr("تنبيه")
        notif = QLabel(text)
        notif.setObjectName("NotificationItem")
        notif.setWordWrap(True)
        notif.setFrameShape(QFrame.Panel)
        notif.setFrameShadow(QFrame.Raised)
        notif.setStyleSheet("background-color: #E5E7EB; color: #1F2937; border-radius: 8px; padding: 8px;")
        notif.setWindowOpacity(0)
        notif.type = notif_type
        self.notifications.append(notif)
        self.layout.insertWidget(self.layout.count() - 1, notif)
        anim = QPropertyAnimation(notif, b"windowOpacity")
        anim.setDuration(400)
        anim.setStartValue(0)
        anim.setEndValue(1)
        anim.start()
        self._last_anim = anim
        self.filter_notifications(self.filter_box.currentText())
        logging.info(f"Notification added to panel: {text} [{notif_type}]")

    def filter_notifications(self, notif_type):
        for notif in self.notifications:
            if notif_type == self.tr("الكل") or getattr(notif, 'type', self.tr("تنبيه")) == notif_type:
                notif.show()
            else:
                notif.hide()
        logging.info(f"Notifications filtered by type: {notif_type}")

    def show_notification(self, payload: dict):
        notif_type = payload.get("type", "info")
        title = payload.get("title", "")
        message = payload.get("message", "")
        full_message = f"<b>{title}</b><br>{message}" if title else message
        self.add_notification(full_message, notif_type)

    def reload_texts(self, lang_code=None):
        """إعادة تحميل النصوص عند تغيير اللغة"""
        try:
            # حفظ الفلتر الحالي
            current_filter = self.filter_box.currentText()
            
            # تحديث قائمة الفلاتر
            self.filter_box.clear()
            self.filter_box.addItems([
                self.tr("الكل"), 
                self.tr("تنبيه"), 
                self.tr("نظام"), 
                self.tr("AI")
            ])
            
            # استعادة الفلتر المحدد
            index = self.filter_box.findText(current_filter)
            if index >= 0:
                self.filter_box.setCurrentIndex(index)
            
            logging.info(f"NotificationPanel texts reloaded for language: {lang_code or 'current'}")
        except Exception as e:
            logging.exception("فشل تحديث نصوص لوحة الإشعارات") 