2025-07-13 22:04:30 [INFO] SmartAccountingApp:123: ============================================================
2025-07-13 22:04:30 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-13 22:04:30 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-13 22:04:30
2025-07-13 22:04:30 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-13 22:04:30 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\نسخ تطوير gaea\مجلد جديد (4)\l0.17
2025-07-13 22:04:30 [INFO] SmartAccountingApp:128: ============================================================
2025-07-13 22:04:41 [INFO] SmartAccountingApp:123: ============================================================
2025-07-13 22:04:41 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-13 22:04:41 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-13 22:04:41
2025-07-13 22:04:41 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-13 22:04:41 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\نسخ تطوير gaea\مجلد جديد (4)\l0.17
2025-07-13 22:04:41 [INFO] SmartAccountingApp:128: ============================================================
2025-07-14 10:47:14 [INFO] SmartAccountingApp:123: ============================================================
2025-07-14 10:47:14 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-14 10:47:14 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-14 10:47:14
2025-07-14 10:47:14 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-14 10:47:14 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.17
2025-07-14 10:47:14 [INFO] SmartAccountingApp:128: ============================================================
2025-07-14 14:57:30 [INFO] SmartAccountingApp:123: ============================================================
2025-07-14 14:57:30 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-14 14:57:30 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-14 14:57:30
2025-07-14 14:57:30 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-14 14:57:30 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.17
2025-07-14 14:57:30 [INFO] SmartAccountingApp:128: ============================================================
2025-07-14 18:06:58 [INFO] SmartAccountingApp:123: ============================================================
2025-07-14 18:06:58 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-14 18:06:58 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-14 18:06:58
2025-07-14 18:06:58 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-14 18:06:58 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.17
2025-07-14 18:06:58 [INFO] SmartAccountingApp:128: ============================================================
2025-07-15 16:58:13 [INFO] SmartAccountingApp:123: ============================================================
2025-07-15 16:58:13 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-15 16:58:13 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-15 16:58:13
2025-07-15 16:58:13 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-15 16:58:13 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.25
2025-07-15 16:58:13 [INFO] SmartAccountingApp:128: ============================================================
2025-07-15 18:27:57 [INFO] SmartAccountingApp:123: ============================================================
2025-07-15 18:27:57 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-15 18:27:57 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-15 18:27:57
2025-07-15 18:27:57 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-15 18:27:57 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.26
2025-07-15 18:27:57 [INFO] SmartAccountingApp:128: ============================================================
2025-07-15 18:29:33 [INFO] SmartAccountingApp:123: ============================================================
2025-07-15 18:29:33 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-15 18:29:33 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-15 18:29:33
2025-07-15 18:29:33 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-15 18:29:33 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.26
2025-07-15 18:29:33 [INFO] SmartAccountingApp:128: ============================================================
2025-07-15 18:32:04 [INFO] SmartAccountingApp:123: ============================================================
2025-07-15 18:32:04 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-15 18:32:04 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-15 18:32:04
2025-07-15 18:32:04 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-15 18:32:04 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.26
2025-07-15 18:32:04 [INFO] SmartAccountingApp:128: ============================================================
2025-07-15 18:32:12 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: could not convert string to float: ''
NoneType: None
2025-07-15 18:44:36 [INFO] SmartAccountingApp:123: ============================================================
2025-07-15 18:44:36 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-15 18:44:36 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-15 18:44:36
2025-07-15 18:44:36 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-15 18:44:36 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.26
2025-07-15 18:44:36 [INFO] SmartAccountingApp:128: ============================================================
2025-07-15 18:47:11 [INFO] SmartAccountingApp:123: ============================================================
2025-07-15 18:47:11 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-15 18:47:11 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-15 18:47:11
2025-07-15 18:47:11 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-15 18:47:11 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.26
2025-07-15 18:47:11 [INFO] SmartAccountingApp:128: ============================================================
2025-07-15 18:51:29 [INFO] SmartAccountingApp:123: ============================================================
2025-07-15 18:51:29 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-15 18:51:29 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-15 18:51:29
2025-07-15 18:51:29 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-15 18:51:29 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.26
2025-07-15 18:51:29 [INFO] SmartAccountingApp:128: ============================================================
2025-07-15 18:54:34 [INFO] SmartAccountingApp:123: ============================================================
2025-07-15 18:54:34 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-15 18:54:34 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-15 18:54:34
2025-07-15 18:54:34 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-15 18:54:34 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.26
2025-07-15 18:54:34 [INFO] SmartAccountingApp:128: ============================================================
2025-07-15 18:57:12 [INFO] SmartAccountingApp:123: ============================================================
2025-07-15 18:57:12 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-15 18:57:12 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-15 18:57:12
2025-07-15 18:57:12 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-15 18:57:12 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.26
2025-07-15 18:57:12 [INFO] SmartAccountingApp:128: ============================================================
2025-07-15 18:59:08 [INFO] SmartAccountingApp:123: ============================================================
2025-07-15 18:59:08 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-15 18:59:08 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-15 18:59:08
2025-07-15 18:59:08 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-15 18:59:08 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.26
2025-07-15 18:59:08 [INFO] SmartAccountingApp:128: ============================================================
2025-07-15 19:00:55 [INFO] SmartAccountingApp:123: ============================================================
2025-07-15 19:00:55 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-15 19:00:55 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-15 19:00:55
2025-07-15 19:00:55 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-15 19:00:55 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.26
2025-07-15 19:00:55 [INFO] SmartAccountingApp:128: ============================================================
2025-07-15 19:02:11 [INFO] SmartAccountingApp:123: ============================================================
2025-07-15 19:02:11 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-15 19:02:11 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-15 19:02:11
2025-07-15 19:02:11 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-15 19:02:11 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.26
2025-07-15 19:02:11 [INFO] SmartAccountingApp:128: ============================================================
2025-07-15 19:03:40 [INFO] SmartAccountingApp:123: ============================================================
2025-07-15 19:03:40 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-15 19:03:40 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-15 19:03:40
2025-07-15 19:03:40 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-15 19:03:40 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.26
2025-07-15 19:03:40 [INFO] SmartAccountingApp:128: ============================================================
2025-07-15 19:14:31 [INFO] SmartAccountingApp:123: ============================================================
2025-07-15 19:14:31 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-15 19:14:31 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-15 19:14:31
2025-07-15 19:14:31 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-15 19:14:31 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.26
2025-07-15 19:14:31 [INFO] SmartAccountingApp:128: ============================================================
2025-07-15 19:47:53 [INFO] SmartAccountingApp:123: ============================================================
2025-07-15 19:47:53 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-15 19:47:53 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-15 19:47:53
2025-07-15 19:47:53 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-15 19:47:53 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.26
2025-07-15 19:47:53 [INFO] SmartAccountingApp:128: ============================================================
2025-07-15 20:07:01 [INFO] SmartAccountingApp:123: ============================================================
2025-07-15 20:07:01 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-15 20:07:01 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-15 20:07:01
2025-07-15 20:07:01 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-15 20:07:01 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.26
2025-07-15 20:07:01 [INFO] SmartAccountingApp:128: ============================================================
2025-07-15 20:08:11 [INFO] SmartAccountingApp:123: ============================================================
2025-07-15 20:08:11 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-15 20:08:11 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-15 20:08:11
2025-07-15 20:08:11 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-15 20:08:11 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.26
2025-07-15 20:08:11 [INFO] SmartAccountingApp:128: ============================================================
2025-07-15 20:12:13 [INFO] SmartAccountingApp:123: ============================================================
2025-07-15 20:12:13 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-15 20:12:13 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-15 20:12:13
2025-07-15 20:12:13 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-15 20:12:13 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.26
2025-07-15 20:12:13 [INFO] SmartAccountingApp:128: ============================================================
2025-07-15 20:13:26 [INFO] SmartAccountingApp:123: ============================================================
2025-07-15 20:13:26 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-15 20:13:26 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-15 20:13:26
2025-07-15 20:13:26 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-15 20:13:26 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.26
2025-07-15 20:13:26 [INFO] SmartAccountingApp:128: ============================================================
2025-07-15 20:18:13 [INFO] SmartAccountingApp:123: ============================================================
2025-07-15 20:18:13 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-15 20:18:13 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-15 20:18:13
2025-07-15 20:18:13 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-15 20:18:13 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.26
2025-07-15 20:18:13 [INFO] SmartAccountingApp:128: ============================================================
2025-07-15 20:21:29 [INFO] SmartAccountingApp:123: ============================================================
2025-07-15 20:21:29 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-15 20:21:29 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-15 20:21:29
2025-07-15 20:21:29 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-15 20:21:29 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.26
2025-07-15 20:21:29 [INFO] SmartAccountingApp:128: ============================================================
2025-07-15 20:25:57 [INFO] SmartAccountingApp:123: ============================================================
2025-07-15 20:25:57 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-15 20:25:57 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-15 20:25:57
2025-07-15 20:25:57 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-15 20:25:57 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.26
2025-07-15 20:25:57 [INFO] SmartAccountingApp:128: ============================================================
2025-07-15 20:30:17 [INFO] SmartAccountingApp:123: ============================================================
2025-07-15 20:30:17 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-15 20:30:17 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-15 20:30:17
2025-07-15 20:30:17 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-15 20:30:17 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.26
2025-07-15 20:30:17 [INFO] SmartAccountingApp:128: ============================================================
2025-07-15 20:32:36 [INFO] SmartAccountingApp:123: ============================================================
2025-07-15 20:32:36 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-15 20:32:36 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-15 20:32:36
2025-07-15 20:32:36 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-15 20:32:36 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.26
2025-07-15 20:32:36 [INFO] SmartAccountingApp:128: ============================================================
2025-07-15 20:34:08 [INFO] SmartAccountingApp:123: ============================================================
2025-07-15 20:34:08 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-15 20:34:08 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-15 20:34:08
2025-07-15 20:34:08 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-15 20:34:08 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.26
2025-07-15 20:34:08 [INFO] SmartAccountingApp:128: ============================================================
2025-07-15 20:34:28 [INFO] SmartAccountingApp:123: ============================================================
2025-07-15 20:34:28 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-15 20:34:28 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-15 20:34:28
2025-07-15 20:34:28 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-15 20:34:28 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.26
2025-07-15 20:34:28 [INFO] SmartAccountingApp:128: ============================================================
2025-07-15 20:40:28 [INFO] SmartAccountingApp:123: ============================================================
2025-07-15 20:40:28 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-15 20:40:28 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-15 20:40:28
2025-07-15 20:40:28 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-15 20:40:28 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.26
2025-07-15 20:40:28 [INFO] SmartAccountingApp:128: ============================================================
2025-07-15 20:44:59 [INFO] SmartAccountingApp:123: ============================================================
2025-07-15 20:44:59 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-15 20:44:59 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-15 20:44:59
2025-07-15 20:44:59 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-15 20:44:59 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.26
2025-07-15 20:44:59 [INFO] SmartAccountingApp:128: ============================================================
2025-07-15 20:47:30 [INFO] SmartAccountingApp:123: ============================================================
2025-07-15 20:47:30 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-15 20:47:30 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-15 20:47:30
2025-07-15 20:47:30 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-15 20:47:30 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.26
2025-07-15 20:47:30 [INFO] SmartAccountingApp:128: ============================================================
2025-07-15 20:49:38 [INFO] SmartAccountingApp:123: ============================================================
2025-07-15 20:49:38 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-15 20:49:38 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-15 20:49:38
2025-07-15 20:49:38 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-15 20:49:38 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.26
2025-07-15 20:49:38 [INFO] SmartAccountingApp:128: ============================================================
2025-07-15 20:51:40 [INFO] SmartAccountingApp:123: ============================================================
2025-07-15 20:51:40 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-15 20:51:40 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-15 20:51:40
2025-07-15 20:51:40 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-15 20:51:40 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.26
2025-07-15 20:51:40 [INFO] SmartAccountingApp:128: ============================================================
2025-07-15 20:52:54 [INFO] SmartAccountingApp:123: ============================================================
2025-07-15 20:52:54 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-15 20:52:54 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-15 20:52:54
2025-07-15 20:52:54 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-15 20:52:54 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.26
2025-07-15 20:52:54 [INFO] SmartAccountingApp:128: ============================================================
2025-07-15 20:53:02 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'active_check'
NoneType: None
2025-07-15 20:53:20 [INFO] SmartAccountingApp:123: ============================================================
2025-07-15 20:53:20 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-15 20:53:20 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-15 20:53:20
2025-07-15 20:53:20 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-15 20:53:20 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.26
2025-07-15 20:53:20 [INFO] SmartAccountingApp:128: ============================================================
2025-07-15 20:56:00 [INFO] SmartAccountingApp:123: ============================================================
2025-07-15 20:56:00 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-15 20:56:00 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-15 20:56:00
2025-07-15 20:56:00 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-15 20:56:00 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.26
2025-07-15 20:56:00 [INFO] SmartAccountingApp:128: ============================================================
2025-07-15 21:00:34 [INFO] SmartAccountingApp:123: ============================================================
2025-07-15 21:00:34 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-15 21:00:34 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-15 21:00:34
2025-07-15 21:00:34 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-15 21:00:34 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.26
2025-07-15 21:00:34 [INFO] SmartAccountingApp:128: ============================================================
2025-07-15 21:03:27 [INFO] SmartAccountingApp:123: ============================================================
2025-07-15 21:03:27 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-15 21:03:27 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-15 21:03:27
2025-07-15 21:03:27 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-15 21:03:27 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.26
2025-07-15 21:03:27 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 13:26:16 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 13:26:16 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 13:26:16 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 13:26:16
2025-07-16 13:26:16 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 13:26:16 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.26
2025-07-16 13:26:16 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 13:31:47 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 13:31:47 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 13:31:47 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 13:31:47
2025-07-16 13:31:47 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 13:31:47 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.26
2025-07-16 13:31:47 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 13:35:58 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 13:35:58 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 13:35:58 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 13:35:58
2025-07-16 13:35:58 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 13:35:58 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.26
2025-07-16 13:35:58 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 13:36:01 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: cannot access local variable 'QPushButton' where it is not associated with a value
NoneType: None
2025-07-16 13:36:03 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: cannot access local variable 'QPushButton' where it is not associated with a value
NoneType: None
2025-07-16 13:36:03 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: cannot access local variable 'QPushButton' where it is not associated with a value
NoneType: None
2025-07-16 13:36:03 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: cannot access local variable 'QPushButton' where it is not associated with a value
NoneType: None
2025-07-16 13:36:04 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: cannot access local variable 'QPushButton' where it is not associated with a value
NoneType: None
2025-07-16 13:36:05 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: cannot access local variable 'QPushButton' where it is not associated with a value
NoneType: None
2025-07-16 13:36:30 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 13:36:30 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 13:36:30 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 13:36:30
2025-07-16 13:36:30 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 13:36:30 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.26
2025-07-16 13:36:30 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 13:36:41 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: cannot access local variable 'QPushButton' where it is not associated with a value
NoneType: None
2025-07-16 13:36:42 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: cannot access local variable 'QPushButton' where it is not associated with a value
NoneType: None
2025-07-16 13:37:40 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 13:37:40 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 13:37:40 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 13:37:40
2025-07-16 13:37:40 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 13:37:40 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.26
2025-07-16 13:37:40 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 13:37:43 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: cannot access local variable 'QPushButton' where it is not associated with a value
NoneType: None
2025-07-16 13:38:17 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 13:38:17 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 13:38:17 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 13:38:17
2025-07-16 13:38:17 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 13:38:17 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.26
2025-07-16 13:38:17 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 13:38:19 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: cannot access local variable 'QPushButton' where it is not associated with a value
NoneType: None
2025-07-16 13:38:20 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: cannot access local variable 'QPushButton' where it is not associated with a value
NoneType: None
2025-07-16 13:38:20 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: cannot access local variable 'QPushButton' where it is not associated with a value
NoneType: None
2025-07-16 13:38:20 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: cannot access local variable 'QPushButton' where it is not associated with a value
NoneType: None
2025-07-16 13:38:21 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: cannot access local variable 'QPushButton' where it is not associated with a value
NoneType: None
2025-07-16 13:38:21 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: cannot access local variable 'QPushButton' where it is not associated with a value
NoneType: None
2025-07-16 13:38:33 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 13:38:33 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 13:38:33 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 13:38:33
2025-07-16 13:38:33 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 13:38:33 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.26
2025-07-16 13:38:33 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 13:38:38 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: cannot access local variable 'QPushButton' where it is not associated with a value
NoneType: None
2025-07-16 13:38:38 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: cannot access local variable 'QPushButton' where it is not associated with a value
NoneType: None
2025-07-16 13:38:38 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: cannot access local variable 'QPushButton' where it is not associated with a value
NoneType: None
2025-07-16 13:38:38 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: cannot access local variable 'QPushButton' where it is not associated with a value
NoneType: None
2025-07-16 13:40:31 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 13:40:31 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 13:40:31 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 13:40:31
2025-07-16 13:40:31 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 13:40:31 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.26
2025-07-16 13:40:31 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 13:42:07 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 13:42:07 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 13:42:07 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 13:42:07
2025-07-16 13:42:07 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 13:42:07 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.26
2025-07-16 13:42:07 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 13:42:10 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'name_edit'
NoneType: None
2025-07-16 13:42:11 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'name_edit'
NoneType: None
2025-07-16 13:42:11 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'name_edit'
NoneType: None
2025-07-16 13:42:12 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'name_edit'
NoneType: None
2025-07-16 13:42:12 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'name_edit'
NoneType: None
2025-07-16 13:43:36 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 13:43:36 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 13:43:36 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 13:43:36
2025-07-16 13:43:36 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 13:43:36 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.26
2025-07-16 13:43:36 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 14:11:12 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 14:11:12 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 14:11:12 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 14:11:12
2025-07-16 14:11:12 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 14:11:12 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.31
2025-07-16 14:11:12 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 14:25:02 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 14:25:02 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 14:25:02 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 14:25:02
2025-07-16 14:25:02 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 14:25:02 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.31
2025-07-16 14:25:02 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 14:28:39 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: cannot import name 'QPropertyAnimation' from 'PySide6.QtWidgets' (G:\Gaea\GaeaDev\1l0.00\l0.31\.venv\Lib\site-packages\PySide6\QtWidgets.pyd)
NoneType: None
2025-07-16 14:31:54 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: cannot import name 'QPropertyAnimation' from 'PySide6.QtWidgets' (G:\Gaea\GaeaDev\1l0.00\l0.31\.venv\Lib\site-packages\PySide6\QtWidgets.pyd)
NoneType: None
2025-07-16 14:32:07 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: cannot import name 'QPropertyAnimation' from 'PySide6.QtWidgets' (G:\Gaea\GaeaDev\1l0.00\l0.31\.venv\Lib\site-packages\PySide6\QtWidgets.pyd)
NoneType: None
2025-07-16 14:32:55 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 14:32:55 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 14:32:55 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 14:32:55
2025-07-16 14:32:55 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 14:32:55 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.31
2025-07-16 14:32:55 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 14:32:57 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'name_edit'
NoneType: None
2025-07-16 14:32:59 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'name_edit'
NoneType: None
2025-07-16 14:32:59 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'name_edit'
NoneType: None
2025-07-16 14:33:02 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'name_edit'
NoneType: None
2025-07-16 14:33:03 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'name_edit'
NoneType: None
2025-07-16 14:33:17 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 14:33:17 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 14:33:17 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 14:33:17
2025-07-16 14:33:17 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 14:33:17 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.31
2025-07-16 14:33:17 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 14:33:21 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'name_edit'
NoneType: None
2025-07-16 14:33:21 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'name_edit'
NoneType: None
2025-07-16 14:34:15 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 14:34:15 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 14:34:15 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 14:34:15
2025-07-16 14:34:15 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 14:34:15 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.31
2025-07-16 14:34:15 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 14:34:39 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 14:34:39 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 14:34:39 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 14:34:39
2025-07-16 14:34:39 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 14:34:39 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.31
2025-07-16 14:34:39 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 14:36:05 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 14:36:05 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 14:36:05 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 14:36:05
2025-07-16 14:36:05 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 14:36:05 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.31
2025-07-16 14:36:05 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 15:00:00 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 15:00:00 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 15:00:00 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 15:00:00
2025-07-16 15:00:00 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 15:00:00 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.31
2025-07-16 15:00:00 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 15:01:48 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 15:01:48 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 15:01:48 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 15:01:48
2025-07-16 15:01:48 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 15:01:48 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.31
2025-07-16 15:01:48 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 15:05:10 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 15:05:10 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 15:05:10 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 15:05:10
2025-07-16 15:05:10 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 15:05:10 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.31
2025-07-16 15:05:10 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 15:08:54 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 15:08:54 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 15:08:54 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 15:08:54
2025-07-16 15:08:54 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 15:08:54 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.31
2025-07-16 15:08:54 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 15:11:08 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 15:11:08 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 15:11:08 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 15:11:08
2025-07-16 15:11:08 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 15:11:08 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.31
2025-07-16 15:11:08 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 15:34:24 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 15:34:24 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 15:34:24 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 15:34:24
2025-07-16 15:34:24 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 15:34:24 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.34
2025-07-16 15:34:24 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 15:38:08 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 15:38:08 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 15:38:08 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 15:38:08
2025-07-16 15:38:08 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 15:38:08 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.34
2025-07-16 15:38:08 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 15:45:57 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 15:45:57 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 15:45:57 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 15:45:57
2025-07-16 15:45:57 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 15:45:57 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.34
2025-07-16 15:45:57 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 15:49:47 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 15:49:47 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 15:49:47 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 15:49:47
2025-07-16 15:49:47 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 15:49:47 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.34
2025-07-16 15:49:47 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 15:49:50 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute '_create_arrow_icon'
NoneType: None
2025-07-16 15:49:52 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute '_create_arrow_icon'
NoneType: None
2025-07-16 15:49:52 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute '_create_arrow_icon'
NoneType: None
2025-07-16 15:49:52 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute '_create_arrow_icon'
NoneType: None
2025-07-16 15:49:53 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute '_create_arrow_icon'
NoneType: None
2025-07-16 15:49:53 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute '_create_arrow_icon'
NoneType: None
2025-07-16 15:49:53 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute '_create_arrow_icon'
NoneType: None
2025-07-16 15:49:54 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute '_create_arrow_icon'
NoneType: None
2025-07-16 15:52:09 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 15:52:09 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 15:52:09 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 15:52:09
2025-07-16 15:52:09 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 15:52:09 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.34
2025-07-16 15:52:09 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 15:52:13 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: name 'QPoint' is not defined
NoneType: None
2025-07-16 15:52:14 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: name 'QPoint' is not defined
NoneType: None
2025-07-16 15:52:14 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: name 'QPoint' is not defined
NoneType: None
2025-07-16 15:52:14 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: name 'QPoint' is not defined
NoneType: None
2025-07-16 15:52:45 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 15:52:45 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 15:52:45 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 15:52:45
2025-07-16 15:52:45 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 15:52:45 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.34
2025-07-16 15:52:45 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 15:56:33 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 15:56:33 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 15:56:33 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 15:56:33
2025-07-16 15:56:33 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 15:56:33 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.34
2025-07-16 15:56:33 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 16:00:37 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 16:00:37 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 16:00:37 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 16:00:37
2025-07-16 16:00:37 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 16:00:37 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.34
2025-07-16 16:00:37 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 16:01:49 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 16:01:49 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 16:01:49 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 16:01:49
2025-07-16 16:01:49 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 16:01:49 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.34
2025-07-16 16:01:49 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 16:03:37 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 16:03:37 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 16:03:37 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 16:03:37
2025-07-16 16:03:37 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 16:03:37 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.34
2025-07-16 16:03:37 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 16:06:00 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 16:06:00 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 16:06:00 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 16:06:00
2025-07-16 16:06:00 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 16:06:00 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.34
2025-07-16 16:06:00 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 16:08:19 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 16:08:19 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 16:08:19 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 16:08:19
2025-07-16 16:08:19 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 16:08:19 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.34
2025-07-16 16:08:19 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 16:10:31 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 16:10:31 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 16:10:31 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 16:10:31
2025-07-16 16:10:31 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 16:10:31 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.34
2025-07-16 16:10:31 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 16:11:26 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 16:11:26 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 16:11:26 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 16:11:26
2025-07-16 16:11:26 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 16:11:26 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.34
2025-07-16 16:11:26 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 16:13:44 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 16:13:44 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 16:13:44 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 16:13:44
2025-07-16 16:13:44 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 16:13:44 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.34
2025-07-16 16:13:44 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 16:15:38 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 16:15:38 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 16:15:38 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 16:15:38
2025-07-16 16:15:38 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 16:15:38 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.34
2025-07-16 16:15:38 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 16:15:41 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'carton_qty_edit'
NoneType: None
2025-07-16 16:15:41 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'carton_qty_edit'
NoneType: None
2025-07-16 16:15:42 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'carton_qty_edit'
NoneType: None
2025-07-16 16:15:42 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'carton_qty_edit'
NoneType: None
2025-07-16 16:16:53 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 16:16:53 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 16:16:53 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 16:16:53
2025-07-16 16:16:53 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 16:16:53 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.34
2025-07-16 16:16:53 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 16:16:55 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'carton_qty_edit'
NoneType: None
2025-07-16 16:16:56 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'carton_qty_edit'
NoneType: None
2025-07-16 16:16:56 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'carton_qty_edit'
NoneType: None
2025-07-16 16:16:57 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'carton_qty_edit'
NoneType: None
2025-07-16 16:18:45 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 16:18:45 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 16:18:45 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 16:18:45
2025-07-16 16:18:45 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 16:18:45 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.34
2025-07-16 16:18:45 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 16:18:48 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'qty_edit'
NoneType: None
2025-07-16 16:18:48 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'qty_edit'
NoneType: None
2025-07-16 16:18:48 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'qty_edit'
NoneType: None
2025-07-16 16:18:49 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'qty_edit'
NoneType: None
2025-07-16 16:18:49 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'qty_edit'
NoneType: None
2025-07-16 16:18:49 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'qty_edit'
NoneType: None
2025-07-16 16:18:49 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'qty_edit'
NoneType: None
2025-07-16 16:18:50 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'qty_edit'
NoneType: None
2025-07-16 16:18:50 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'qty_edit'
NoneType: None
2025-07-16 16:18:50 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'qty_edit'
NoneType: None
2025-07-16 16:18:50 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'qty_edit'
NoneType: None
2025-07-16 16:18:51 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'qty_edit'
NoneType: None
2025-07-16 16:18:51 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'qty_edit'
NoneType: None
2025-07-16 16:18:51 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'qty_edit'
NoneType: None
2025-07-16 16:18:52 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'qty_edit'
NoneType: None
2025-07-16 16:18:52 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'qty_edit'
NoneType: None
2025-07-16 16:19:14 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 16:19:14 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 16:19:14 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 16:19:14
2025-07-16 16:19:14 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 16:19:14 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.34
2025-07-16 16:19:14 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 16:19:17 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'qty_edit'
NoneType: None
2025-07-16 16:19:18 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'qty_edit'
NoneType: None
2025-07-16 16:19:18 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'qty_edit'
NoneType: None
2025-07-16 16:19:18 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'qty_edit'
NoneType: None
2025-07-16 16:19:21 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 16:19:21 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 16:19:21 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 16:19:21
2025-07-16 16:19:21 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 16:19:21 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.34
2025-07-16 16:19:21 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 16:20:15 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 16:20:15 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 16:20:15 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 16:20:15
2025-07-16 16:20:15 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 16:20:15 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.34
2025-07-16 16:20:15 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 16:20:18 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'qty_edit'
NoneType: None
2025-07-16 16:20:19 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'qty_edit'
NoneType: None
2025-07-16 16:20:19 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'qty_edit'
NoneType: None
2025-07-16 16:20:30 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 16:20:30 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 16:20:30 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 16:20:30
2025-07-16 16:20:30 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 16:20:30 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.34
2025-07-16 16:20:30 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 16:20:36 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'qty_edit'
NoneType: None
2025-07-16 16:20:36 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'qty_edit'
NoneType: None
2025-07-16 16:20:37 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'qty_edit'
NoneType: None
2025-07-16 16:20:37 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'qty_edit'
NoneType: None
2025-07-16 16:21:30 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 16:21:30 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 16:21:30 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 16:21:30
2025-07-16 16:21:30 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 16:21:30 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.34
2025-07-16 16:21:30 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 16:23:19 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 16:23:19 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 16:23:19 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 16:23:19
2025-07-16 16:23:19 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 16:23:19 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.34
2025-07-16 16:23:19 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 16:25:28 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 16:25:28 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 16:25:28 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 16:25:28
2025-07-16 16:25:28 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 16:25:28 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.34
2025-07-16 16:25:28 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 16:29:27 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 16:29:27 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 16:29:27 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 16:29:27
2025-07-16 16:29:27 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 16:29:27 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.34
2025-07-16 16:29:27 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 17:22:40 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 17:22:40 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 17:22:40 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 17:22:40
2025-07-16 17:22:40 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 17:22:40 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.34
2025-07-16 17:22:40 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 17:22:49 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'warehouse_advanced_widget'
NoneType: None
2025-07-16 17:22:50 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'warehouse_advanced_widget'
NoneType: None
2025-07-16 17:22:51 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'warehouse_advanced_widget'
NoneType: None
2025-07-16 17:22:51 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'warehouse_advanced_widget'
NoneType: None
2025-07-16 17:22:51 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'warehouse_advanced_widget'
NoneType: None
2025-07-16 17:22:52 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'warehouse_advanced_widget'
NoneType: None
2025-07-16 17:22:52 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'warehouse_advanced_widget'
NoneType: None
2025-07-16 17:23:13 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 17:23:13 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 17:23:13 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 17:23:13
2025-07-16 17:23:13 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 17:23:13 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.34
2025-07-16 17:23:13 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 17:23:16 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'warehouse_advanced_widget'
NoneType: None
2025-07-16 17:23:17 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'warehouse_advanced_widget'
NoneType: None
2025-07-16 17:23:17 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'warehouse_advanced_widget'
NoneType: None
2025-07-16 17:23:52 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 17:23:52 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 17:23:52 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 17:23:52
2025-07-16 17:23:52 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 17:23:52 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.34
2025-07-16 17:23:52 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 17:26:07 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 17:26:07 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 17:26:07 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 17:26:07
2025-07-16 17:26:07 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 17:26:07 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.34
2025-07-16 17:26:07 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 17:27:31 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 17:27:31 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 17:27:31 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 17:27:31
2025-07-16 17:27:31 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 17:27:31 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.34
2025-07-16 17:27:31 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 17:30:35 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 17:30:35 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 17:30:35 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 17:30:35
2025-07-16 17:30:35 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 17:30:35 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.34
2025-07-16 17:30:35 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 17:31:45 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 17:31:45 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 17:31:45 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 17:31:45
2025-07-16 17:31:45 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 17:31:45 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.34
2025-07-16 17:31:45 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 17:32:45 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 17:32:45 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 17:32:45 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 17:32:45
2025-07-16 17:32:45 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 17:32:45 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.34
2025-07-16 17:32:45 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 17:34:20 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 17:34:20 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 17:34:20 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 17:34:20
2025-07-16 17:34:20 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 17:34:20 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.34
2025-07-16 17:34:20 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 17:34:33 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 17:34:33 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 17:34:33 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 17:34:33
2025-07-16 17:34:33 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 17:34:33 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.34
2025-07-16 17:34:33 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 17:35:33 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 17:35:33 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 17:35:33 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 17:35:33
2025-07-16 17:35:33 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 17:35:33 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.34
2025-07-16 17:35:33 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 17:36:53 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 17:36:53 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 17:36:53 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 17:36:53
2025-07-16 17:36:53 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 17:36:53 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.34
2025-07-16 17:36:53 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 17:37:53 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 17:37:53 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 17:37:53 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 17:37:53
2025-07-16 17:37:53 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 17:37:53 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.34
2025-07-16 17:37:53 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 17:38:51 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 17:38:51 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 17:38:51 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 17:38:51
2025-07-16 17:38:51 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 17:38:51 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.34
2025-07-16 17:38:51 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 17:38:56 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 17:38:56 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 17:38:56 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 17:38:56
2025-07-16 17:38:56 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 17:38:56 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.34
2025-07-16 17:38:56 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 17:39:02 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 17:39:02 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 17:39:02 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 17:39:02
2025-07-16 17:39:02 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 17:39:02 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.34
2025-07-16 17:39:02 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 17:41:01 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 17:41:01 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 17:41:01 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 17:41:01
2025-07-16 17:41:01 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 17:41:01 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.34
2025-07-16 17:41:01 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 17:41:04 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: cannot access local variable 'QWidget' where it is not associated with a value
NoneType: None
2025-07-16 17:41:06 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: cannot access local variable 'QWidget' where it is not associated with a value
NoneType: None
2025-07-16 17:41:06 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: cannot access local variable 'QWidget' where it is not associated with a value
NoneType: None
2025-07-16 17:41:06 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: cannot access local variable 'QWidget' where it is not associated with a value
NoneType: None
2025-07-16 17:41:52 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 17:41:52 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 17:41:52 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 17:41:52
2025-07-16 17:41:52 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 17:41:52 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.34
2025-07-16 17:41:52 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 17:43:59 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 17:43:59 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 17:43:59 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 17:43:59
2025-07-16 17:43:59 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 17:43:59 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.34
2025-07-16 17:43:59 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 17:45:15 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 17:45:15 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 17:45:15 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 17:45:15
2025-07-16 17:45:15 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 17:45:15 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.34
2025-07-16 17:45:15 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 17:46:22 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 17:46:22 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 17:46:22 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 17:46:22
2025-07-16 17:46:22 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 17:46:22 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.34
2025-07-16 17:46:22 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 17:48:17 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 17:48:17 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 17:48:17 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 17:48:17
2025-07-16 17:48:17 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 17:48:17 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.34
2025-07-16 17:48:17 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 17:49:29 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 17:49:29 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 17:49:29 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 17:49:29
2025-07-16 17:49:29 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 17:49:29 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.34
2025-07-16 17:49:29 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 17:50:56 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 17:50:56 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 17:50:56 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 17:50:56
2025-07-16 17:50:56 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 17:50:56 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.34
2025-07-16 17:50:56 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 17:52:19 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 17:52:19 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 17:52:19 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 17:52:19
2025-07-16 17:52:19 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 17:52:19 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.34
2025-07-16 17:52:19 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 17:53:36 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 17:53:36 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 17:53:36 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 17:53:36
2025-07-16 17:53:36 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 17:53:36 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.34
2025-07-16 17:53:36 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 18:11:00 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 18:11:00 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 18:11:00 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 18:11:00
2025-07-16 18:11:00 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 18:11:00 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.38
2025-07-16 18:11:00 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 18:12:53 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 18:12:53 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 18:12:53 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 18:12:53
2025-07-16 18:12:53 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 18:12:53 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.38
2025-07-16 18:12:53 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 18:15:03 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 18:15:03 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 18:15:03 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 18:15:03
2025-07-16 18:15:03 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 18:15:03 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.38
2025-07-16 18:15:03 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 18:15:25 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'ProductDialog' object has no attribute 'description_edit'
NoneType: None
2025-07-16 18:17:30 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 18:17:30 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 18:17:30 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 18:17:30
2025-07-16 18:17:30 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 18:17:30 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.38
2025-07-16 18:17:30 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 18:18:59 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 18:18:59 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 18:18:59 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 18:18:59
2025-07-16 18:18:59 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 18:18:59 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.38
2025-07-16 18:18:59 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 18:19:57 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 18:19:57 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 18:19:57 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 18:19:57
2025-07-16 18:19:57 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 18:19:57 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.38
2025-07-16 18:19:57 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 18:23:44 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 18:23:44 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 18:23:44 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 18:23:44
2025-07-16 18:23:44 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 18:23:44 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.38
2025-07-16 18:23:44 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 18:25:42 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 18:25:42 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 18:25:42 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 18:25:42
2025-07-16 18:25:42 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 18:25:42 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.38
2025-07-16 18:25:42 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 18:26:52 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 18:26:52 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 18:26:52 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 18:26:52
2025-07-16 18:26:52 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 18:26:52 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.38
2025-07-16 18:26:52 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 18:27:02 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 18:27:02 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 18:27:02 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 18:27:02
2025-07-16 18:27:02 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 18:27:02 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.38
2025-07-16 18:27:02 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 18:28:58 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 18:28:58 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 18:28:58 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 18:28:58
2025-07-16 18:28:58 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 18:28:58 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.38
2025-07-16 18:28:58 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 18:30:57 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 18:30:57 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 18:30:57 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 18:30:57
2025-07-16 18:30:57 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 18:30:57 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.38
2025-07-16 18:30:57 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 18:33:56 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 18:33:56 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 18:33:56 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 18:33:56
2025-07-16 18:33:56 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 18:33:56 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.38
2025-07-16 18:33:56 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 18:45:21 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 18:45:21 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 18:45:21 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 18:45:21
2025-07-16 18:45:21 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 18:45:21 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.38
2025-07-16 18:45:21 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 18:48:08 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 18:48:08 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 18:48:08 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 18:48:08
2025-07-16 18:48:08 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 18:48:08 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.38
2025-07-16 18:48:08 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 18:49:13 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 18:49:13 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 18:49:13 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 18:49:13
2025-07-16 18:49:13 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 18:49:13 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.38
2025-07-16 18:49:13 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 18:52:44 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 18:52:44 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 18:52:44 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 18:52:44
2025-07-16 18:52:44 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 18:52:44 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.38
2025-07-16 18:52:44 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 18:52:59 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'WarehouseDialog' object has no attribute 'location_edit'
NoneType: None
2025-07-16 18:56:06 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 18:56:06 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 18:56:06 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 18:56:06
2025-07-16 18:56:06 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 18:56:06 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.38
2025-07-16 18:56:06 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 18:56:10 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: cannot access local variable 'QVBoxLayout' where it is not associated with a value
NoneType: None
2025-07-16 18:56:11 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: cannot access local variable 'QVBoxLayout' where it is not associated with a value
NoneType: None
2025-07-16 18:56:12 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: cannot access local variable 'QVBoxLayout' where it is not associated with a value
NoneType: None
2025-07-16 18:56:12 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: cannot access local variable 'QVBoxLayout' where it is not associated with a value
NoneType: None
2025-07-16 18:57:22 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 18:57:22 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 18:57:22 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 18:57:22
2025-07-16 18:57:22 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 18:57:22 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.38
2025-07-16 18:57:22 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 18:57:29 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: cannot access local variable 'QVBoxLayout' where it is not associated with a value
NoneType: None
2025-07-16 18:57:29 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: cannot access local variable 'QVBoxLayout' where it is not associated with a value
NoneType: None
2025-07-16 18:57:30 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: cannot access local variable 'QVBoxLayout' where it is not associated with a value
NoneType: None
2025-07-16 18:57:30 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: cannot access local variable 'QVBoxLayout' where it is not associated with a value
NoneType: None
2025-07-16 18:57:30 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: cannot access local variable 'QVBoxLayout' where it is not associated with a value
NoneType: None
2025-07-16 18:57:30 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: cannot access local variable 'QVBoxLayout' where it is not associated with a value
NoneType: None
2025-07-16 18:57:31 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: cannot access local variable 'QVBoxLayout' where it is not associated with a value
NoneType: None
2025-07-16 18:58:28 [INFO] SmartAccountingApp:123: ============================================================
2025-07-16 18:58:28 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-16 18:58:28 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-16 18:58:28
2025-07-16 18:58:28 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-16 18:58:28 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.38
2025-07-16 18:58:28 [INFO] SmartAccountingApp:128: ============================================================
2025-07-16 18:58:43 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'WarehouseDialog' object has no attribute 'location_edit'
NoneType: None
2025-07-18 20:37:37 [INFO] SmartAccountingApp:123: ============================================================
2025-07-18 20:37:37 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-18 20:37:37 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-18 20:37:37
2025-07-18 20:37:37 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-18 20:37:37 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.38
2025-07-18 20:37:37 [INFO] SmartAccountingApp:128: ============================================================
2025-07-18 20:44:31 [INFO] SmartAccountingApp:123: ============================================================
2025-07-18 20:44:31 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-18 20:44:31 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-18 20:44:31
2025-07-18 20:44:31 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-18 20:44:31 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.38
2025-07-18 20:44:31 [INFO] SmartAccountingApp:128: ============================================================
2025-07-18 20:48:20 [INFO] SmartAccountingApp:123: ============================================================
2025-07-18 20:48:20 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-18 20:48:20 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-18 20:48:20
2025-07-18 20:48:20 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-18 20:48:20 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.38
2025-07-18 20:48:20 [INFO] SmartAccountingApp:128: ============================================================
2025-07-18 20:52:31 [INFO] SmartAccountingApp:123: ============================================================
2025-07-18 20:52:31 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-18 20:52:31 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-18 20:52:31
2025-07-18 20:52:31 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-18 20:52:31 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.38
2025-07-18 20:52:31 [INFO] SmartAccountingApp:128: ============================================================
2025-07-18 20:53:08 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'WarehouseDialog' object has no attribute 'main_image_path'
NoneType: None
2025-07-18 20:53:15 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'WarehouseDialog' object has no attribute 'rack_image_path'
NoneType: None
2025-07-18 20:55:49 [INFO] SmartAccountingApp:123: ============================================================
2025-07-18 20:55:49 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-18 20:55:49 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-18 20:55:49
2025-07-18 20:55:49 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-18 20:55:49 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.38
2025-07-18 20:55:49 [INFO] SmartAccountingApp:128: ============================================================
2025-07-18 21:02:29 [INFO] SmartAccountingApp:123: ============================================================
2025-07-18 21:02:29 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-18 21:02:29 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-18 21:02:29
2025-07-18 21:02:29 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-18 21:02:29 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.38
2025-07-18 21:02:29 [INFO] SmartAccountingApp:128: ============================================================
2025-07-18 21:04:02 [INFO] SmartAccountingApp:123: ============================================================
2025-07-18 21:04:02 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-18 21:04:02 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-18 21:04:02
2025-07-18 21:04:02 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-18 21:04:02 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.38
2025-07-18 21:04:02 [INFO] SmartAccountingApp:128: ============================================================
2025-07-18 21:07:47 [INFO] SmartAccountingApp:123: ============================================================
2025-07-18 21:07:47 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-18 21:07:47 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-18 21:07:47
2025-07-18 21:07:47 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-18 21:07:47 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.38
2025-07-18 21:07:47 [INFO] SmartAccountingApp:128: ============================================================
2025-07-18 21:09:40 [INFO] SmartAccountingApp:123: ============================================================
2025-07-18 21:09:40 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-18 21:09:40 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-18 21:09:40
2025-07-18 21:09:40 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-18 21:09:40 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.38
2025-07-18 21:09:40 [INFO] SmartAccountingApp:128: ============================================================
2025-07-18 21:13:08 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
NoneType: None
2025-07-18 21:13:09 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
NoneType: None
2025-07-18 21:13:29 [INFO] SmartAccountingApp:123: ============================================================
2025-07-18 21:13:29 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-18 21:13:29 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-18 21:13:29
2025-07-18 21:13:29 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-18 21:13:29 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.38
2025-07-18 21:13:29 [INFO] SmartAccountingApp:128: ============================================================
2025-07-18 21:21:50 [INFO] SmartAccountingApp:123: ============================================================
2025-07-18 21:21:50 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-18 21:21:50 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-18 21:21:50
2025-07-18 21:21:50 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-18 21:21:50 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.38
2025-07-18 21:21:50 [INFO] SmartAccountingApp:128: ============================================================
2025-07-18 21:22:53 [INFO] SmartAccountingApp:123: ============================================================
2025-07-18 21:22:53 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-18 21:22:53 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-18 21:22:53
2025-07-18 21:22:53 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-18 21:22:53 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.38
2025-07-18 21:22:53 [INFO] SmartAccountingApp:128: ============================================================
2025-07-18 21:24:14 [INFO] SmartAccountingApp:123: ============================================================
2025-07-18 21:24:14 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-18 21:24:14 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-18 21:24:14
2025-07-18 21:24:14 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-18 21:24:14 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.38
2025-07-18 21:24:14 [INFO] SmartAccountingApp:128: ============================================================
2025-07-18 21:24:20 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
NoneType: None
2025-07-18 21:25:58 [INFO] SmartAccountingApp:123: ============================================================
2025-07-18 21:25:58 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-18 21:25:58 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-18 21:25:58
2025-07-18 21:25:58 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-18 21:25:58 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.38
2025-07-18 21:25:58 [INFO] SmartAccountingApp:128: ============================================================
2025-07-18 21:32:15 [INFO] SmartAccountingApp:123: ============================================================
2025-07-18 21:32:15 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-18 21:32:15 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-18 21:32:15
2025-07-18 21:32:15 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-18 21:32:15 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.38
2025-07-18 21:32:15 [INFO] SmartAccountingApp:128: ============================================================
2025-07-18 21:32:22 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
NoneType: None
2025-07-18 21:32:22 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
NoneType: None
2025-07-18 21:32:23 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
NoneType: None
2025-07-18 21:32:23 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
NoneType: None
2025-07-18 21:32:23 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
NoneType: None
2025-07-18 21:32:25 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
NoneType: None
2025-07-18 21:32:26 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
NoneType: None
2025-07-18 21:32:29 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
NoneType: None
2025-07-18 21:32:39 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
NoneType: None
2025-07-18 21:32:39 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
NoneType: None
2025-07-18 21:33:06 [INFO] SmartAccountingApp:123: ============================================================
2025-07-18 21:33:06 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-18 21:33:06 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-18 21:33:06
2025-07-18 21:33:06 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-18 21:33:06 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.38
2025-07-18 21:33:06 [INFO] SmartAccountingApp:128: ============================================================
2025-07-19 20:04:07 [INFO] SmartAccountingApp:123: ============================================================
2025-07-19 20:04:07 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-19 20:04:07 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-19 20:04:07
2025-07-19 20:04:07 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-19 20:04:07 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.38
2025-07-19 20:04:07 [INFO] SmartAccountingApp:128: ============================================================
2025-07-19 20:04:14 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
NoneType: None
2025-07-19 20:04:15 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
NoneType: None
2025-07-19 20:04:15 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
NoneType: None
2025-07-19 20:04:27 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
NoneType: None
2025-07-19 20:04:32 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
NoneType: None
2025-07-19 20:04:32 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
NoneType: None
2025-07-19 20:04:32 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
NoneType: None
2025-07-19 20:04:34 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
NoneType: None
2025-07-19 20:04:34 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
NoneType: None
2025-07-19 20:35:29 [INFO] SmartAccountingApp:123: ============================================================
2025-07-19 20:35:29 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-19 20:35:29 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-19 20:35:29
2025-07-19 20:35:29 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-19 20:35:29 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.38
2025-07-19 20:35:29 [INFO] SmartAccountingApp:128: ============================================================
2025-07-19 20:35:37 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
NoneType: None
2025-07-19 20:35:38 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
NoneType: None
2025-07-19 20:35:38 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
NoneType: None
2025-07-19 20:39:39 [INFO] SmartAccountingApp:123: ============================================================
2025-07-19 20:39:39 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-19 20:39:39 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-19 20:39:39
2025-07-19 20:39:39 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-19 20:39:39 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.38
2025-07-19 20:39:39 [INFO] SmartAccountingApp:128: ============================================================
2025-07-19 20:43:42 [INFO] SmartAccountingApp:123: ============================================================
2025-07-19 20:43:42 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-19 20:43:42 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-19 20:43:42
2025-07-19 20:43:42 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-19 20:43:42 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.38
2025-07-19 20:43:42 [INFO] SmartAccountingApp:128: ============================================================
2025-07-19 20:45:47 [INFO] SmartAccountingApp:123: ============================================================
2025-07-19 20:45:47 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-19 20:45:47 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-19 20:45:47
2025-07-19 20:45:47 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-19 20:45:47 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.38
2025-07-19 20:45:47 [INFO] SmartAccountingApp:128: ============================================================
2025-07-19 20:49:31 [INFO] SmartAccountingApp:123: ============================================================
2025-07-19 20:49:31 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-19 20:49:31 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-19 20:49:31
2025-07-19 20:49:31 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-19 20:49:31 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.38
2025-07-19 20:49:31 [INFO] SmartAccountingApp:128: ============================================================
2025-07-19 20:53:50 [INFO] SmartAccountingApp:123: ============================================================
2025-07-19 20:53:50 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-19 20:53:50 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-19 20:53:50
2025-07-19 20:53:50 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-19 20:53:50 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.38
2025-07-19 20:53:50 [INFO] SmartAccountingApp:128: ============================================================
2025-07-19 20:57:04 [INFO] SmartAccountingApp:123: ============================================================
2025-07-19 20:57:04 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-19 20:57:04 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-19 20:57:04
2025-07-19 20:57:04 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-19 20:57:04 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.38
2025-07-19 20:57:04 [INFO] SmartAccountingApp:128: ============================================================
2025-07-19 21:00:11 [INFO] SmartAccountingApp:123: ============================================================
2025-07-19 21:00:11 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-19 21:00:11 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-19 21:00:11
2025-07-19 21:00:11 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-19 21:00:11 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.38
2025-07-19 21:00:11 [INFO] SmartAccountingApp:128: ============================================================
2025-07-19 21:01:35 [INFO] SmartAccountingApp:123: ============================================================
2025-07-19 21:01:35 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-19 21:01:35 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-19 21:01:35
2025-07-19 21:01:35 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-19 21:01:35 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.38
2025-07-19 21:01:35 [INFO] SmartAccountingApp:128: ============================================================
2025-07-19 21:02:02 [INFO] SmartAccountingApp:123: ============================================================
2025-07-19 21:02:02 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-19 21:02:02 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-19 21:02:02
2025-07-19 21:02:02 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-19 21:02:02 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.38
2025-07-19 21:02:02 [INFO] SmartAccountingApp:128: ============================================================
2025-07-19 21:03:09 [INFO] SmartAccountingApp:123: ============================================================
2025-07-19 21:03:09 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-19 21:03:09 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-19 21:03:09
2025-07-19 21:03:09 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-19 21:03:09 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.38
2025-07-19 21:03:09 [INFO] SmartAccountingApp:128: ============================================================
2025-07-19 21:03:48 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
NoneType: None
2025-07-19 21:03:49 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
NoneType: None
2025-07-19 21:03:49 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
NoneType: None
2025-07-21 15:26:52 [INFO] SmartAccountingApp:123: ============================================================
2025-07-21 15:26:52 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-21 15:26:52 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-21 15:26:52
2025-07-21 15:26:52 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-21 15:26:52 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.46
2025-07-21 15:26:52 [INFO] SmartAccountingApp:128: ============================================================
2025-07-21 15:27:35 [INFO] SmartAccountingApp:123: ============================================================
2025-07-21 15:27:35 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-21 15:27:35 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-21 15:27:35
2025-07-21 15:27:35 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-21 15:27:35 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.46
2025-07-21 15:27:35 [INFO] SmartAccountingApp:128: ============================================================
2025-07-21 15:41:03 [INFO] SmartAccountingApp:123: ============================================================
2025-07-21 15:41:03 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-21 15:41:03 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-21 15:41:03
2025-07-21 15:41:03 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-21 15:41:03 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.46
2025-07-21 15:41:03 [INFO] SmartAccountingApp:128: ============================================================
2025-07-21 16:02:37 [INFO] SmartAccountingApp:123: ============================================================
2025-07-21 16:02:37 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-21 16:02:37 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-21 16:02:37
2025-07-21 16:02:37 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-21 16:02:37 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.46
2025-07-21 16:02:37 [INFO] SmartAccountingApp:128: ============================================================
2025-07-21 16:09:06 [INFO] SmartAccountingApp:123: ============================================================
2025-07-21 16:09:06 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-21 16:09:06 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-21 16:09:06
2025-07-21 16:09:06 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-21 16:09:06 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.46
2025-07-21 16:09:06 [INFO] SmartAccountingApp:128: ============================================================
2025-07-21 16:09:27 [INFO] SmartAccountingApp:123: ============================================================
2025-07-21 16:09:27 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-21 16:09:27 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-21 16:09:27
2025-07-21 16:09:27 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-21 16:09:27 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.46
2025-07-21 16:09:27 [INFO] SmartAccountingApp:128: ============================================================
2025-07-21 16:20:12 [INFO] SmartAccountingApp:123: ============================================================
2025-07-21 16:20:12 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-21 16:20:12 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-21 16:20:12
2025-07-21 16:20:12 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-21 16:20:12 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.46
2025-07-21 16:20:12 [INFO] SmartAccountingApp:128: ============================================================
2025-07-21 16:20:50 [INFO] SmartAccountingApp:123: ============================================================
2025-07-21 16:20:50 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-21 16:20:50 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-21 16:20:50
2025-07-21 16:20:50 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-21 16:20:50 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.46
2025-07-21 16:20:50 [INFO] SmartAccountingApp:128: ============================================================
2025-07-21 16:23:26 [INFO] SmartAccountingApp:123: ============================================================
2025-07-21 16:23:26 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-21 16:23:26 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-21 16:23:26
2025-07-21 16:23:26 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-21 16:23:26 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.46
2025-07-21 16:23:26 [INFO] SmartAccountingApp:128: ============================================================
2025-07-21 16:26:41 [INFO] SmartAccountingApp:123: ============================================================
2025-07-21 16:26:41 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-21 16:26:41 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-21 16:26:41
2025-07-21 16:26:41 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-21 16:26:41 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.46
2025-07-21 16:26:41 [INFO] SmartAccountingApp:128: ============================================================
2025-07-21 16:30:10 [INFO] SmartAccountingApp:123: ============================================================
2025-07-21 16:30:10 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-21 16:30:10 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-21 16:30:10
2025-07-21 16:30:10 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-21 16:30:10 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.46
2025-07-21 16:30:10 [INFO] SmartAccountingApp:128: ============================================================
2025-07-21 16:30:33 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
NoneType: None
2025-07-21 16:30:34 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
NoneType: None
2025-07-21 16:30:34 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
NoneType: None
2025-07-21 16:30:45 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
NoneType: None
2025-07-21 16:31:03 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
NoneType: None
2025-07-21 16:31:04 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
NoneType: None
2025-07-21 16:31:04 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
NoneType: None
2025-07-21 16:31:04 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
NoneType: None
2025-07-21 16:31:05 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
NoneType: None
2025-07-21 16:31:43 [INFO] SmartAccountingApp:123: ============================================================
2025-07-21 16:31:43 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-21 16:31:43 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-21 16:31:43
2025-07-21 16:31:43 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-21 16:31:43 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.46
2025-07-21 16:31:43 [INFO] SmartAccountingApp:128: ============================================================
2025-07-21 16:32:00 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
NoneType: None
2025-07-21 16:32:00 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
NoneType: None
2025-07-21 16:32:00 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
NoneType: None
2025-07-21 16:33:49 [INFO] SmartAccountingApp:123: ============================================================
2025-07-21 16:33:49 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-21 16:33:49 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-21 16:33:49
2025-07-21 16:33:49 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-21 16:33:49 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.46
2025-07-21 16:33:49 [INFO] SmartAccountingApp:128: ============================================================
2025-07-21 16:33:56 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
NoneType: None
2025-07-21 16:33:56 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
NoneType: None
2025-07-21 16:33:56 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
NoneType: None
2025-07-21 16:34:05 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
NoneType: None
2025-07-21 16:34:06 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
NoneType: None
2025-07-21 16:34:06 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
NoneType: None
2025-07-21 16:34:07 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
NoneType: None
2025-07-21 16:34:08 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
NoneType: None
2025-07-21 16:34:09 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
NoneType: None
2025-07-21 16:34:29 [INFO] SmartAccountingApp:123: ============================================================
2025-07-21 16:34:29 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-21 16:34:29 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-21 16:34:29
2025-07-21 16:34:29 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-21 16:34:29 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.46
2025-07-21 16:34:29 [INFO] SmartAccountingApp:128: ============================================================
2025-07-21 16:34:38 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
NoneType: None
2025-07-21 16:36:46 [INFO] SmartAccountingApp:123: ============================================================
2025-07-21 16:36:46 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-21 16:36:46 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-21 16:36:46
2025-07-21 16:36:46 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-21 16:36:46 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.46
2025-07-21 16:36:46 [INFO] SmartAccountingApp:128: ============================================================
2025-07-21 16:40:47 [INFO] SmartAccountingApp:123: ============================================================
2025-07-21 16:40:47 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-21 16:40:48 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-21 16:40:48
2025-07-21 16:40:48 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-21 16:40:48 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.46
2025-07-21 16:40:48 [INFO] SmartAccountingApp:128: ============================================================
2025-07-21 16:41:12 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'WarehouseDialog' object has no attribute 'location_edit'
NoneType: None
2025-07-21 16:42:02 [INFO] SmartAccountingApp:123: ============================================================
2025-07-21 16:42:02 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-21 16:42:02 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-21 16:42:02
2025-07-21 16:42:02 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-21 16:42:02 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.46
2025-07-21 16:42:02 [INFO] SmartAccountingApp:128: ============================================================
2025-07-21 16:42:51 [INFO] SmartAccountingApp:123: ============================================================
2025-07-21 16:42:51 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-21 16:42:51 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-21 16:42:51
2025-07-21 16:42:51 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-21 16:42:51 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.46
2025-07-21 16:42:51 [INFO] SmartAccountingApp:128: ============================================================
2025-07-21 16:43:14 [INFO] SmartAccountingApp:123: ============================================================
2025-07-21 16:43:14 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-21 16:43:14 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-21 16:43:14
2025-07-21 16:43:14 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-21 16:43:14 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.46
2025-07-21 16:43:14 [INFO] SmartAccountingApp:128: ============================================================
2025-07-21 16:51:56 [INFO] SmartAccountingApp:123: ============================================================
2025-07-21 16:51:56 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-21 16:51:56 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-21 16:51:56
2025-07-21 16:51:56 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-21 16:51:56 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: G:\Gaea\GaeaDev\1l0.00\l0.46
2025-07-21 16:51:56 [INFO] SmartAccountingApp:128: ============================================================
2025-07-27 09:01:16 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
NoneType: None
2025-07-27 09:02:06 [INFO] SmartAccountingApp:123: ============================================================
2025-07-27 09:02:06 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-27 09:02:06 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-27 09:02:06
2025-07-27 09:02:06 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-27 09:02:06 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: C:\Users\<USER>\Desktop\l\l0.50
2025-07-27 09:02:06 [INFO] SmartAccountingApp:128: ============================================================
2025-07-27 09:06:58 [INFO] SmartAccountingApp:123: ============================================================
2025-07-27 09:06:58 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-27 09:06:58 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-27 09:06:58
2025-07-27 09:06:58 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-27 09:06:58 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: C:\Users\<USER>\Desktop\l\l0.50
2025-07-27 09:06:58 [INFO] SmartAccountingApp:128: ============================================================
2025-07-27 09:08:14 [INFO] SmartAccountingApp:123: ============================================================
2025-07-27 09:08:14 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-27 09:08:14 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-27 09:08:14
2025-07-27 09:08:14 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-27 09:08:14 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: C:\Users\<USER>\Desktop\l\l0.50
2025-07-27 09:08:14 [INFO] SmartAccountingApp:128: ============================================================
2025-07-27 09:09:51 [INFO] SmartAccountingApp:123: ============================================================
2025-07-27 09:09:51 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-07-27 09:09:51 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-07-27 09:09:51
2025-07-27 09:09:51 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-07-27 09:09:51 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: C:\Users\<USER>\Desktop\l\l0.50
2025-07-27 09:09:51 [INFO] SmartAccountingApp:128: ============================================================
2025-07-27 09:11:31 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
NoneType: None
2025-07-27 09:11:39 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
NoneType: None
2025-08-02 17:53:22 [INFO] SmartAccountingApp:123: ============================================================
2025-08-02 17:53:22 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-08-02 17:53:22 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-08-02 17:53:22
2025-08-02 17:53:22 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.3 (tags/v3.13.3:6280bb5, Apr  8 2025, 14:47:33) [MSC v.1943 64 bit (AMD64)]
2025-08-02 17:53:22 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: C:\Users\<USER>\Desktop\l0.50
2025-08-02 17:53:22 [INFO] SmartAccountingApp:128: ============================================================
2025-08-02 17:53:31 [INFO] SmartAccountingApp:123: ============================================================
2025-08-02 17:53:31 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-08-02 17:53:31 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-08-02 17:53:31
2025-08-02 17:53:31 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.3 (tags/v3.13.3:6280bb5, Apr  8 2025, 14:47:33) [MSC v.1943 64 bit (AMD64)]
2025-08-02 17:53:31 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: C:\Users\<USER>\Desktop\l0.50
2025-08-02 17:53:31 [INFO] SmartAccountingApp:128: ============================================================
2025-08-02 17:55:31 [INFO] SmartAccountingApp:123: ============================================================
2025-08-02 17:55:31 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-08-02 17:55:31 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-08-02 17:55:31
2025-08-02 17:55:31 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.3 (tags/v3.13.3:6280bb5, Apr  8 2025, 14:47:33) [MSC v.1943 64 bit (AMD64)]
2025-08-02 17:55:31 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: C:\Users\<USER>\Desktop\l0.50
2025-08-02 17:55:31 [INFO] SmartAccountingApp:128: ============================================================
2025-08-02 17:56:45 [INFO] SmartAccountingApp:123: ============================================================
2025-08-02 17:56:45 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-08-02 17:56:45 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-08-02 17:56:45
2025-08-02 17:56:45 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.3 (tags/v3.13.3:6280bb5, Apr  8 2025, 14:47:33) [MSC v.1943 64 bit (AMD64)]
2025-08-02 17:56:45 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: C:\Users\<USER>\Desktop\l0.50
2025-08-02 17:56:45 [INFO] SmartAccountingApp:128: ============================================================
2025-08-02 17:57:17 [INFO] SmartAccountingApp:123: ============================================================
2025-08-02 17:57:17 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-08-02 17:57:17 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-08-02 17:57:17
2025-08-02 17:57:17 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.3 (tags/v3.13.3:6280bb5, Apr  8 2025, 14:47:33) [MSC v.1943 64 bit (AMD64)]
2025-08-02 17:57:17 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: C:\Users\<USER>\Desktop\l0.50
2025-08-02 17:57:17 [INFO] SmartAccountingApp:128: ============================================================
2025-08-02 17:58:23 [INFO] SmartAccountingApp:123: ============================================================
2025-08-02 17:58:23 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-08-02 17:58:23 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-08-02 17:58:23
2025-08-02 17:58:23 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.3 (tags/v3.13.3:6280bb5, Apr  8 2025, 14:47:33) [MSC v.1943 64 bit (AMD64)]
2025-08-02 17:58:23 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: C:\Users\<USER>\Desktop\l0.50
2025-08-02 17:58:23 [INFO] SmartAccountingApp:128: ============================================================
2025-08-02 17:58:33 [INFO] SmartAccountingApp:123: ============================================================
2025-08-02 17:58:33 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-08-02 17:58:33 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-08-02 17:58:33
2025-08-02 17:58:33 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.3 (tags/v3.13.3:6280bb5, Apr  8 2025, 14:47:33) [MSC v.1943 64 bit (AMD64)]
2025-08-02 17:58:33 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: C:\Users\<USER>\Desktop\l0.50
2025-08-02 17:58:33 [INFO] SmartAccountingApp:128: ============================================================
2025-08-02 17:59:24 [INFO] SmartAccountingApp:123: ============================================================
2025-08-02 17:59:24 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-08-02 17:59:24 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-08-02 17:59:24
2025-08-02 17:59:24 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.3 (tags/v3.13.3:6280bb5, Apr  8 2025, 14:47:33) [MSC v.1943 64 bit (AMD64)]
2025-08-02 17:59:24 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: C:\Users\<USER>\Desktop\l0.50
2025-08-02 17:59:24 [INFO] SmartAccountingApp:128: ============================================================
2025-08-02 18:00:36 [INFO] SmartAccountingApp:123: ============================================================
2025-08-02 18:00:36 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-08-02 18:00:36 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-08-02 18:00:36
2025-08-02 18:00:36 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.3 (tags/v3.13.3:6280bb5, Apr  8 2025, 14:47:33) [MSC v.1943 64 bit (AMD64)]
2025-08-02 18:00:36 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: C:\Users\<USER>\Desktop\l0.50
2025-08-02 18:00:36 [INFO] SmartAccountingApp:128: ============================================================
2025-08-02 18:07:34 [INFO] SmartAccountingApp:123: ============================================================
2025-08-02 18:07:34 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-08-02 18:07:34 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-08-02 18:07:34
2025-08-02 18:07:34 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.3 (tags/v3.13.3:6280bb5, Apr  8 2025, 14:47:33) [MSC v.1943 64 bit (AMD64)]
2025-08-02 18:07:34 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: C:\Users\<USER>\Desktop\l0.50
2025-08-02 18:07:34 [INFO] SmartAccountingApp:128: ============================================================
2025-08-02 18:07:52 [INFO] SmartAccountingApp:123: ============================================================
2025-08-02 18:07:52 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-08-02 18:07:52 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-08-02 18:07:52
2025-08-02 18:07:52 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.3 (tags/v3.13.3:6280bb5, Apr  8 2025, 14:47:33) [MSC v.1943 64 bit (AMD64)]
2025-08-02 18:07:52 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: C:\Users\<USER>\Desktop\l0.50
2025-08-02 18:07:52 [INFO] SmartAccountingApp:128: ============================================================
2025-08-02 18:16:15 [INFO] SmartAccountingApp:123: ============================================================
2025-08-02 18:16:15 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-08-02 18:16:15 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-08-02 18:16:15
2025-08-02 18:16:15 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.3 (tags/v3.13.3:6280bb5, Apr  8 2025, 14:47:33) [MSC v.1943 64 bit (AMD64)]
2025-08-02 18:16:15 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: C:\Users\<USER>\Desktop\l0.50
2025-08-02 18:16:15 [INFO] SmartAccountingApp:128: ============================================================
2025-08-02 18:17:54 [INFO] SmartAccountingApp:123: ============================================================
2025-08-02 18:17:54 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-08-02 18:17:54 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-08-02 18:17:54
2025-08-02 18:17:54 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.3 (tags/v3.13.3:6280bb5, Apr  8 2025, 14:47:33) [MSC v.1943 64 bit (AMD64)]
2025-08-02 18:17:54 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: C:\Users\<USER>\Desktop\l0.50
2025-08-02 18:17:54 [INFO] SmartAccountingApp:128: ============================================================
2025-08-02 18:32:30 [INFO] SmartAccountingApp:123: ============================================================
2025-08-02 18:32:30 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-08-02 18:32:30 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-08-02 18:32:30
2025-08-02 18:32:30 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.3 (tags/v3.13.3:6280bb5, Apr  8 2025, 14:47:33) [MSC v.1943 64 bit (AMD64)]
2025-08-02 18:32:30 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: C:\Users\<USER>\Desktop\l0.50
2025-08-02 18:32:30 [INFO] SmartAccountingApp:128: ============================================================
2025-08-02 21:33:38 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
NoneType: None
2025-08-02 21:37:41 [INFO] SmartAccountingApp:123: ============================================================
2025-08-02 21:37:41 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-08-02 21:37:41 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-08-02 21:37:41
2025-08-02 21:37:41 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-08-02 21:37:41 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: C:\Users\<USER>\Desktop\l\l0.51
2025-08-02 21:37:41 [INFO] SmartAccountingApp:128: ============================================================
2025-08-02 21:39:43 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
NoneType: None
2025-08-02 21:40:06 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
NoneType: None
2025-08-02 21:40:15 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
NoneType: None
2025-08-02 21:40:22 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
NoneType: None
2025-08-02 21:40:42 [CRITICAL] SmartAccountingApp.ErrorHandler:144: خطأ system: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
NoneType: None
2025-08-02 21:42:14 [INFO] SmartAccountingApp:123: ============================================================
2025-08-02 21:42:14 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-08-02 21:42:14 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-08-02 21:42:14
2025-08-02 21:42:14 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-08-02 21:42:14 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: c:\Users\<USER>\Desktop\l\l0.51
2025-08-02 21:42:14 [INFO] SmartAccountingApp:128: ============================================================
2025-08-02 21:42:42 [INFO] SmartAccountingApp:123: ============================================================
2025-08-02 21:42:42 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-08-02 21:42:42 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-08-02 21:42:42
2025-08-02 21:42:42 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-08-02 21:42:42 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: c:\Users\<USER>\Desktop\l\l0.51
2025-08-02 21:42:42 [INFO] SmartAccountingApp:128: ============================================================
2025-08-02 21:50:06 [INFO] SmartAccountingApp:123: ============================================================
2025-08-02 21:50:06 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-08-02 21:50:06 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-08-02 21:50:06
2025-08-02 21:50:06 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-08-02 21:50:06 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: c:\Users\<USER>\Desktop\l\l0.51
2025-08-02 21:50:06 [INFO] SmartAccountingApp:128: ============================================================
2025-08-02 21:51:19 [INFO] SmartAccountingApp:123: ============================================================
2025-08-02 21:51:19 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-08-02 21:51:19 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-08-02 21:51:19
2025-08-02 21:51:19 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-08-02 21:51:19 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: c:\Users\<USER>\Desktop\l\l0.51
2025-08-02 21:51:19 [INFO] SmartAccountingApp:128: ============================================================
2025-08-02 21:56:07 [INFO] SmartAccountingApp:123: ============================================================
2025-08-02 21:56:07 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-08-02 21:56:07 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-08-02 21:56:07
2025-08-02 21:56:07 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-08-02 21:56:07 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: c:\Users\<USER>\Desktop\l\l0.51
2025-08-02 21:56:07 [INFO] SmartAccountingApp:128: ============================================================
2025-08-02 21:58:52 [INFO] SmartAccountingApp:123: ============================================================
2025-08-02 21:58:52 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-08-02 21:58:52 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-08-02 21:58:52
2025-08-02 21:58:52 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-08-02 21:58:53 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: c:\Users\<USER>\Desktop\l\l0.51
2025-08-02 21:58:53 [INFO] SmartAccountingApp:128: ============================================================
2025-08-02 22:09:00 [INFO] SmartAccountingApp:123: ============================================================
2025-08-02 22:09:00 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-08-02 22:09:00 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-08-02 22:09:00
2025-08-02 22:09:00 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-08-02 22:09:00 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: c:\Users\<USER>\Desktop\l\l0.51
2025-08-02 22:09:00 [INFO] SmartAccountingApp:128: ============================================================
2025-08-02 22:10:42 [INFO] SmartAccountingApp:123: ============================================================
2025-08-02 22:10:42 [INFO] SmartAccountingApp:124: 🚀 بدء تشغيل Smart Accounting App
2025-08-02 22:10:42 [INFO] SmartAccountingApp:125: ⏰ الوقت: 2025-08-02 22:10:42
2025-08-02 22:10:42 [INFO] SmartAccountingApp:126: 🐍 إصدار Python: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-08-02 22:10:42 [INFO] SmartAccountingApp:127: 📁 مجلد العمل: c:\Users\<USER>\Desktop\l\l0.51
2025-08-02 22:10:42 [INFO] SmartAccountingApp:128: ============================================================
