"""
نموذج المورد - يحتوي على جميع المعلومات المتعلقة بالموردين
"""

from dataclasses import dataclass, field
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum


class SupplierStatus(Enum):
    """حالة المورد"""
    ACTIVE = "نشط"
    INACTIVE = "غير نشط"
    SUSPENDED = "معلق"
    BLACKLISTED = "محظور"


class SupplierType(Enum):
    """نوع المورد"""
    INDIVIDUAL = "فرد"
    COMPANY = "شركة"
    GOVERNMENT = "حكومي"
    INTERNATIONAL = "دولي"


class PaymentTerms(Enum):
    """شروط الدفع"""
    CASH = "نقدي"
    NET_30 = "30 يوم"
    NET_60 = "60 يوم"
    NET_90 = "90 يوم"
    ADVANCE = "دفع مقدم"
    COD = "الدفع عند التسليم"


@dataclass
class SupplierContact:
    """معلومات الاتصال بالمورد"""
    name: str = ""
    position: str = ""
    phone: str = ""
    mobile: str = ""
    email: str = ""
    is_primary: bool = False


@dataclass
class SupplierAddress:
    """عنوان المورد"""
    type: str = "رئيسي"  # رئيسي، فرعي، مستودع، إلخ
    street: str = ""
    city: str = ""
    state: str = ""
    postal_code: str = ""
    country: str = "السعودية"
    is_primary: bool = True


@dataclass
class SupplierBankAccount:
    """الحساب البنكي للمورد"""
    bank_name: str = ""
    account_number: str = ""
    iban: str = ""
    swift_code: str = ""
    account_holder_name: str = ""
    is_primary: bool = True


@dataclass
class SupplierDocument:
    """وثائق المورد"""
    document_type: str = ""  # سجل تجاري، رخصة، شهادة ضريبية، إلخ
    document_number: str = ""
    issue_date: Optional[datetime] = None
    expiry_date: Optional[datetime] = None
    file_path: str = ""
    notes: str = ""


@dataclass
class SupplierRating:
    """تقييم المورد"""
    quality_rating: float = 0.0  # من 1 إلى 5
    delivery_rating: float = 0.0  # من 1 إلى 5
    service_rating: float = 0.0  # من 1 إلى 5
    price_rating: float = 0.0  # من 1 إلى 5
    overall_rating: float = 0.0  # من 1 إلى 5
    total_reviews: int = 0
    last_review_date: Optional[datetime] = None


@dataclass
class Supplier:
    """نموذج المورد الرئيسي"""
    
    # المعلومات الأساسية
    id: Optional[int] = None
    supplier_code: str = ""  # رمز المورد
    name: str = ""
    name_english: str = ""
    supplier_type: SupplierType = SupplierType.COMPANY
    status: SupplierStatus = SupplierStatus.ACTIVE
    
    # معلومات الاتصال
    contacts: List[SupplierContact] = field(default_factory=list)
    addresses: List[SupplierAddress] = field(default_factory=list)
    
    # المعلومات المالية
    payment_terms: PaymentTerms = PaymentTerms.NET_30
    credit_limit: float = 0.0
    current_balance: float = 0.0
    currency: str = "SAR"
    bank_accounts: List[SupplierBankAccount] = field(default_factory=list)
    
    # المعلومات الضريبية
    tax_number: str = ""
    commercial_registration: str = ""
    is_tax_exempt: bool = False
    tax_exemption_reason: str = ""
    
    # معلومات التقييم والأداء
    rating: SupplierRating = field(default_factory=SupplierRating)
    total_orders: int = 0
    total_purchases: float = 0.0
    last_order_date: Optional[datetime] = None
    
    # الوثائق والمرفقات
    documents: List[SupplierDocument] = field(default_factory=list)
    
    # معلومات إضافية
    categories: List[str] = field(default_factory=list)  # فئات المنتجات التي يوردها
    lead_time_days: int = 7  # مدة التسليم بالأيام
    minimum_order_amount: float = 0.0
    discount_percentage: float = 0.0
    
    # ملاحظات ومعلومات إضافية
    notes: str = ""
    internal_notes: str = ""  # ملاحظات داخلية لا يراها المورد
    
    # معلومات النظام
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    created_by: str = ""
    updated_by: str = ""
    
    def __post_init__(self):
        """تهيئة إضافية بعد إنشاء الكائن"""
        if not self.created_at:
            self.created_at = datetime.now()
        self.updated_at = datetime.now()
        
        # إنشاء رمز المورد إذا لم يكن موجوداً
        if not self.supplier_code and self.name:
            self.supplier_code = self.generate_supplier_code()
    
    def generate_supplier_code(self) -> str:
        """إنشاء رمز المورد تلقائياً"""
        if self.name:
            # أخذ أول 3 أحرف من اسم المورد + رقم عشوائي
            prefix = self.name[:3].upper()
            import random
            suffix = str(random.randint(1000, 9999))
            return f"SUP-{prefix}-{suffix}"
        return ""
    
    def get_primary_contact(self) -> Optional[SupplierContact]:
        """الحصول على جهة الاتصال الرئيسية"""
        for contact in self.contacts:
            if contact.is_primary:
                return contact
        return self.contacts[0] if self.contacts else None
    
    def get_primary_address(self) -> Optional[SupplierAddress]:
        """الحصول على العنوان الرئيسي"""
        for address in self.addresses:
            if address.is_primary:
                return address
        return self.addresses[0] if self.addresses else None
    
    def get_primary_bank_account(self) -> Optional[SupplierBankAccount]:
        """الحصول على الحساب البنكي الرئيسي"""
        for account in self.bank_accounts:
            if account.is_primary:
                return account
        return self.bank_accounts[0] if self.bank_accounts else None
    
    def calculate_overall_rating(self) -> float:
        """حساب التقييم الإجمالي"""
        if self.rating.total_reviews == 0:
            return 0.0
        
        total = (self.rating.quality_rating + 
                self.rating.delivery_rating + 
                self.rating.service_rating + 
                self.rating.price_rating)
        
        self.rating.overall_rating = total / 4
        return self.rating.overall_rating
    
    def is_active(self) -> bool:
        """التحقق من أن المورد نشط"""
        return self.status == SupplierStatus.ACTIVE
    
    def can_place_order(self) -> bool:
        """التحقق من إمكانية وضع طلب مع هذا المورد"""
        return (self.is_active() and 
                self.status != SupplierStatus.BLACKLISTED and
                self.current_balance <= self.credit_limit)
    
    def to_dict(self) -> Dict[str, Any]:
        """تحويل الكائن إلى قاموس"""
        return {
            'id': self.id,
            'supplier_code': self.supplier_code,
            'name': self.name,
            'name_english': self.name_english,
            'supplier_type': self.supplier_type.value,
            'status': self.status.value,
            'payment_terms': self.payment_terms.value,
            'credit_limit': self.credit_limit,
            'current_balance': self.current_balance,
            'currency': self.currency,
            'tax_number': self.tax_number,
            'commercial_registration': self.commercial_registration,
            'is_tax_exempt': self.is_tax_exempt,
            'total_orders': self.total_orders,
            'total_purchases': self.total_purchases,
            'categories': self.categories,
            'lead_time_days': self.lead_time_days,
            'minimum_order_amount': self.minimum_order_amount,
            'discount_percentage': self.discount_percentage,
            'notes': self.notes,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'created_by': self.created_by,
            'updated_by': self.updated_by
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Supplier':
        """إنشاء كائن من قاموس"""
        supplier = cls()
        supplier.id = data.get('id')
        supplier.supplier_code = data.get('supplier_code', '')
        supplier.name = data.get('name', '')
        supplier.name_english = data.get('name_english', '')
        
        # تحويل القيم النصية إلى Enums
        if 'supplier_type' in data:
            supplier.supplier_type = SupplierType(data['supplier_type'])
        if 'status' in data:
            supplier.status = SupplierStatus(data['status'])
        if 'payment_terms' in data:
            supplier.payment_terms = PaymentTerms(data['payment_terms'])
        
        supplier.credit_limit = data.get('credit_limit', 0.0)
        supplier.current_balance = data.get('current_balance', 0.0)
        supplier.currency = data.get('currency', 'SAR')
        supplier.tax_number = data.get('tax_number', '')
        supplier.commercial_registration = data.get('commercial_registration', '')
        supplier.is_tax_exempt = data.get('is_tax_exempt', False)
        supplier.total_orders = data.get('total_orders', 0)
        supplier.total_purchases = data.get('total_purchases', 0.0)
        supplier.categories = data.get('categories', [])
        supplier.lead_time_days = data.get('lead_time_days', 7)
        supplier.minimum_order_amount = data.get('minimum_order_amount', 0.0)
        supplier.discount_percentage = data.get('discount_percentage', 0.0)
        supplier.notes = data.get('notes', '')
        supplier.created_by = data.get('created_by', '')
        supplier.updated_by = data.get('updated_by', '')
        
        # تحويل التواريخ
        if data.get('created_at'):
            supplier.created_at = datetime.fromisoformat(data['created_at'])
        if data.get('updated_at'):
            supplier.updated_at = datetime.fromisoformat(data['updated_at'])
        
        return supplier
