import logging

class NotificationController:
    def __init__(self, notification_panel):
        self.notification_panel = notification_panel

    def notify(self, text, notif_type="info"):
        try:
            self.notification_panel.add_notification(text, notif_type)
            return True
        except Exception as e:
            from accounting_app.core.logger import get_logger
            logger = get_logger(__name__)
            logger.error(f"Failed to add notification: {e}")
            return False 