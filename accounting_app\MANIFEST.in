# Include package data
include README.md
include LICENSE
include requirements.txt
include requirements-minimal.txt
include dev-requirements.txt
include requirements-lock.txt
include pyproject.toml
include setup.py
include Makefile
include .pre-commit-config.yaml

# Include configuration files
include config.json
include app_config.py

# Include translation files
recursive-include translations *.ts *.qm

# Include resource files
recursive-include resources *.svg *.wav *.png *.jpg *.jpeg

# Include UI style files
recursive-include ui *.qss

# Include documentation
recursive-include docs *.md *.rst *.txt

# Include scripts
recursive-include scripts *.py

# Include tests
recursive-include tests *.py

# Exclude unnecessary files
global-exclude *.pyc
global-exclude *.pyo
global-exclude *.pyd
global-exclude __pycache__
global-exclude .git*
global-exclude .DS_Store
global-exclude .vscode
global-exclude .idea
global-exclude *.log
global-exclude logs/*
global-exclude .coverage
global-exclude htmlcov
global-exclude .pytest_cache
global-exclude .mypy_cache
global-exclude .tox
global-exclude build
global-exclude dist
global-exclude *.egg-info 