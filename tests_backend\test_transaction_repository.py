import sys
import os
import pytest
import uuid
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from datetime import datetime

sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from database.models import Base, Transaction
from database.repositories.transaction_repository import TransactionRepository

@pytest.fixture(scope="function")
def db_session():
    engine = create_engine('sqlite:///:memory:')
    Base.metadata.create_all(engine)
    Session = sessionmaker(bind=engine)
    session = Session()
    yield session
    session.close()

def test_create_and_get_transaction(db_session):
    company_id = uuid.uuid4()
    repo = TransactionRepository(db_session)
    transaction = Transaction(company_id=company_id, description="Test Transaction", date=datetime.utcnow())
    repo.create(transaction)
    fetched = repo.get_by_id(transaction.transaction_id)
    assert fetched is not None
    assert fetched.description == "Test Transaction"
    assert fetched.company_id == company_id 