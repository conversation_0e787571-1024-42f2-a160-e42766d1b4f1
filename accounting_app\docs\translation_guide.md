# دليل الترجمة - برنامج المحاسبة الذكي

## نظرة عامة

تم تحديث جميع النصوص في التطبيق لتكون قابلة للترجمة باستخدام نظام الترجمة المدمج في Qt. هذا يضمن أن التطبيق يمكن ترجمته بسهولة إلى أي لغة دون الحاجة لتعديل الكود.

## الملفات المحدثة

### 1. ملفات الواجهة (UI)
- `ui/topbar.py` - الشريط العلوي
- `ui/main_window.py` - النافذة الرئيسية
- `ui/sidebar.py` - الشريط الجانبي
- `ui/content_area.py` - منطقة المحتوى
- `ui/footer.py` - الشريط السفلي
- `ui/notification_panel.py` - لوحة الإشعارات
- `ui/login_dialog.py` - نافذة تسجيل الدخول
- `ui/ai_assistant.py` - مساعد الذكاء الصناعي
- `ui/loading_overlay.py` - شاشة التحميل

### 2. ملفات الترجمة
- `translations/ar.ts` - الترجمة العربية (اللغة الأساسية)
- `translations/en.ts` - الترجمة الإنجليزية
- `translations/fr.ts` - الترجمة الفرنسية

## كيفية استخدام الترجمة

### 1. في الكود

#### استخدام `self.tr()`:
```python
# للأزرار والعناوين
self.button = QPushButton(self.tr("حفظ"))

# للنصوص الديناميكية
message = self.tr("تم حفظ {} بنجاح").format(filename)

# للرسائل
QMessageBox.information(self, self.tr("نجح"), self.tr("تم الحفظ بنجاح"))
```

#### استخدام `QCoreApplication.translate()`:
```python
# للنصوص في الملفات الرئيسية
title = QCoreApplication.translate("MainWindow", "برنامج المحاسبة الذكي")
```

### 2. إضافة نصوص جديدة

عند إضافة نص جديد:

1. استخدم `self.tr()` بدلاً من النص المباشر
2. أضف الترجمة إلى ملفات `.ts` المناسبة
3. تأكد من أن النص بالعربية في الكود

```python
# ❌ خطأ
self.label = QLabel("نص جديد")

# ✅ صحيح
self.label = QLabel(self.tr("نص جديد"))
```

### 3. تحديث ملفات الترجمة

#### إضافة ترجمة جديدة إلى `ar.ts`:
```xml
<message>
    <source>نص جديد</source>
    <translation>نص جديد</translation>
</message>
```

#### إضافة ترجمة إلى `en.ts`:
```xml
<message>
    <source>نص جديد</source>
    <translation>New Text</translation>
</message>
```

#### إضافة ترجمة إلى `fr.ts`:
```xml
<message>
    <source>نص جديد</source>
    <translation>Nouveau Texte</translation>
</message>
```

## أفضل الممارسات

### 1. تسمية السياق (Context)
استخدم أسماء سياق واضحة ومنظمة:

- `MainWindow` - للنافذة الرئيسية
- `TopBar` - للشريط العلوي
- `SideBar` - للشريط الجانبي
- `ContentArea` - لمنطقة المحتوى
- `Footer` - للشريط السفلي
- `LoginDialog` - لنافذة تسجيل الدخول
- `AIAssistant` - لمساعد الذكاء الصناعي

### 2. النصوص الديناميكية
استخدم `format()` للنصوص التي تحتوي على متغيرات:

```python
# ✅ صحيح
self.tr("المستخدم: {} ({})").format(username, role)

# ❌ خطأ
self.tr(f"المستخدم: {username} ({role})")
```

### 3. الرسائل الطويلة
للرسائل الطويلة، استخدم متغيرات منفصلة:

```python
title = self.tr("تأكيد الحذف")
message = self.tr("هل أنت متأكد أنك تريد حذف {}؟").format(item_name)
QMessageBox.question(self, title, message)
```

### 4. النصوص المتكررة
استخدم نفس المفتاح للنصوص المتكررة:

```python
# في عدة أماكن
self.tr("حفظ")  # سيتم ترجمته مرة واحدة
self.tr("إلغاء")  # سيتم ترجمته مرة واحدة
```

## إضافة لغة جديدة

### 1. إنشاء ملف الترجمة
```bash
# إنشاء ملف .ts جديد
pylupdate6 -translate-function tr main.py -ts translations/new_lang.ts
```

### 2. إضافة اللغة إلى الإعدادات
في `core/settings.py`:
```python
SUPPORTED_LANGS = ["ar", "en", "fr", "new_lang"]
```

### 3. إضافة اللغة إلى واجهة المستخدم
في `ui/topbar.py`:
```python
action_new = QAction(self.tr("New Language 🇳🇱"), self)
action_new.triggered.connect(lambda: self.event_bus.emit(AppEvents.LANGUAGE_CHANGED, "new_lang"))
```

## أدوات الترجمة

### 1. Qt Linguist
استخدم Qt Linguist لتحرير ملفات الترجمة:
```bash
linguist translations/en.ts
```

### 2. pylupdate6
لتحديث ملفات الترجمة من الكود:
```bash
pylupdate6 -translate-function tr *.py -ts translations/*.ts
```

### 3. lrelease
لتحويل ملفات .ts إلى .qm:
```bash
lrelease translations/*.ts
```

## اختبار الترجمة

### 1. تغيير اللغة في التطبيق
1. انقر على زر اللغة في الشريط العلوي
2. اختر اللغة المطلوبة
3. تأكد من تحديث جميع النصوص

### 2. اختبار الاتجاه
- العربية: من اليمين إلى اليسار
- الإنجليزية والفرنسية: من اليسار إلى اليمين

### 3. اختبار الطول
تأكد من أن النصوص المترجمة لا تسبب مشاكل في التخطيط.

## استكشاف الأخطاء

### 1. النص لا يظهر مترجماً
- تأكد من استخدام `self.tr()` أو `QCoreApplication.translate()`
- تحقق من وجود الترجمة في ملف `.ts`
- تأكد من تحميل ملف الترجمة

### 2. خطأ في الترميز
- تأكد من حفظ ملفات `.ts` بترميز UTF-8
- تحقق من عدم وجود أحرف خاصة غير مدعومة

### 3. النص يظهر فارغاً
- تحقق من وجود `<translation>` في ملف `.ts`
- تأكد من عدم وجود أخطاء في XML

## أمثلة عملية

### مثال 1: إضافة زر جديد
```python
# في الكود
self.new_button = QPushButton(self.tr("زر جديد"))

# في ar.ts
<message>
    <source>زر جديد</source>
    <translation>زر جديد</translation>
</message>

# في en.ts
<message>
    <source>زر جديد</source>
    <translation>New Button</translation>
</message>
```

### مثال 2: رسالة خطأ
```python
# في الكود
error_msg = self.tr("فشل في حفظ الملف: {}").format(error_details)
QMessageBox.critical(self, self.tr("خطأ"), error_msg)

# في ar.ts
<message>
    <source>فشل في حفظ الملف: {}</source>
    <translation>فشل في حفظ الملف: {}</translation>
</message>
<message>
    <source>خطأ</source>
    <translation>خطأ</translation>
</message>
```

## الخلاصة

بعد هذا التحديث، أصبح التطبيق جاهزاً للترجمة بالكامل. جميع النصوص تستخدم نظام الترجمة المدمج في Qt، مما يضمن:

1. **سهولة الترجمة** - لا حاجة لتعديل الكود
2. **المرونة** - إضافة لغات جديدة بسهولة
3. **الاتساق** - نفس النص يترجم في كل مكان
4. **الصيانة** - تحديث الترجمات منفصل عن الكود

لتطوير ميزات جديدة، تأكد دائماً من استخدام `self.tr()` أو `QCoreApplication.translate()` لجميع النصوص التي تظهر للمستخدم. 