"""
واجهة مرتجعات الشراء - واجهة شاملة لإدارة جميع مرتجعات المشتريات
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QPushButton, QLineEdit, QComboBox, QLabel, QGroupBox, QFormLayout,
    QDialog, QDialogButtonBox, QTabWidget, QTextEdit, QSpinBox,
    QDoubleSpinBox, QDateEdit, QCheckBox, QMessageBox, QHeaderView,
    QFrame, QScrollArea, QGridLayout, QSplitter
)
from PySide6.QtCore import Qt, QDate, Signal
from PySide6.QtGui import QIcon, QFont
from datetime import datetime, date
import logging

# استيراد النماذج
from ..models.purchase_return import (
    PurchaseReturn, PurchaseReturnItem, ReturnReason, ReturnStatus
)


class PurchaseReturnsTab(QWidget):
    """التبويب الرئيسي لمرتجعات الشراء"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("PurchaseReturnsTab")
        self.returns_data = []
        self.init_ui()
        self.load_sample_data()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)
        
        # شريط الأدوات العلوي
        toolbar = self.create_toolbar()
        layout.addWidget(toolbar)
        
        # منطقة البحث والفلترة
        search_area = self.create_search_area()
        layout.addWidget(search_area)
        
        # المحتوى الرئيسي
        splitter = QSplitter(Qt.Horizontal)
        
        # قائمة المرتجعات (الجانب الأيسر)
        returns_list = self.create_returns_list()
        splitter.addWidget(returns_list)
        
        # تفاصيل المرتجع (الجانب الأيمن)
        return_details = self.create_return_details()
        splitter.addWidget(return_details)
        
        splitter.setSizes([500, 700])
        layout.addWidget(splitter)
        
        # شريط الحالة
        status_bar = self.create_status_bar()
        layout.addWidget(status_bar)
    
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar = QFrame()
        toolbar.setFrameStyle(QFrame.StyledPanel)
        layout = QHBoxLayout(toolbar)
        
        # أزرار العمليات الرئيسية
        self.add_return_btn = QPushButton(self.tr("إنشاء مرتجع جديد"))
        self.add_return_btn.setIcon(QIcon("accounting_app/resources/icons/add.svg"))
        self.add_return_btn.clicked.connect(self.add_new_return)
        
        self.edit_return_btn = QPushButton(self.tr("تعديل"))
        self.edit_return_btn.setIcon(QIcon("accounting_app/resources/icons/edit.svg"))
        self.edit_return_btn.clicked.connect(self.edit_return)
        self.edit_return_btn.setEnabled(False)
        
        self.approve_return_btn = QPushButton(self.tr("موافقة"))
        self.approve_return_btn.setIcon(QIcon("accounting_app/resources/icons/approve.svg"))
        self.approve_return_btn.clicked.connect(self.approve_return)
        self.approve_return_btn.setEnabled(False)
        
        self.ship_return_btn = QPushButton(self.tr("شحن"))
        self.ship_return_btn.setIcon(QIcon("accounting_app/resources/icons/ship.svg"))
        self.ship_return_btn.clicked.connect(self.ship_return)
        self.ship_return_btn.setEnabled(False)
        
        self.print_return_btn = QPushButton(self.tr("طباعة"))
        self.print_return_btn.setIcon(QIcon("accounting_app/resources/icons/print.svg"))
        self.print_return_btn.clicked.connect(self.print_return)
        self.print_return_btn.setEnabled(False)
        
        # إضافة الأزرار
        layout.addWidget(self.add_return_btn)
        layout.addWidget(self.edit_return_btn)
        layout.addWidget(self.approve_return_btn)
        layout.addWidget(self.ship_return_btn)
        layout.addWidget(self.print_return_btn)
        layout.addStretch()
        
        return toolbar
    
    def create_search_area(self):
        """إنشاء منطقة البحث والفلترة"""
        search_group = QGroupBox(self.tr("البحث والفلترة"))
        layout = QHBoxLayout(search_group)
        
        # حقل البحث
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText(self.tr("البحث في المرتجعات..."))
        self.search_input.textChanged.connect(self.filter_returns)
        
        # فلتر الحالة
        self.status_filter = QComboBox()
        self.status_filter.addItem(self.tr("جميع الحالات"), "")
        for status in ReturnStatus:
            self.status_filter.addItem(status.value, status.name)
        self.status_filter.currentTextChanged.connect(self.filter_returns)
        
        # فلتر سبب الإرجاع
        self.reason_filter = QComboBox()
        self.reason_filter.addItem(self.tr("جميع الأسباب"), "")
        for reason in ReturnReason:
            self.reason_filter.addItem(reason.value, reason.name)
        self.reason_filter.currentTextChanged.connect(self.filter_returns)
        
        # فلتر التاريخ
        self.date_from = QDateEdit()
        self.date_from.setDate(QDate.currentDate().addMonths(-1))
        self.date_to = QDateEdit()
        self.date_to.setDate(QDate.currentDate())
        
        layout.addWidget(QLabel(self.tr("البحث:")))
        layout.addWidget(self.search_input)
        layout.addWidget(QLabel(self.tr("الحالة:")))
        layout.addWidget(self.status_filter)
        layout.addWidget(QLabel(self.tr("السبب:")))
        layout.addWidget(self.reason_filter)
        layout.addWidget(QLabel(self.tr("من:")))
        layout.addWidget(self.date_from)
        layout.addWidget(QLabel(self.tr("إلى:")))
        layout.addWidget(self.date_to)
        layout.addStretch()
        
        return search_group
    
    def create_returns_list(self):
        """إنشاء قائمة المرتجعات"""
        list_group = QGroupBox(self.tr("قائمة المرتجعات"))
        layout = QVBoxLayout(list_group)
        
        # جدول المرتجعات
        self.returns_table = QTableWidget()
        self.returns_table.setColumnCount(6)
        self.returns_table.setHorizontalHeaderLabels([
            self.tr("رقم المرتجع"),
            self.tr("رقم الفاتورة"),
            self.tr("المورد"),
            self.tr("التاريخ"),
            self.tr("المبلغ"),
            self.tr("الحالة")
        ])
        
        # تخصيص الجدول
        header = self.returns_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.Interactive)
        
        self.returns_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.returns_table.setSelectionMode(QTableWidget.SingleSelection)
        self.returns_table.setAlternatingRowColors(True)
        self.returns_table.itemSelectionChanged.connect(self.on_return_selected)
        
        layout.addWidget(self.returns_table)
        
        # معلومات إحصائية
        stats_layout = QHBoxLayout()
        self.total_returns_label = QLabel(self.tr("إجمالي المرتجعات: 0"))
        self.total_amount_label = QLabel(self.tr("إجمالي المبلغ: 0.00"))
        self.pending_returns_label = QLabel(self.tr("المرتجعات المعلقة: 0"))
        
        stats_layout.addWidget(self.total_returns_label)
        stats_layout.addWidget(self.total_amount_label)
        stats_layout.addWidget(self.pending_returns_label)
        stats_layout.addStretch()
        
        layout.addLayout(stats_layout)
        
        return list_group
    
    def create_return_details(self):
        """إنشاء منطقة تفاصيل المرتجع"""
        details_group = QGroupBox(self.tr("تفاصيل المرتجع"))
        layout = QVBoxLayout(details_group)
        
        # تبويبات التفاصيل
        self.details_tabs = QTabWidget()
        
        # تبويب المعلومات الأساسية
        basic_tab = self.create_basic_info_tab()
        self.details_tabs.addTab(basic_tab, self.tr("المعلومات الأساسية"))
        
        # تبويب العناصر المرتجعة
        items_tab = self.create_items_tab()
        self.details_tabs.addTab(items_tab, self.tr("العناصر المرتجعة"))
        
        layout.addWidget(self.details_tabs)
        
        return details_group
    
    def create_basic_info_tab(self):
        """إنشاء تبويب المعلومات الأساسية"""
        tab = QWidget()
        scroll = QScrollArea(tab)
        scroll.setWidgetResizable(True)
        
        container = QWidget()
        layout = QFormLayout(container)
        
        # المعلومات الأساسية
        self.return_number_label = QLabel()
        self.invoice_number_label = QLabel()
        self.supplier_name_label = QLabel()
        self.return_date_label = QLabel()
        self.status_label = QLabel()
        self.total_amount_label = QLabel()
        self.refund_amount_label = QLabel()
        self.refund_method_label = QLabel()
        self.notes_label = QLabel()
        
        # إضافة الحقول
        layout.addRow(self.tr("رقم المرتجع:"), self.return_number_label)
        layout.addRow(self.tr("رقم الفاتورة:"), self.invoice_number_label)
        layout.addRow(self.tr("المورد:"), self.supplier_name_label)
        layout.addRow(self.tr("تاريخ الإرجاع:"), self.return_date_label)
        layout.addRow(self.tr("الحالة:"), self.status_label)
        layout.addRow(self.tr("إجمالي المبلغ:"), self.total_amount_label)
        layout.addRow(self.tr("مبلغ الاسترداد:"), self.refund_amount_label)
        layout.addRow(self.tr("طريقة الاسترداد:"), self.refund_method_label)
        layout.addRow(self.tr("ملاحظات:"), self.notes_label)
        
        scroll.setWidget(container)
        
        tab_layout = QVBoxLayout(tab)
        tab_layout.addWidget(scroll)
        
        return tab
    
    def create_items_tab(self):
        """إنشاء تبويب العناصر المرتجعة"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # جدول العناصر المرتجعة
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(6)
        self.items_table.setHorizontalHeaderLabels([
            self.tr("المنتج"),
            self.tr("الكمية المرتجعة"),
            self.tr("سعر الوحدة"),
            self.tr("المجموع"),
            self.tr("سبب الإرجاع"),
            self.tr("الحالة")
        ])
        
        layout.addWidget(self.items_table)
        
        return tab
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_bar = QFrame()
        status_bar.setFrameStyle(QFrame.StyledPanel)
        layout = QHBoxLayout(status_bar)
        
        self.status_label = QLabel(self.tr("جاهز"))
        layout.addWidget(self.status_label)
        layout.addStretch()
        
        return status_bar
    
    def load_sample_data(self):
        """تحميل بيانات وهمية للاختبار"""
        sample_returns = [
            {
                'return_number': 'PR-202401-1001',
                'invoice_number': 'PI-202401-1001',
                'supplier_name': 'شركة المواد الغذائية المتقدمة',
                'return_date': '2024-01-25',
                'status': ReturnStatus.APPROVED,
                'total_amount': 2500.00,
                'refund_amount': 2500.00,
                'refund_method': 'إشعار دائن',
                'notes': 'منتجات تالفة'
            },
            {
                'return_number': 'PR-202401-1002',
                'invoice_number': 'PI-202401-1002',
                'supplier_name': 'مؤسسة التقنية الحديثة',
                'return_date': '2024-01-28',
                'status': ReturnStatus.PENDING_APPROVAL,
                'total_amount': 1800.00,
                'refund_amount': 1800.00,
                'refund_method': 'تحويل بنكي',
                'notes': 'صنف خاطئ'
            }
        ]
        
        self.returns_data = sample_returns
        self.populate_returns_table()
        self.update_statistics()
    
    def populate_returns_table(self):
        """ملء جدول المرتجعات"""
        self.returns_table.setRowCount(len(self.returns_data))
        
        for row, return_data in enumerate(self.returns_data):
            # رقم المرتجع
            self.returns_table.setItem(row, 0, QTableWidgetItem(return_data['return_number']))
            
            # رقم الفاتورة
            self.returns_table.setItem(row, 1, QTableWidgetItem(return_data['invoice_number']))
            
            # المورد
            self.returns_table.setItem(row, 2, QTableWidgetItem(return_data['supplier_name']))
            
            # التاريخ
            self.returns_table.setItem(row, 3, QTableWidgetItem(return_data['return_date']))
            
            # المبلغ
            self.returns_table.setItem(row, 4, QTableWidgetItem(f"{return_data['total_amount']:,.2f}"))
            
            # الحالة
            status_item = QTableWidgetItem(return_data['status'].value)
            if return_data['status'] == ReturnStatus.APPROVED:
                status_item.setBackground(Qt.green)
            elif return_data['status'] == ReturnStatus.PENDING_APPROVAL:
                status_item.setBackground(Qt.yellow)
            else:
                status_item.setBackground(Qt.red)
            self.returns_table.setItem(row, 5, status_item)
    
    def update_statistics(self):
        """تحديث الإحصائيات"""
        total_returns = len(self.returns_data)
        total_amount = sum(return_data['total_amount'] for return_data in self.returns_data)
        pending_returns = sum(1 for r in self.returns_data if r['status'] == ReturnStatus.PENDING_APPROVAL)
        
        self.total_returns_label.setText(f"إجمالي المرتجعات: {total_returns}")
        self.total_amount_label.setText(f"إجمالي المبلغ: {total_amount:,.2f}")
        self.pending_returns_label.setText(f"المرتجعات المعلقة: {pending_returns}")
    
    def filter_returns(self):
        """فلترة المرتجعات حسب معايير البحث"""
        # تنفيذ منطق الفلترة
        pass
    
    def on_return_selected(self):
        """عند اختيار مرتجع من القائمة"""
        current_row = self.returns_table.currentRow()
        if current_row >= 0:
            # تفعيل الأزرار
            self.edit_return_btn.setEnabled(True)
            self.approve_return_btn.setEnabled(True)
            self.ship_return_btn.setEnabled(True)
            self.print_return_btn.setEnabled(True)
            
            # عرض تفاصيل المرتجع
            return_number = self.returns_table.item(current_row, 0).text()
            self.display_return_details(return_number)
    
    def display_return_details(self, return_number):
        """عرض تفاصيل المرتجع المختار"""
        return_data = next((r for r in self.returns_data if r['return_number'] == return_number), None)
        if not return_data:
            return
        
        # تحديث المعلومات الأساسية
        self.return_number_label.setText(return_data['return_number'])
        self.invoice_number_label.setText(return_data['invoice_number'])
        self.supplier_name_label.setText(return_data['supplier_name'])
        self.return_date_label.setText(return_data['return_date'])
        self.status_label.setText(return_data['status'].value)
        self.total_amount_label.setText(f"{return_data['total_amount']:,.2f}")
        self.refund_amount_label.setText(f"{return_data['refund_amount']:,.2f}")
        self.refund_method_label.setText(return_data['refund_method'])
        self.notes_label.setText(return_data['notes'])
    
    def add_new_return(self):
        """إضافة مرتجع جديد"""
        QMessageBox.information(self, self.tr("إضافة مرتجع"), self.tr("سيتم إضافة مرتجع جديد"))
    
    def edit_return(self):
        """تعديل المرتجع المختار"""
        QMessageBox.information(self, self.tr("تعديل مرتجع"), self.tr("سيتم تعديل المرتجع"))
    
    def approve_return(self):
        """الموافقة على المرتجع"""
        QMessageBox.information(self, self.tr("موافقة"), self.tr("تم الموافقة على المرتجع"))
    
    def ship_return(self):
        """شحن المرتجع"""
        QMessageBox.information(self, self.tr("شحن"), self.tr("تم شحن المرتجع"))
    
    def print_return(self):
        """طباعة المرتجع"""
        QMessageBox.information(self, self.tr("طباعة"), self.tr("سيتم طباعة المرتجع"))
