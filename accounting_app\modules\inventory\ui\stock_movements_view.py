from PySide6.QtWidgets import QWidget, QVBoxLayout, QLineEdit, QTableWidget, QTableWidgetItem, QHeaderView, QPushButton, QDialog, QFormLayout, QDialogButtonBox

class StockMovementDialog(QDialog):
    def __init__(self, parent=None, movement=None):
        super().__init__(parent)
        self.setWindowTitle(self.tr("إضافة / تعديل حركة مخزون"))
        self.movement = movement
        self.init_ui()

    def init_ui(self):
        layout = QFormLayout(self)
        self.product_edit = QLineEdit(self)
        self.type_edit = QLineEdit(self)
        self.qty_edit = QLineEdit(self)
        self.warehouse_edit = QLineEdit(self)
        layout.addRow(self.tr("المنتج:"), self.product_edit)
        layout.addRow(self.tr("النوع (دخول/خروج):"), self.type_edit)
        layout.addRow(self.tr("الكمية:"), self.qty_edit)
        layout.addRow(self.tr("المخزن:"), self.warehouse_edit)
        if self.movement:
            self.product_edit.setText(self.movement["product"])
            self.type_edit.setText(self.movement["type"])
            self.qty_edit.setText(str(self.movement["qty"]))
            self.warehouse_edit.setText(self.movement["warehouse"])
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel, self)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addRow(buttons)

    def get_data(self):
        return {
            "product": self.product_edit.text(),
            "type": self.type_edit.text(),
            "qty": self.qty_edit.text(),
            "warehouse": self.warehouse_edit.text(),
        }

class StockMovementsTab(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("StockMovementsTab")
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout(self)
        self.search_box = QLineEdit(self)
        self.search_box.setPlaceholderText(self.tr("بحث في الحركات..."))
        self.search_box.textChanged.connect(self.filter_table)
        layout.addWidget(self.search_box)
        self.table = QTableWidget(self)
        self.table.setColumnCount(5)
        self.table.setHorizontalHeaderLabels([
            self.tr("المنتج"), self.tr("النوع"), self.tr("الكمية"), self.tr("المخزن"), self.tr("إجراءات")
        ])
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        layout.addWidget(self.table)
        self.add_btn = QPushButton(self.tr("إضافة حركة جديدة"), self)
        self.add_btn.clicked.connect(self.add_movement)
        layout.addWidget(self.add_btn)
        self.setLayout(layout)
        self.movements = [
            {"product": "منتج 1", "type": "دخول", "qty": 20, "warehouse": "المخزن الرئيسي"},
            {"product": "منتج 2", "type": "خروج", "qty": 5, "warehouse": "مخزن فرعي"},
        ]
        self.refresh_table()

    def refresh_table(self):
        self.table.setRowCount(0)
        for movement in self.movements:
            row = self.table.rowCount()
            self.table.insertRow(row)
            self.table.setItem(row, 0, QTableWidgetItem(movement["product"]))
            self.table.setItem(row, 1, QTableWidgetItem(movement["type"]))
            self.table.setItem(row, 2, QTableWidgetItem(str(movement["qty"])))
            self.table.setItem(row, 3, QTableWidgetItem(movement["warehouse"]))
            edit_btn = QPushButton(self.tr("تعديل"))
            edit_btn.clicked.connect(lambda _, r=row: self.edit_movement(r))
            self.table.setCellWidget(row, 4, edit_btn)

    def filter_table(self, text):
        for row in range(self.table.rowCount()):
            match = False
            for col in range(4):
                item = self.table.item(row, col)
                if item and text in item.text():
                    match = True
            self.table.setRowHidden(row, not match)

    def add_movement(self):
        dialog = StockMovementDialog(self)
        if dialog.exec() == QDialog.Accepted:
            data = dialog.get_data()
            self.movements.append({
                "product": data["product"],
                "type": data["type"],
                "qty": int(data["qty"]),
                "warehouse": data["warehouse"],
            })
            self.refresh_table()

    def edit_movement(self, row):
        movement = self.movements[row]
        dialog = StockMovementDialog(self, movement)
        if dialog.exec() == QDialog.Accepted:
            data = dialog.get_data()
            self.movements[row] = {
                "product": data["product"],
                "type": data["type"],
                "qty": int(data["qty"]),
                "warehouse": data["warehouse"],
            }
            self.refresh_table() 