"""
نموذج مرتجعات الشراء - يحتوي على جميع المعلومات المتعلقة بمرتجعات المشتريات
"""

from dataclasses import dataclass, field
from typing import Optional, List, Dict, Any
from datetime import datetime, date
from enum import Enum
from decimal import Decimal


class ReturnReason(Enum):
    """أسباب الإرجاع"""
    DEFECTIVE = "معيب"
    WRONG_ITEM = "صنف خاطئ"
    DAMAGED = "تالف"
    EXPIRED = "منتهي الصلاحية"
    EXCESS_QUANTITY = "كمية زائدة"
    QUALITY_ISSUE = "مشكلة في الجودة"
    NOT_AS_DESCRIBED = "غير مطابق للوصف"
    OTHER = "أخرى"


class ReturnStatus(Enum):
    """حالة المرتجع"""
    DRAFT = "مسودة"
    PENDING_APPROVAL = "في انتظار الموافقة"
    APPROVED = "موافق عليه"
    SHIPPED = "مشحون"
    RECEIVED_BY_SUPPLIER = "مستلم من المورد"
    REFUNDED = "مسترد"
    CANCELLED = "ملغي"


@dataclass
class PurchaseReturnItem:
    """عنصر في مرتجع الشراء"""
    id: Optional[int] = None
    return_id: Optional[int] = None
    invoice_item_id: Optional[int] = None
    product_id: Optional[int] = None
    product_code: str = ""
    product_name: str = ""
    
    # الكميات والأسعار
    quantity_returned: Decimal = Decimal('0')
    unit_price: Decimal = Decimal('0')
    total_amount: Decimal = Decimal('0')
    
    # سبب الإرجاع
    return_reason: ReturnReason = ReturnReason.OTHER
    return_reason_details: str = ""
    
    # معلومات إضافية
    batch_number: str = ""
    serial_number: str = ""
    condition: str = "جيد"  # جيد، تالف، معيب
    notes: str = ""
    
    def __post_init__(self):
        """حساب المجموع"""
        self.total_amount = self.quantity_returned * self.unit_price


@dataclass
class PurchaseReturn:
    """نموذج مرتجع الشراء الرئيسي"""
    
    # المعلومات الأساسية
    id: Optional[int] = None
    return_number: str = ""
    invoice_id: Optional[int] = None
    invoice_number: str = ""
    supplier_id: Optional[int] = None
    supplier_name: str = ""
    
    # التواريخ
    return_date: date = field(default_factory=date.today)
    expected_pickup_date: Optional[date] = None
    actual_pickup_date: Optional[date] = None
    
    # الحالة
    status: ReturnStatus = ReturnStatus.DRAFT
    
    # المعلومات المالية
    total_amount: Decimal = Decimal('0')
    refund_amount: Decimal = Decimal('0')
    refund_method: str = "إشعار دائن"
    
    # العناصر
    items: List[PurchaseReturnItem] = field(default_factory=list)
    
    # ملاحظات
    notes: str = ""
    internal_notes: str = ""
    
    # معلومات النظام
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    created_by: str = ""
    updated_by: str = ""
    
    def __post_init__(self):
        """تهيئة إضافية"""
        if not self.created_at:
            self.created_at = datetime.now()
        self.updated_at = datetime.now()
        
        if not self.return_number:
            self.return_number = self.generate_return_number()
        
        self.calculate_totals()
    
    def generate_return_number(self) -> str:
        """إنشاء رقم المرتجع"""
        today = date.today()
        year = today.year
        month = today.month
        import random
        sequence = random.randint(1000, 9999)
        return f"PR-{year}{month:02d}-{sequence}"
    
    def calculate_totals(self):
        """حساب المجاميع"""
        self.total_amount = sum(item.total_amount for item in self.items)
        if not self.refund_amount:
            self.refund_amount = self.total_amount
