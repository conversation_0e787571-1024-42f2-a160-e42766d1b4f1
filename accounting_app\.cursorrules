# Cursor Settings for Accounting App
# This file helps <PERSON><PERSON><PERSON> understand the project structure

# Python interpreter path
python.interpreter.path = "./.venv/Scripts/python.exe"

# Working directory for running the app
python.terminal.cwd = "."

# Environment variables
python.env.PYTHONPATH = "."

# File associations
files.associations = {
    "*.py": "python",
    "*.qss": "css",
    "*.ts": "xml"
}

# Run configuration
run.configurations = [
    {
        "name": "Run Accounting App",
        "type": "python",
        "request": "launch",
        "program": "main.py",
        "console": "integratedTerminal",
        "cwd": ".",
        "env": {
            "PYTHONPATH": "."
        }
    }
] 