from PySide6.QtWidgets import QWidget, QHBoxLayout, QLabel, QPushButton, QSizePolicy, QSpacerItem, QToolButton, QMenu, QVBoxLayout, QLineEdit
from PySide6.QtCore import Qt, QTimer, QTime, QDate, Signal, QMargins
from PySide6.QtGui import QAction, QIcon
import logging
from .toast import show_error_toast
from .effects import ShadowEffects
from accounting_app.core.events import AppEvents
from accounting_app.core.style_manager import get_resource_path

class TopBar(QWidget):
    theme_changed = Signal()
    def __init__(self, language_controller, event_bus, settings, parent=None):
        super().__init__(parent)
        self.language_controller = language_controller
        self.event_bus = event_bus
        self.settings = settings
        self.setObjectName("TopBar")
        self.setAttribute(Qt.WA_StyledBackground, True)
        self.init_ui()
        self.update_time()
        timer = QTimer(self)
        timer.timeout.connect(self.update_time)
        timer.start(1000)
        logging.info("TopBar initialized.")
        
        # الاشتراك في حدث تغيير اللغة لاستقبال التحديثات
        self.event_bus.subscribe(AppEvents.LANGUAGE_CHANGED, self.reload_texts)
        
        # تطبيق الاتجاه الصحيح عند الإنشاء
        self.update_layout_direction()

    def init_ui(self):
        layout = QHBoxLayout()
        layout.setContentsMargins(16, 8, 16, 8)
        layout.setSpacing(16)
        self.setMinimumHeight(64)
        self.title = QLabel(self.tr("Smart Accounting App"))
        self.title.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Preferred)
        self.time_label = QLabel()
        self.date_label = QLabel()
        self.notif_btn = QPushButton(self.tr("الإشعارات"))
        self.exit_btn = QPushButton(self.tr("خروج"))
        for btn in [self.notif_btn, self.exit_btn]:
            btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
            btn.setCursor(Qt.PointingHandCursor)
        self.lang_btn = QPushButton()
        self.lang_btn.setText(self.tr("اللغة"))
        self.lang_btn.setIcon(QIcon(get_resource_path("lang.svg")))
        self.lang_btn.setMinimumWidth(150)
        self.lang_btn.setCursor(Qt.PointingHandCursor)
        lang_menu = QMenu(self)
        action_ar = QAction(self.tr("العربية 🇸🇦"), self)
        action_en = QAction(self.tr("English 🇬🇧"), self)
        action_fr = QAction(self.tr("Français 🇫🇷"), self)
        lang_menu.addAction(action_ar)
        lang_menu.addAction(action_en)
        lang_menu.addAction(action_fr)
        self.lang_btn.clicked.connect(lambda: lang_menu.exec(self.lang_btn.mapToGlobal(self.lang_btn.rect().bottomLeft())))
        action_ar.triggered.connect(lambda: self.language_controller.set_language("ar"))
        action_en.triggered.connect(lambda: self.language_controller.set_language("en"))
        action_fr.triggered.connect(lambda: self.language_controller.set_language("fr"))
        self.ai_btn = QPushButton(self.tr("الذكاء الصناعي"))
        self.ai_btn.setMinimumWidth(150)
        self.ai_btn.setCursor(Qt.PointingHandCursor)
        self.user_btn = QToolButton()
        self.user_btn.setText("👤")
        self.user_btn.setPopupMode(QToolButton.InstantPopup)
        user_menu = QMenu()
        action_profile = QAction(self.tr("معلومات الحساب"), self)
        action_login = QAction(self.tr("تسجيل الدخول"), self)
        action_logout = QAction(self.tr("تسجيل الخروج"), self)
        user_menu.addAction(action_profile)
        user_menu.addAction(action_login)
        user_menu.addAction(action_logout)
        self.user_btn.setMenu(user_menu)
        self.user_btn.setFixedSize(40, 32)
        self.notif_badge = QLabel("")
        self.notif_badge.setStyleSheet("background: #ef4444; color: #FFF; border-radius: 10px; font-size: 13px; min-width: 20px; min-height: 20px; padding: 0 6px; qproperty-alignment: AlignCenter;")
        self.notif_badge.setVisible(False)
        self.notif_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        ShadowEffects.apply_button_shadow(self.notif_btn)
        ShadowEffects.apply_button_shadow(self.exit_btn)
        ShadowEffects.apply_button_shadow(self.lang_btn)
        ShadowEffects.apply_button_shadow(self.ai_btn)
        notif_btn_layout = QHBoxLayout()
        notif_btn_layout.setContentsMargins(0, 0, 0, 0)
        notif_btn_layout.setSpacing(4)
        notif_btn_layout.addWidget(self.notif_btn)
        notif_btn_layout.addWidget(self.notif_badge)
        notif_btn_widget = QWidget()
        notif_btn_widget.setLayout(notif_btn_layout)
        self.search_box = QLineEdit()
        self.search_box.setPlaceholderText(self.tr("بحث سريع..."))
        self.search_box.setFixedWidth(200)
        self.search_box.setStyleSheet("border-radius: 8px; padding: 4px 12px; background: #FFF; color: #232e3c;")
        self.search_box.textChanged.connect(self.handle_search)
        # ترتيب العناصر:
        layout.addWidget(self.title)
        layout.addWidget(self.time_label)
        layout.addWidget(self.date_label)
        layout.addSpacerItem(QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum))
        layout.addWidget(self.search_box)
        layout.addSpacerItem(QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum))
        layout.addWidget(notif_btn_widget)
        layout.addWidget(self.ai_btn)
        layout.addWidget(self.lang_btn)
        layout.addWidget(self.user_btn)
        layout.addWidget(self.exit_btn)
        self.setLayout(layout)
        # تحميل الأيقونات مع معالجة الأخطاء
        try:
            self.notif_btn.setIcon(QIcon(get_resource_path("bell.svg")))
            self.exit_btn.setIcon(QIcon(get_resource_path("exit.svg")))
            self.lang_btn.setIcon(QIcon(get_resource_path("lang.svg")))
            self.ai_btn.setIcon(QIcon(get_resource_path("ai.svg")))
            self.user_btn.setIcon(QIcon(get_resource_path("user.svg")))
        except Exception as e:
            logging.warning(f"Failed to load icons: {e}")
            # يمكن إضافة أيقونات افتراضية هنا إذا لزم الأمر

    def update_time(self):
        self.time_label.setText(QTime.currentTime().toString("HH:mm:ss"))
        self.date_label.setText(QDate.currentDate().toString("yyyy-MM-dd"))

    def reload_texts(self, lang_code=None):
        try:
            self.title.setText(self.tr("Smart Accounting App"))
            self.notif_btn.setText(self.tr("الإشعارات"))
            self.exit_btn.setText(self.tr("خروج"))
            # نص زر اللغة حسب اللغة الحالية
            code = self.language_controller.current_lang
            if code == "ar":
                self.lang_btn.setText(self.tr("اللغة"))
            elif code == "en":
                self.lang_btn.setText(self.tr("Language"))
            else:
                self.lang_btn.setText(self.tr("Langue"))
            self.ai_btn.setText(self.tr("الذكاء الصناعي"))
            self.user_btn.setText("👤")
            
            # تحديث اتجاه التخطيط
            self.update_layout_direction()
            
            logging.info(f"TopBar texts reloaded for language: {code}")
        except Exception as e:
            logging.exception("فشل تحديث نصوص الشريط العلوي")
            show_error_toast(self, self.tr("تعذر تحديث نصوص الشريط العلوي"))
    
    def update_layout_direction(self):
        """تحديث اتجاه التخطيط للشريط العلوي"""
        try:
            direction = self.language_controller.get_language_direction(self.language_controller.current_lang)
            self.setLayoutDirection(direction)
            
            # تحديث اتجاه جميع العناصر الفرعية
            for child in self.findChildren(QWidget):
                child.setLayoutDirection(direction)
                
            logging.info(f"TopBar layout direction updated to: {'RTL' if direction == Qt.RightToLeft else 'LTR'}")
        except Exception as e:
            logging.error(f"Failed to update TopBar layout direction: {e}")

    def set_notif_badge(self, count):
        if count > 0:
            self.notif_badge.setText(str(count))
            self.notif_badge.setVisible(True)
            self.notif_badge.setStyleSheet("background: #ef4444; color: #FFF; border-radius: 10px; font-size: 13px; min-width: 20px; min-height: 20px; padding: 0 6px; qproperty-alignment: AlignCenter; border: 2px solid #FFF;")
        else:
            self.notif_badge.setVisible(False) 

    def handle_search(self, text):
        # فلترة التبويبات المفتوحة في ContentArea
        main_window = self.window()
        if hasattr(main_window, 'content_area'):
            for i in range(main_window.content_area.count()):
                tab_text = main_window.content_area.tabText(i)
                tab_widget = main_window.content_area.widget(i)
                if text.strip() == "" or text in tab_text:
                    main_window.content_area.tabBar().setTabVisible(i, True)
                else:
                    main_window.content_area.tabBar().setTabVisible(i, False) 

    def show_help(self):
        from PySide6.QtWidgets import QMessageBox
        QMessageBox.information(self, self.tr("المساعدة"), self.tr("للمساعدة والتوثيق: شاهد الفيديو التعليمي أو راجع دليل الاستخدام.")) 

    def handle_login(self):
        main_window = self.window()
        if hasattr(main_window, 'show_login_dialog'):
            main_window.show_login_dialog()

    def handle_logout(self):
        main_window = self.window()
        if hasattr(main_window, 'set_user_and_role'):
            main_window.set_user_and_role("guest", self.tr("ضيف")) 

    def sizeHint(self):
        hint = super().sizeHint()
        return hint.expandedTo(self.minimumSizeHint()).grownBy(QMargins(30, 0, 30, 0)) 
    