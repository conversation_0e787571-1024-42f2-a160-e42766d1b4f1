from PySide6.QtWidgets import <PERSON><PERSON><PERSON>og, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton, QCheckBox
from PySide6.QtCore import Qt
from .toast import show_error_toast
from .effects import ShadowEffects

class LoginDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle(self.tr("تسجيل الدخول"))
        self.setModal(True)
        self.setFixedSize(350, 220)
        layout = QVBoxLayout()
        layout.setAlignment(Qt.AlignCenter)
        self.user_label = QLabel(self.tr("اسم المستخدم:"))
        self.user_input = QLineEdit()
        self.pass_label = QLabel(self.tr("كلمة المرور:"))
        self.pass_input = QLineEdit()
        self.pass_input.setEchoMode(QLineEdit.Password)
        self.remember_check = QCheckBox(self.tr("تذكرني"))
        self.login_btn = QPushButton(self.tr("دخول"))
        self.forgot_btn = QPushButton(self.tr("نسيت كلمة المرور؟"))
        self.forgot_btn.setFlat(True)
        self.forgot_btn.setStyleSheet("color: #2563eb; text-decoration: underline; background: transparent;")
        layout.addWidget(self.user_label)
        layout.addWidget(self.user_input)
        layout.addWidget(self.pass_label)
        layout.addWidget(self.pass_input)
        layout.addWidget(self.remember_check)
        btn_layout = QHBoxLayout()
        btn_layout.addWidget(self.login_btn)
        btn_layout.addWidget(self.forgot_btn)
        layout.addLayout(btn_layout)
        self.setLayout(layout)
        
        # تطبيق تأثيرات الظل
        ShadowEffects.apply_dialog_shadow(self)
        ShadowEffects.apply_button_shadow(self.login_btn)
        
        self.login_btn.clicked.connect(self.handle_login)
        self.forgot_btn.clicked.connect(self.handle_forgot)

    def handle_login(self):
        if not self.user_input.text() or not self.pass_input.text():
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.warning(self, self.tr("خطأ"), self.tr("يرجى إدخال اسم المستخدم وكلمة المرور."))
            show_error_toast(self, self.tr("يرجى إدخال اسم المستخدم وكلمة المرور."))
            return
        self.accept()

    def handle_forgot(self):
        from PySide6.QtWidgets import QMessageBox
        QMessageBox.information(self, self.tr("استعادة كلمة المرور"), self.tr("يرجى التواصل مع الدعم الفني لاستعادة كلمة المرور.")) 