import json
import os
import logging
from ..app_config import (
    LANG_DEFAULT, TRANSLATIONS_PATH, RESOURCES_PATH, 
    SUPPORTED_LANGS, APP_TITLE, APP_VERSION, NOTIFICATION_SOUND
)
from ..utils import validate_config_file, create_valid_config_file, ConfigValidator

logger = logging.getLogger(__name__)

# إعدادات JWT على مستوى الملف ليستوردها أي جزء من المشروع بسهولة
JWT_SECRET = "your_jwt_secret_key"  # يفضل تحميلها من متغير بيئة في الإنتاج
JWT_ALGORITHM = "HS256"
JWT_EXP_MINUTES = 60

class AppSettings:
    """إدارة إعدادات التطبيق"""
    
    def __init__(self, config_path="config.json"):
        self.config_path = config_path
        
        # إعدادات ثابتة
        self.LANG_DEFAULT = LANG_DEFAULT
        self.TRANSLATIONS_PATH = TRANSLATIONS_PATH
        self.RESOURCES_PATH = RESOURCES_PATH
        self.THEME_DEFAULT = "default"  # تغيير من "dark" إلى "default"
        self.SUPPORTED_LANGS = SUPPORTED_LANGS
        self.APP_TITLE = APP_TITLE
        self.APP_VERSION = APP_VERSION
        self.NOTIFICATION_SOUND = NOTIFICATION_SOUND
        
        # إعدادات JWT
        self.JWT_SECRET = JWT_SECRET
        self.JWT_ALGORITHM = JWT_ALGORITHM
        self.JWT_EXP_MINUTES = JWT_EXP_MINUTES
        
        # إعدادات قابلة للتغيير
        self.defaults = {
            "language": LANG_DEFAULT,
            "theme": self.THEME_DEFAULT,
        }
        
        self.settings = self.defaults.copy()
        self.load()

    def load(self):
        """تحميل الإعدادات من الملف"""
        logger.info(f"Loading settings from {self.config_path}")
        
        # التحقق من وجود الملف
        if not os.path.exists(self.config_path):
            logger.warning(f"Config file not found: {self.config_path}")
            logger.info("Creating default config file...")
            if create_valid_config_file(self.config_path):
                logger.info("Default config file created successfully")
            else:
                logger.error("Failed to create default config file")
                return
        
        # التحقق من صحة الإعدادات
        validation_result = validate_config_file(self.config_path)
        
        if not validation_result.is_valid:
            logger.error("Configuration validation failed:")
            for error in validation_result.errors:
                logger.error(f"  - {error}")
        
        if validation_result.warnings:
            logger.warning("Configuration warnings:")
            for warning in validation_result.warnings:
                logger.warning(f"  - {warning}")
        
        # تطبيق الإصلاحات إذا كانت متاحة
        if validation_result.fixed_values:
            logger.info("Applying configuration fixes...")
            try:
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # تطبيق الإصلاحات
                validator = ConfigValidator()
                fixed_data = validator.apply_fixes(data, validation_result.fixed_values)
                
                # حفظ الإعدادات المصلحة
                with open(self.config_path, 'w', encoding='utf-8') as f:
                    json.dump(fixed_data, f, ensure_ascii=False, indent=2)
                
                logger.info("Configuration fixes applied and saved")
                data = fixed_data
                
            except Exception as e:
                logger.error(f"Failed to apply configuration fixes: {e}")
                data = {}
        else:
            # تحميل الإعدادات العادية
            try:
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                logger.info("Settings loaded successfully")
            except Exception as e:
                logger.error(f"Failed to load settings: {e}")
                data = {}
        
        # تحديث الإعدادات مع الحفاظ على التوافق مع النظام القديم
        self._update_settings_from_config(data)
        self._validate_settings()
        
        # طباعة تشخيصية للثيم
        print(f"🔍 [DEBUG] Theme loaded: {self.settings.get('theme', 'NOT_FOUND')}")
        print(f"🔍 [DEBUG] Full config theme: {data.get('user_preferences', {}).get('theme', 'NOT_FOUND') if 'user_preferences' in data else 'NO_USER_PREFS'}")

    def save(self):
        """حفظ الإعدادات إلى الملف"""
        try:
            # طباعة تشخيصية قبل الحفظ
            print(f"💾 [DEBUG] Saving theme: {self.settings.get('theme', 'NOT_FOUND')}")
            
            # تحديث الإعدادات المحلية في التكوين الكامل
            if hasattr(self, 'full_config'):
                if "user_preferences" not in self.full_config:
                    self.full_config["user_preferences"] = {}
                
                self.full_config["user_preferences"]["language"] = self.settings["language"]
                self.full_config["user_preferences"]["theme"] = self.settings["theme"]
                
                print(f"💾 [DEBUG] Full config theme before save: {self.full_config['user_preferences'].get('theme', 'NOT_FOUND')}")
                
                # حفظ التكوين الكامل
                with open(self.config_path, 'w', encoding='utf-8') as f:
                    json.dump(self.full_config, f, ensure_ascii=False, indent=2)
            else:
                # النظام القديم - حفظ الإعدادات المحلية فقط
                with open(self.config_path, 'w', encoding='utf-8') as f:
                    json.dump(self.settings, f, ensure_ascii=False, indent=2)
            
            logger.info("Settings saved successfully")
            print(f"✅ [DEBUG] Settings saved successfully")
        except Exception as e:
            logger.error(f"Failed to save settings: {e}")
            print(f"❌ [DEBUG] Failed to save settings: {e}")

    def _update_settings_from_config(self, config_data: dict):
        """تحديث الإعدادات من بيانات التكوين الجديدة مع الحفاظ على التوافق"""
        print(f"🔄 [DEBUG] Updating settings from config...")
        print(f"🔄 [DEBUG] Config data keys: {list(config_data.keys())}")
        
        # الحفاظ على التوافق مع النظام القديم
        if "user_preferences" in config_data:
            prefs = config_data["user_preferences"]
            old_lang = self.settings.get("language", "NOT_SET")
            old_theme = self.settings.get("theme", "NOT_SET")
            
            self.settings["language"] = prefs.get("language", self.LANG_DEFAULT)
            self.settings["theme"] = prefs.get("theme", self.THEME_DEFAULT)
            
            print(f"🔄 [DEBUG] From user_preferences - Language: {old_lang} -> {self.settings['language']}")
            print(f"🔄 [DEBUG] From user_preferences - Theme: {old_theme} -> {self.settings['theme']}")
        else:
            # النظام القديم - البحث عن القيم في المستوى الأعلى
            old_lang = self.settings.get("language", "NOT_SET")
            old_theme = self.settings.get("theme", "NOT_SET")
            
            self.settings["language"] = config_data.get("language", self.LANG_DEFAULT)
            self.settings["theme"] = config_data.get("theme", self.THEME_DEFAULT)
            
            print(f"🔄 [DEBUG] From root level - Language: {old_lang} -> {self.settings['language']}")
            print(f"🔄 [DEBUG] From root level - Theme: {old_theme} -> {self.settings['theme']}")
        
        # حفظ جميع بيانات التكوين للوصول المستقبلي
        self.full_config = config_data

    def _validate_settings(self):
        """التحقق من صحة الإعدادات"""
        print(f"🔍 [DEBUG] Validating settings...")
        print(f"🔍 [DEBUG] Current theme before validation: {self.settings.get('theme', 'NOT_FOUND')}")
        
        # التحقق من اللغة
        if self.settings["language"] not in self.SUPPORTED_LANGS:
            logger.warning(f"Invalid language: {self.settings['language']}, using default: {self.LANG_DEFAULT}")
            self.settings["language"] = self.LANG_DEFAULT
        
        # التحقق من الثيم
        valid_themes = ["dark", "light", "auto", "default"]
        print(f"🔍 [DEBUG] Valid themes: {valid_themes}")
        print(f"🔍 [DEBUG] Current theme: {self.settings['theme']}")
        print(f"🔍 [DEBUG] THEME_DEFAULT: {self.THEME_DEFAULT}")
        
        if self.settings["theme"] not in valid_themes:
            logger.warning(f"Invalid theme: {self.settings['theme']}, using default: {self.THEME_DEFAULT}")
            print(f"⚠️ [DEBUG] Invalid theme detected, changing from '{self.settings['theme']}' to '{self.THEME_DEFAULT}'")
            self.settings["theme"] = self.THEME_DEFAULT
        else:
            print(f"✅ [DEBUG] Theme '{self.settings['theme']}' is valid")
        
        print(f"🔍 [DEBUG] Final theme after validation: {self.settings.get('theme', 'NOT_FOUND')}")

    def get(self, key, default=None):
        """الحصول على قيمة إعداد مع قيمة افتراضية"""
        return self.settings.get(key, default)

    def set(self, key, value):
        """تعيين قيمة إعداد"""
        self.settings[key] = value

    @property
    def language(self):
        """اللغة الحالية"""
        return self.settings.get("language", self.LANG_DEFAULT)
    
    @language.setter
    def language(self, value):
        """تعيين اللغة"""
        self.settings["language"] = value
    
    @property
    def theme(self):
        """الثيم الحالي"""
        return self.settings.get("theme", self.THEME_DEFAULT)
    
    @theme.setter
    def theme(self, value):
        """تعيين الثيم"""
        self.settings["theme"] = value
    
    def get_config_section(self, section: str) -> dict:
        """الحصول على قسم من الإعدادات"""
        if hasattr(self, 'full_config') and section in self.full_config:
            return self.full_config[section]
        return {}
    
    def get_config_value(self, section: str, key: str, default=None):
        """الحصول على قيمة من قسم معين"""
        section_data = self.get_config_section(section)
        return section_data.get(key, default)
    
    def set_config_value(self, section: str, key: str, value):
        """تعيين قيمة في قسم معين"""
        if not hasattr(self, 'full_config'):
            self.full_config = {}
        
        if section not in self.full_config:
            self.full_config[section] = {}
        
        self.full_config[section][key] = value
        
        # تحديث الإعدادات المحلية إذا لزم الأمر
        if section == "user_preferences":
            if key == "language":
                self.settings["language"] = value
            elif key == "theme":
                self.settings["theme"] = value
    
    def validate_and_save(self):
        """التحقق من صحة الإعدادات وحفظها"""
        try:
            # التحقق من صحة الإعدادات
            validator = ConfigValidator()
            validation_result = validator.validate_config(self.full_config)
            
            if not validation_result.is_valid:
                logger.error("Configuration validation failed before saving")
                return False
            
            # حفظ الإعدادات
            self.save()
            return True
            
        except Exception as e:
            logger.error(f"Failed to validate and save settings: {e}")
            return False 