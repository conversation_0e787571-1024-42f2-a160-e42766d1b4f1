"""
نموذج عروض الأسعار - يحتوي على جميع المعلومات المتعلقة بطلبات عروض الأسعار
"""

from dataclasses import dataclass, field
from typing import Optional, List, Dict, Any
from datetime import datetime, date
from enum import Enum
from decimal import Decimal


class QuotationStatus(Enum):
    """حالة طلب عرض السعر"""
    DRAFT = "مسودة"
    SENT = "مرسل"
    RECEIVED = "مستلم"
    UNDER_REVIEW = "قيد المراجعة"
    ACCEPTED = "مقبول"
    REJECTED = "مرفوض"
    EXPIRED = "منتهي الصلاحية"


@dataclass
class QuotationItem:
    """عنصر في طلب عرض السعر"""
    id: Optional[int] = None
    quotation_id: Optional[int] = None
    product_id: Optional[int] = None
    product_code: str = ""
    product_name: str = ""
    product_description: str = ""
    
    # الكمية والمواصفات
    quantity_requested: Decimal = Decimal('0')
    unit_of_measure: str = "قطعة"
    specifications: str = ""
    quality_requirements: str = ""
    
    # عرض السعر من المورد
    quoted_price: Decimal = Decimal('0')
    delivery_time_days: int = 0
    minimum_order_quantity: Decimal = Decimal('0')
    
    # معلومات إضافية
    notes: str = ""


@dataclass
class Quotation:
    """نموذج طلب عرض السعر الرئيسي"""
    
    # المعلومات الأساسية
    id: Optional[int] = None
    quotation_number: str = ""
    supplier_id: Optional[int] = None
    supplier_name: str = ""
    
    # التواريخ
    request_date: date = field(default_factory=date.today)
    response_due_date: Optional[date] = None
    response_received_date: Optional[date] = None
    validity_date: Optional[date] = None
    
    # الحالة
    status: QuotationStatus = QuotationStatus.DRAFT
    
    # العناصر
    items: List[QuotationItem] = field(default_factory=list)
    
    # المعلومات المالية
    total_quoted_amount: Decimal = Decimal('0')
    currency: str = "SAR"
    
    # ملاحظات
    notes: str = ""
    terms_and_conditions: str = ""
    
    # معلومات النظام
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    created_by: str = ""
    updated_by: str = ""
    
    def __post_init__(self):
        """تهيئة إضافية"""
        if not self.created_at:
            self.created_at = datetime.now()
        self.updated_at = datetime.now()
        
        if not self.quotation_number:
            self.quotation_number = self.generate_quotation_number()
    
    def generate_quotation_number(self) -> str:
        """إنشاء رقم طلب عرض السعر"""
        today = date.today()
        year = today.year
        month = today.month
        import random
        sequence = random.randint(1000, 9999)
        return f"RFQ-{year}{month:02d}-{sequence}"
