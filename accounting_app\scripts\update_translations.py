#!/usr/bin/env python3
"""
سكريبت لتحديث ملفات الترجمة وتوليد ملفات .qm
يستخرج النصوص القابلة للترجمة من الكود ويحدث ملفات .ts
ثم يحولها إلى ملفات .qm للاستخدام في التطبيق
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# إعداد المسارات
PROJECT_ROOT = Path(__file__).parent.parent
TRANSLATIONS_DIR = PROJECT_ROOT / "translations"
UI_DIR = PROJECT_ROOT / "ui"
CORE_DIR = PROJECT_ROOT / "core"
CONTROLLERS_DIR = PROJECT_ROOT / "controllers"

def setup_logging():
    """إعداد نظام التسجيل"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('translation_update.log', encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )

def check_dependencies():
    """التحقق من وجود الأدوات المطلوبة"""
    try:
        # التحقق من pylupdate6
        result = subprocess.run(['pylupdate6', '--version'], 
                              capture_output=True, text=True)
        if result.returncode != 0:
            logging.error("pylupdate6 غير متوفر. يرجى تثبيته أولاً.")
            return False
        
        # التحقق من lrelease
        result = subprocess.run(['lrelease', '--version'], 
                              capture_output=True, text=True)
        if result.returncode != 0:
            logging.error("lrelease غير متوفر. يرجى تثبيته أولاً.")
            return False
        
        logging.info("جميع الأدوات المطلوبة متوفرة")
        return True
        
    except FileNotFoundError:
        logging.error("أحد الأدوات غير متوفر. يرجى تثبيت Qt tools.")
        return False

def update_ts_files():
    """تحديث ملفات .ts من الكود"""
    try:
        # إنشاء قائمة بجميع ملفات Python
        python_files = []
        
        # إضافة الملفات الرئيسية
        python_files.extend([
            str(PROJECT_ROOT / "main.py"),
            str(PROJECT_ROOT / "core" / "*.py"),
            str(PROJECT_ROOT / "controllers" / "*.py"),
            str(PROJECT_ROOT / "ui" / "*.py"),
            str(PROJECT_ROOT / "models" / "*.py")
        ])
        
        # تحويل القائمة إلى سلسلة نصية
        files_pattern = " ".join(python_files)
        
        # أمر تحديث ملفات .ts
        cmd = f'pylupdate6 -translate-function tr {files_pattern} -ts {TRANSLATIONS_DIR}/ar.ts {TRANSLATIONS_DIR}/en.ts {TRANSLATIONS_DIR}/fr.ts'
        
        logging.info(f"تنفيذ الأمر: {cmd}")
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            logging.info("تم تحديث ملفات .ts بنجاح")
            if result.stdout:
                logging.info(f"النتيجة: {result.stdout}")
        else:
            logging.error(f"فشل في تحديث ملفات .ts: {result.stderr}")
            return False
            
        return True
        
    except Exception as e:
        logging.error(f"خطأ في تحديث ملفات .ts: {e}")
        return False

def generate_qm_files():
    """توليد ملفات .qm من ملفات .ts"""
    try:
        ts_files = list(TRANSLATIONS_DIR.glob("*.ts"))
        
        for ts_file in ts_files:
            qm_file = ts_file.with_suffix('.qm')
            
            cmd = f'lrelease {ts_file} -qm {qm_file}'
            logging.info(f"تنفيذ الأمر: {cmd}")
            
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            
            if result.returncode == 0:
                logging.info(f"تم توليد {qm_file} بنجاح")
            else:
                logging.error(f"فشل في توليد {qm_file}: {result.stderr}")
                return False
        
        return True
        
    except Exception as e:
        logging.error(f"خطأ في توليد ملفات .qm: {e}")
        return False

def verify_files():
    """التحقق من وجود الملفات المطلوبة"""
    required_files = [
        TRANSLATIONS_DIR / "ar.ts",
        TRANSLATIONS_DIR / "en.ts", 
        TRANSLATIONS_DIR / "fr.ts",
        TRANSLATIONS_DIR / "ar.qm",
        TRANSLATIONS_DIR / "en.qm",
        TRANSLATIONS_DIR / "fr.qm"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not file_path.exists():
            missing_files.append(str(file_path))
    
    if missing_files:
        logging.warning(f"الملفات المفقودة: {missing_files}")
        return False
    
    logging.info("جميع الملفات المطلوبة موجودة")
    return True

def main():
    """الدالة الرئيسية"""
    setup_logging()
    logging.info("بدء عملية تحديث الترجمة")
    
    # التحقق من المسارات
    if not TRANSLATIONS_DIR.exists():
        logging.error(f"مجلد الترجمة غير موجود: {TRANSLATIONS_DIR}")
        return False
    
    # التحقق من الأدوات
    if not check_dependencies():
        return False
    
    # تحديث ملفات .ts
    if not update_ts_files():
        return False
    
    # توليد ملفات .qm
    if not generate_qm_files():
        return False
    
    # التحقق من الملفات
    if not verify_files():
        return False
    
    logging.info("تم إكمال عملية تحديث الترجمة بنجاح!")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 