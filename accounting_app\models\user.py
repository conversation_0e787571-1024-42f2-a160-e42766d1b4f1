from enum import Enum
from typing import List, Optional
import logging

class UserRole(Enum):
    """أدوار المستخدمين المدعومة"""
    ADMIN = "admin"
    ACCOUNTANT = "accountant"
    CASHIER = "cashier"
    GUEST = "guest"

class User:
    """نموذج المستخدم مع نظام صلاحيات"""
    
    def __init__(self, username: str, role: str, email: Optional[str] = None, permissions: Optional[List[str]] = None):
        self.username = username
        self.email = email
        self.role = self._validate_role(role)
        self.permissions = permissions or self._get_default_permissions()
        self.is_active = True
        self.last_login = None
        logging.info(f"User created: {username} with role: {role}")

    def _validate_role(self, role: str) -> UserRole:
        """التحقق من صحة دور المستخدم"""
        try:
            return UserRole(role.lower())
        except ValueError:
            logging.warning(f"Invalid role '{role}', defaulting to GUEST")
            return UserRole.GUEST

    def _get_default_permissions(self) -> List[str]:
        """الحصول على الصلاحيات الافتراضية حسب الدور"""
        permissions_map = {
            UserRole.ADMIN: [
                "read", "write", "delete", "admin", "user_management",
                "settings", "reports", "backup", "restore"
            ],
            UserRole.ACCOUNTANT: [
                "read", "write", "reports", "settings"
            ],
            UserRole.CASHIER: [
                "read", "write", "pos", "sales"
            ],
            UserRole.GUEST: [
                "read"
            ]
        }
        return permissions_map.get(self.role, ["read"])

    def has_permission(self, permission: str) -> bool:
        """التحقق من وجود صلاحية معينة"""
        return permission in self.permissions

    def can_access_module(self, module: str) -> bool:
        """التحقق من إمكانية الوصول لوحدة معينة"""
        module_permissions = {
            "settings": ["admin"],
            "user_management": ["admin"],
            "reports": ["admin", "accountant"],
            "pos": ["admin", "cashier"],
            "sales": ["admin", "cashier", "accountant"],
            "purchases": ["admin", "accountant"],
            "inventory": ["admin", "accountant"],
            "payroll": ["admin", "accountant"]
        }
        required_permissions = module_permissions.get(module, [])
        return any(self.has_permission(perm) for perm in required_permissions)

    def is_admin(self) -> bool:
        """التحقق من كون المستخدم مدير"""
        return self.role == UserRole.ADMIN

    def to_dict(self) -> dict:
        """تحويل بيانات المستخدم إلى قاموس"""
        return {
            "username": self.username,
            "email": self.email,
            "role": self.role.value,
            "permissions": self.permissions,
            "is_active": self.is_active,
            "last_login": self.last_login
        }

    @classmethod
    def from_dict(cls, data: dict) -> 'User':
        """إنشاء مستخدم من قاموس"""
        return cls(
            username=data.get("username"),
            role=data.get("role", "guest"),
            email=data.get("email"),
            permissions=data.get("permissions")
        )

    def __str__(self) -> str:
        return f"User({self.username}, {self.role.value})"

    def __repr__(self) -> str:
        return f"User(username='{self.username}', role='{self.role.value}', email='{self.email}')" 