import os
import urllib.request

# قائمة الموارد: الاسم والروابط
resources = {
    "lang.svg": "https://cdn.jsdelivr.net/gh/feathericons/feather/icons/globe.svg",
    "bell.svg": "https://cdn.jsdelivr.net/gh/feathericons/feather/icons/bell.svg",
    "exit.svg": "https://cdn.jsdelivr.net/gh/feathericons/feather/icons/log-out.svg",
    "ai.svg": "https://cdn.jsdelivr.net/gh/feathericons/feather/icons/cpu.svg",
    "user.svg": "https://cdn.jsdelivr.net/gh/feathericons/feather/icons/user.svg",
    "home.svg": "https://cdn.jsdelivr.net/gh/feathericons/feather/icons/home.svg",
    "settings.svg": "https://cdn.jsdelivr.net/gh/feathericons/feather/icons/settings.svg",
    "loading.svg": "https://cdn.jsdelivr.net/gh/feathericons/feather/icons/loader.svg",
    "mic.svg": "https://cdn.jsdelivr.net/gh/feathericons/feather/icons/mic.svg",
    "support.svg": "https://cdn.jsdelivr.net/gh/feathericons/feather/icons/help-circle.svg",
    "notification.wav": "https://assets.mixkit.co/sfx/preview/mixkit-bell-notification-933.wav"
}

# تأكد من وجود مجلد resources
os.makedirs(os.path.dirname(__file__), exist_ok=True)

for filename, url in resources.items():
    dest = os.path.join(os.path.dirname(__file__), filename)
    print(f"Downloading {filename} ...")
    try:
        urllib.request.urlretrieve(url, dest)
        print(f"Saved: {dest}")
    except Exception as e:
        print(f"Failed to download {filename}: {e}")

print("All resources downloaded.") 