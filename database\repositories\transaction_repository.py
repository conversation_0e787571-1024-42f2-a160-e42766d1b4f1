from sqlalchemy.orm import Session
from database.models import Transaction

class TransactionRepository:
    def __init__(self, db: Session):
        self.db = db

    def get_by_id(self, transaction_id):
        return self.db.query(Transaction).filter(Transaction.transaction_id == transaction_id).first()

    def get_by_company(self, company_id):
        return self.db.query(Transaction).filter(Transaction.company_id == company_id).all()

    def create(self, transaction: Transaction):
        self.db.add(transaction)
        self.db.commit()
        self.db.refresh(transaction)
        return transaction 