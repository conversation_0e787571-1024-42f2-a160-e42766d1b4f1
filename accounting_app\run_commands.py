#!/usr/bin/env python3
"""
سكريبت لتنفيذ الأوامر المطلوبة
Script to run required commands
"""

import sys
import os
from pathlib import Path

# إضافة مجلد المشروع إلى مسار Python
PROJECT_ROOT = Path(__file__).parent
sys.path.insert(0, str(PROJECT_ROOT))

def init_config():
    """تهيئة التكوين"""
    print("🔧 تهيئة التكوين...")
    try:
        from config.app_config import save_config
        save_config()
        print("✅ تم تهيئة التكوين بنجاح!")
        print("📁 تم إنشاء ملف: config/app_config.json")
    except Exception as e:
        print(f"❌ خطأ في تهيئة التكوين: {e}")

def validate_config():
    """التحقق من صحة التكوين"""
    print("🔍 التحقق من صحة التكوين...")
    try:
        from config.app_config import get_config
        config = get_config()
        is_valid = config.validate()
        print(f"✅ صحة التكوين: {is_valid}")
        
        if is_valid:
            print("📋 تفاصيل التكوين:")
            print(f"  - اسم التطبيق: {config.app_name}")
            print(f"  - الإصدار: {config.app_version}")
            print(f"  - البيئة: {config.environment.value}")
            print(f"  - اللغة: {config.ui.language}")
            print(f"  - الثيم: {config.ui.theme}")
            print(f"  - الذكاء الاصطناعي: {'مفعل' if config.ai.enabled else 'معطل'}")
        else:
            print("⚠️  التكوين يحتوي على أخطاء")
            
    except Exception as e:
        print(f"❌ خطأ في التحقق من التكوين: {e}")

def error_stats():
    """مراجعة إحصائيات الأخطاء"""
    print("📊 مراجعة إحصائيات الأخطاء...")
    try:
        from accounting_app.core.error_handler import error_handler
        stats = error_handler.get_error_statistics()
        
        print("📈 إحصائيات الأخطاء:")
        print(f"  - إجمالي الأخطاء: {stats['total_errors']}")
        print(f"  - الأخطاء الحديثة (آخر ساعة): {stats['recent_errors']}")
        
        print("\n📋 الأخطاء حسب النوع:")
        for error_type, count in stats['by_type'].items():
            print(f"  - {error_type}: {count}")
        
        print("\n📋 الأخطاء حسب الخطورة:")
        for severity, count in stats['by_severity'].items():
            print(f"  - {severity}: {count}")
            
    except Exception as e:
        print(f"❌ خطأ في مراجعة إحصائيات الأخطاء: {e}")

def clear_errors():
    """مسح تاريخ الأخطاء"""
    print("🧹 مسح تاريخ الأخطاء...")
    try:
        from accounting_app.core.error_handler import error_handler
        error_handler.clear_error_history()
        print("✅ تم مسح تاريخ الأخطاء بنجاح!")
    except Exception as e:
        print(f"❌ خطأ في مسح تاريخ الأخطاء: {e}")

def test_core():
    """تشغيل اختبارات النواة"""
    print("🧪 تشغيل اختبارات النواة...")
    try:
        import pytest
        import subprocess
        
        result = subprocess.run([
            sys.executable, "-m", "pytest", 
            "tests/test_core.py", "-v"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ نجحت اختبارات النواة!")
        else:
            print("❌ فشلت بعض اختبارات النواة")
            print(result.stdout)
            print(result.stderr)
            
    except Exception as e:
        print(f"❌ خطأ في تشغيل اختبارات النواة: {e}")

def test_controllers():
    """تشغيل اختبارات المتحكمات"""
    print("🧪 تشغيل اختبارات المتحكمات...")
    try:
        import pytest
        import subprocess
        
        result = subprocess.run([
            sys.executable, "-m", "pytest", 
            "tests/test_controllers.py", "-v"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ نجحت اختبارات المتحكمات!")
        else:
            print("❌ فشلت بعض اختبارات المتحكمات")
            print(result.stdout)
            print(result.stderr)
            
    except Exception as e:
        print(f"❌ خطأ في تشغيل اختبارات المتحكمات: {e}")

def show_help():
    """عرض المساعدة"""
    print("🚀 أوامر Smart Accounting App:")
    print()
    print("التكوين:")
    print("  init-config     - تهيئة التكوين")
    print("  validate-config - التحقق من صحة التكوين")
    print()
    print("الأخطاء:")
    print("  error-stats     - مراجعة إحصائيات الأخطاء")
    print("  clear-errors    - مسح تاريخ الأخطاء")
    print()
    print("الاختبارات:")
    print("  test-core       - تشغيل اختبارات النواة")
    print("  test-controllers- تشغيل اختبارات المتحكمات")
    print()
    print("المساعدة:")
    print("  help            - عرض هذه المساعدة")

def main():
    """الدالة الرئيسية"""
    if len(sys.argv) < 2:
        show_help()
        return
    
    command = sys.argv[1].lower()
    
    commands = {
        'init-config': init_config,
        'validate-config': validate_config,
        'error-stats': error_stats,
        'clear-errors': clear_errors,
        'test-core': test_core,
        'test-controllers': test_controllers,
        'help': show_help
    }
    
    if command in commands:
        commands[command]()
    else:
        print(f"❌ أمر غير معروف: {command}")
        show_help()

if __name__ == "__main__":
    main() 