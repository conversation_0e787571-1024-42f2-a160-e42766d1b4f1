#!/usr/bin/env python3
"""
ملف تشغيل سريع للتطبيق
يساعد في حل مشاكل المسار والبيئة الافتراضية
"""

import os
import sys
import subprocess

def main():
    """تشغيل التطبيق مع التأكد من المسار الصحيح"""
    
    # التأكد من أننا في المجلد الصحيح
    current_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(current_dir)
    
    print(f"📁 المجلد الحالي: {current_dir}")
    print(f"🐍 Python: {sys.executable}")
    print(f"📦 PySide6 متوفر: {'PySide6' in sys.modules or 'PySide6' in [pkg.key for pkg in __import__('pkg_resources').working_set]}")
    
    # تشغيل التطبيق
    try:
        print("🚀 بدء تشغيل التطبيق...")
        # استيراد من الحزمة بدلاً من الملف المباشر
        from accounting_app.main import main as app_main
        app_main()
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        print("💡 تأكد من تثبيت المكتبات المطلوبة:")
        print("   pip install -r requirements.txt")
    except Exception as e:
        print(f"❌ خطأ في التشغيل: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 