#!/usr/bin/env python3
"""
سكريبت اختبار فاحص الموارد
Test Resource Checker Script
"""

import sys
import os
import tempfile
import shutil
from pathlib import Path

# إضافة مجلد المشروع إلى مسار Python
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from accounting_app.utils import ResourceChecker, check_resources_on_startup

def test_resource_checker():
    """اختبار فاحص الموارد"""
    print("🧪 بدء اختبار فاحص الموارد...")
    
    # اختبار 1: فحص الموارد الحالية
    print("\n📋 اختبار 1: فحص الموارد الحالية")
    success = check_resources_on_startup()
    print(f"النتيجة: {'✅ نجح' if success else '❌ فشل'}")
    
    # اختبار 2: إنشاء فاحص مخصص
    print("\n📋 اختبار 2: إنشاء فاحص مخصص")
    checker = ResourceChecker()
    results = checker.check_all_resources()
    
    total_resources = sum(len(resources) for resources in results.values())
    print(f"إجمالي الموارد المفحوصة: {total_resources}")
    
    # اختبار 3: محاكاة موارد مفقودة
    print("\n📋 اختبار 3: محاكاة موارد مفقودة")
    test_missing_resources(checker)
    
    # اختبار 4: اختبار الإصلاح التلقائي
    print("\n📋 اختبار 4: اختبار الإصلاح التلقائي")
    test_auto_fix(checker)
    
    print("\n✅ انتهى اختبار فاحص الموارد")

def test_missing_resources(checker):
    """اختبار التعامل مع الموارد المفقودة"""
    # إنشاء مجلد مؤقت للاختبار
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_checker = ResourceChecker(temp_dir)
        
        # فحص الموارد في المجلد الفارغ
        results = temp_checker.check_all_resources()
        
        missing_count = 0
        for category, resources in results.items():
            for resource in resources:
                if not resource.exists:
                    missing_count += 1
        
        print(f"الموارد المفقودة في المجلد الفارغ: {missing_count}")
        
        # اختبار الحصول على الموارد المفقودة
        missing_resources = temp_checker.get_missing_resources()
        print(f"قائمة الموارد المفقودة: {len(missing_resources)} مورد")

def test_auto_fix(checker):
    """اختبار الإصلاح التلقائي"""
    # إنشاء مجلد مؤقت للاختبار
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_checker = ResourceChecker(temp_dir)
        
        # إنشاء مجلد resources
        resources_dir = Path(temp_dir) / "resources"
        resources_dir.mkdir(exist_ok=True)
        
        # فحص الموارد قبل الإصلاح
        results_before = temp_checker.check_all_resources()
        missing_before = sum(1 for category in results_before.values() 
                           for resource in category if not resource.exists)
        
        # محاولة الإصلاح
        fixed_count = temp_checker.fix_missing_resources()
        
        # فحص الموارد بعد الإصلاح
        results_after = temp_checker.check_all_resources()
        missing_after = sum(1 for category in results_after.values() 
                          for resource in category if not resource.exists)
        
        print(f"الموارد المفقودة قبل الإصلاح: {missing_before}")
        print(f"الموارد المفقودة بعد الإصلاح: {missing_after}")
        print(f"عدد الموارد التي تم إصلاحها: {fixed_count}")
        
        # التحقق من إنشاء البدائل
        if resources_dir.exists():
            svg_files = list(resources_dir.glob("*.svg"))
            print(f"عدد ملفات SVG المنشأة: {len(svg_files)}")

def test_fallback_resources():
    """اختبار بدائل الموارد"""
    print("\n📋 اختبار 5: اختبار بدائل الموارد")
    
    checker = ResourceChecker()
    
    # اختبار إنشاء بديل لأيقونة
    test_icon = "test_icon.svg"
    success = checker.create_fallback_resource(test_icon)
    print(f"إنشاء بديل لـ {test_icon}: {'✅ نجح' if success else '❌ فشل'}")
    
    # اختبار إنشاء بديل لملف غير مدعوم
    test_file = "test.txt"
    success = checker.create_fallback_resource(test_file)
    print(f"إنشاء بديل لـ {test_file}: {'✅ نجح' if success else '❌ فشل'} (متوقع: فشل)")

def test_error_handling():
    """اختبار معالجة الأخطاء"""
    print("\n📋 اختبار 6: اختبار معالجة الأخطاء")
    
    # اختبار مع مجلد غير موجود
    try:
        checker = ResourceChecker("/path/that/does/not/exist")
        results = checker.check_all_resources()
        print("✅ التعامل مع المجلد غير الموجود: نجح")
    except Exception as e:
        print(f"❌ التعامل مع المجلد غير الموجود: فشل - {e}")
    
    # اختبار مع صلاحيات محدودة
    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            # إنشاء ملف بدون صلاحيات كتابة
            test_file = Path(temp_dir) / "test.svg"
            test_file.touch()
            test_file.chmod(0o444)  # قراءة فقط
            
            checker = ResourceChecker(temp_dir)
            results = checker.check_all_resources()
            print("✅ التعامل مع صلاحيات محدودة: نجح")
    except Exception as e:
        print(f"❌ التعامل مع صلاحيات محدودة: فشل - {e}")

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار فاحص الموارد")
    print("=" * 50)
    
    try:
        test_resource_checker()
        test_fallback_resources()
        test_error_handling()
        
        print("\n" + "=" * 50)
        print("✅ جميع الاختبارات اكتملت بنجاح!")
        
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main()) 