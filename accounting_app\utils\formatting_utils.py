"""
Formatting Utilities for Smart Accounting App
أدوات تنسيق البيانات لبرنامج المحاسبة الذكي
"""

import locale
import logging
from datetime import datetime, date
from typing import Union, Optional
from decimal import Decimal, ROUND_HALF_UP

logger = logging.getLogger(__name__)

def format_currency(amount: Union[float, int, str, Decimal], 
                   currency_code: str = "SAR", 
                   locale_code: str = "ar_SA") -> str:
    """تنسيق المبلغ كعملة"""
    try:
        # تحويل إلى Decimal للحصول على دقة أفضل
        if isinstance(amount, str):
            amount = Decimal(amount.replace(',', ''))
        elif isinstance(amount, (int, float)):
            amount = Decimal(str(amount))
        elif isinstance(amount, Decimal):
            pass
        else:
            return "0.00"
        
        # تقريب إلى رقمين عشريين
        amount = amount.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
        
        # تنسيق العملة حسب اللغة
        if locale_code == "ar_SA":
            # تنسيق عربي
            formatted = f"{amount:,.2f} {currency_code}"
        else:
            # تنسيق إنجليزي
            formatted = f"{currency_code} {amount:,.2f}"
        
        return formatted
    except Exception as e:
        logger.error(f"Currency formatting error: {e}")
        return "0.00"

def format_date(date_obj: Union[date, datetime, str], 
                format_str: str = "%Y-%m-%d",
                locale_code: str = "ar_SA") -> str:
    """تنسيق التاريخ"""
    try:
        # تحويل النص إلى كائن تاريخ
        if isinstance(date_obj, str):
            date_obj = datetime.strptime(date_obj, "%Y-%m-%d")
        
        # تنسيق التاريخ
        if locale_code == "ar_SA":
            # تنسيق عربي
            arabic_months = {
                1: "يناير", 2: "فبراير", 3: "مارس", 4: "أبريل",
                5: "مايو", 6: "يونيو", 7: "يوليو", 8: "أغسطس",
                9: "سبتمبر", 10: "أكتوبر", 11: "نوفمبر", 12: "ديسمبر"
            }
            return f"{date_obj.day} {arabic_months[date_obj.month]} {date_obj.year}"
        else:
            # تنسيق إنجليزي
            return date_obj.strftime(format_str)
    except Exception as e:
        logger.error(f"Date formatting error: {e}")
        return ""

def format_number(number: Union[int, float, str], 
                 decimal_places: int = 2,
                 locale_code: str = "ar_SA") -> str:
    """تنسيق الأرقام"""
    try:
        # تحويل إلى رقم
        if isinstance(number, str):
            number = float(number.replace(',', ''))
        
        # تنسيق الرقم
        if locale_code == "ar_SA":
            # تنسيق عربي مع فواصل للآلاف
            formatted = f"{number:,.{decimal_places}f}"
        else:
            # تنسيق إنجليزي
            formatted = f"{number:,.{decimal_places}f}"
        
        return formatted
    except Exception as e:
        logger.error(f"Number formatting error: {e}")
        return "0.00"

def format_percentage(value: Union[float, int, str], 
                     decimal_places: int = 2) -> str:
    """تنسيق النسبة المئوية"""
    try:
        # تحويل إلى رقم
        if isinstance(value, str):
            value = float(value.replace('%', '').replace(',', ''))
        
        # تنسيق النسبة المئوية
        formatted = f"{value:.{decimal_places}f}%"
        return formatted
    except Exception as e:
        logger.error(f"Percentage formatting error: {e}")
        return "0.00%"

def format_file_size(size_bytes: int) -> str:
    """تنسيق حجم الملف"""
    try:
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"
    except Exception as e:
        logger.error(f"File size formatting error: {e}")
        return "0 B"

def format_duration(seconds: Union[int, float]) -> str:
    """تنسيق المدة الزمنية"""
    try:
        if seconds < 60:
            return f"{int(seconds)} ثانية"
        elif seconds < 3600:
            minutes = int(seconds // 60)
            remaining_seconds = int(seconds % 60)
            return f"{minutes} دقيقة {remaining_seconds} ثانية"
        else:
            hours = int(seconds // 3600)
            remaining_minutes = int((seconds % 3600) // 60)
            return f"{hours} ساعة {remaining_minutes} دقيقة"
    except Exception as e:
        logger.error(f"Duration formatting error: {e}")
        return "0 ثانية"

def format_phone_number(phone: str, country_code: str = "+966") -> str:
    """تنسيق رقم الهاتف"""
    try:
        # إزالة جميع الأحرف غير الرقمية
        import re
        digits_only = re.sub(r'[^\d]', '', phone)
        
        # إزالة رمز البلد إذا كان موجوداً
        if digits_only.startswith('966'):
            digits_only = digits_only[3:]
        
        # تنسيق الرقم
        if len(digits_only) == 9:
            return f"{country_code} {digits_only[:3]} {digits_only[3:6]} {digits_only[6:]}"
        elif len(digits_only) == 10:
            return f"{country_code} {digits_only[:3]} {digits_only[3:6]} {digits_only[6:]}"
        else:
            return phone  # إرجاع الرقم كما هو إذا لم يتطابق مع التنسيق
    except Exception as e:
        logger.error(f"Phone number formatting error: {e}")
        return phone

def format_account_number(account_number: str, 
                         separator: str = "-",
                         group_size: int = 4) -> str:
    """تنسيق رقم الحساب"""
    try:
        # إزالة جميع الأحرف غير الرقمية
        import re
        digits_only = re.sub(r'[^\d]', '', account_number)
        
        # تقسيم الرقم إلى مجموعات
        formatted = ""
        for i in range(0, len(digits_only), group_size):
            if formatted:
                formatted += separator
            formatted += digits_only[i:i + group_size]
        
        return formatted
    except Exception as e:
        logger.error(f"Account number formatting error: {e}")
        return account_number

def format_text(text: str, max_length: int = 100, suffix: str = "...") -> str:
    """تنسيق النص مع تحديد الطول الأقصى"""
    try:
        if len(text) <= max_length:
            return text
        
        return text[:max_length - len(suffix)] + suffix
    except Exception as e:
        logger.error(f"Text formatting error: {e}")
        return text 