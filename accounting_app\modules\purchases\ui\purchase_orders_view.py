from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLineEdit, QTableWidget, QTableWidgetItem,
    QHeaderView, QPushButton, QDialog, QFormLayout, QDialogButtonBox, QComboBox,
    QMessageBox, QLabel, QCheckBox, QTextEdit, QGroupBox, QFrame, QScrollArea,
    QToolButton, QSizePolicy, QDateEdit, QTabWidget, QToolTip, QGridLayout,
    QSpacerItem, QSpinBox, QDoubleSpinBox
)
from PySide6.QtCore import Qt, QDate, QParallelAnimationGroup, QAbstractAnimation, QEasingCurve, QPropertyAnimation, QPoint, QSize
from PySide6.QtGui import QPixmap, QIntValidator, QDoubleValidator, QIcon, QPainter, QColor, QPolygon
from datetime import datetime, timedelta
import logging

class CollapsibleGroupBox(QWidget):
    """مجموعة قابلة للطي مع عنوان وزر توسيع/طي."""
    def __init__(self, title, parent=None, expanded=False):
        super().__init__(parent)
        self.toggle_button = QToolButton(text=title, checkable=True, checked=expanded)
        self.toggle_button.setStyleSheet("QToolButton { border: none; font-weight: bold; font-size: 15px; text-align: right; }")
        self.toggle_button.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)
        self.toggle_button.setArrowType(Qt.DownArrow if expanded else Qt.RightArrow)
        self.toggle_button.clicked.connect(self.on_toggle)

        self.content_area = QWidget()
        self.content_area.setMaximumHeight(0 if not expanded else 16777215)
        self.content_area.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)

        self.anim = QPropertyAnimation(self.content_area, b"maximumHeight")
        self.anim.setDuration(200)
        self.anim.setEasingCurve(QEasingCurve.InOutCubic)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.addWidget(self.toggle_button)
        layout.addWidget(self.content_area)
        self.setLayout(layout)

    def setContentLayout(self, content_layout):
        self.content_area.setLayout(content_layout)
        self.content_area.setMaximumHeight(content_layout.sizeHint().height() if self.toggle_button.isChecked() else 0)

    def on_toggle(self):
        checked = self.toggle_button.isChecked()
        self.toggle_button.setArrowType(Qt.DownArrow if checked else Qt.RightArrow)
        content_height = self.content_area.layout().sizeHint().height()
        start_value = self.content_area.maximumHeight()
        end_value = content_height if checked else 0
        self.anim.stop()
        self.anim.setStartValue(start_value)
        self.anim.setEndValue(end_value)
        self.anim.start()

class PurchaseOrderDialog(QDialog):
    """نافذة إنشاء/تعديل أمر شراء"""
    
    def __init__(self, parent=None, order=None, suppliers=None, products=None):
        super().__init__(parent)
        self.setWindowTitle(self.tr("إنشاء أمر شراء جديد"))
        self.setMinimumWidth(900)
        self.setMinimumHeight(700)
        self.order = order
        self.suppliers = suppliers or [
            {"id": 1, "name": "شركة المورد الأولى", "phone": "0501234567", "email": "<EMAIL>"},
            {"id": 2, "name": "شركة المورد الثانية", "phone": "0507654321", "email": "<EMAIL>"},
            {"id": 3, "name": "شركة المورد الثالثة", "phone": "0509876543", "email": "<EMAIL>"}
        ]
        self.products = products or [
            {"id": 1, "name": "منتج 1", "code": "P001", "price": 100.0, "stock": 50},
            {"id": 2, "name": "منتج 2", "code": "P002", "price": 150.0, "stock": 30},
            {"id": 3, "name": "منتج 3", "code": "P003", "price": 200.0, "stock": 25}
        ]
        self.order_items = []
        self.init_ui()
        
        if self.order:
            self.set_order_data(self.order)

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setLayoutDirection(Qt.RightToLeft)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        
        # استخدام QTabWidget لتنظيم المحتوى
        self.tab_widget = QTabWidget(self)
        
        # تبويب المعلومات الأساسية
        basic_tab = self.create_basic_info_tab()
        self.tab_widget.addTab(basic_tab, self.tr("المعلومات الأساسية"))
        
        # تبويب المنتجات
        products_tab = self.create_products_tab()
        self.tab_widget.addTab(products_tab, self.tr("المنتجات"))
        
        # تبويب الشحن والتوصيل
        shipping_tab = self.create_shipping_tab()
        self.tab_widget.addTab(shipping_tab, self.tr("الشحن والتوصيل"))
        
        # تبويب الدفع
        payment_tab = self.create_payment_tab()
        self.tab_widget.addTab(payment_tab, self.tr("الدفع"))
        
        # تبويب الموافقات
        approval_tab = self.create_approval_tab()
        self.tab_widget.addTab(approval_tab, self.tr("الموافقات"))
        
        main_layout.addWidget(self.tab_widget)
        
        # ملخص الطلب
        summary_group = QGroupBox(self.tr("ملخص الطلب"))
        summary_layout = QHBoxLayout(summary_group)
        
        self.subtotal_label = QLabel(self.tr("المجموع الفرعي: 0.00"))
        self.discount_label = QLabel(self.tr("الخصم: 0.00"))
        self.tax_label = QLabel(self.tr("الضريبة: 0.00"))
        self.shipping_label = QLabel(self.tr("الشحن: 0.00"))
        self.handling_label = QLabel(self.tr("المعالجة: 0.00"))
        self.total_label = QLabel(self.tr("المجموع الكلي: 0.00"))
        self.total_label.setStyleSheet("font-weight: bold; font-size: 14px; color: #dc2626;")
        
        summary_layout.addWidget(self.subtotal_label)
        summary_layout.addWidget(self.discount_label)
        summary_layout.addWidget(self.tax_label)
        summary_layout.addWidget(self.shipping_label)
        summary_layout.addWidget(self.handling_label)
        summary_layout.addWidget(self.total_label)
        
        main_layout.addWidget(summary_group)
        
        # أزرار الحفظ والإلغاء
        button_box = QDialogButtonBox(
            QDialogButtonBox.Save | QDialogButtonBox.Cancel
        )
        button_box.accepted.connect(self.save_order)
        button_box.rejected.connect(self.reject)
        main_layout.addWidget(button_box)

    def create_basic_info_tab(self):
        """إنشاء تبويب المعلومات الأساسية"""
        tab = QWidget()
        scroll = QScrollArea(tab)
        scroll.setWidgetResizable(True)
        container = QWidget()
        layout = QVBoxLayout(container)
        
        # معلومات المورد
        supplier_group = CollapsibleGroupBox(self.tr("معلومات المورد"), container, expanded=True)
        supplier_layout = QFormLayout()
        
        self.supplier_combo = QComboBox(self)
        for supplier in self.suppliers:
            self.supplier_combo.addItem(f"{supplier['name']} - {supplier['phone']}", supplier['id'])
        self.supplier_combo.currentIndexChanged.connect(self.on_supplier_changed)
        supplier_layout.addRow(self.tr("المورد:"), self.supplier_combo)
        
        self.supplier_phone_edit = QLineEdit(self)
        self.supplier_phone_edit.setReadOnly(True)
        supplier_layout.addRow(self.tr("الهاتف:"), self.supplier_phone_edit)
        
        self.supplier_email_edit = QLineEdit(self)
        self.supplier_email_edit.setReadOnly(True)
        supplier_layout.addRow(self.tr("البريد الإلكتروني:"), self.supplier_email_edit)
        
        supplier_group.setContentLayout(supplier_layout)
        layout.addWidget(supplier_group)
        
        # معلومات الطلب
        order_group = CollapsibleGroupBox(self.tr("معلومات الطلب"), container, expanded=True)
        order_layout = QFormLayout()
        
        self.order_date_edit = QDateEdit(self)
        self.order_date_edit.setDate(QDate.currentDate())
        order_layout.addRow(self.tr("تاريخ الطلب:"), self.order_date_edit)
        
        self.order_status_combo = QComboBox(self)
        self.order_status_combo.addItems([
            self.tr("مسودة"),
            self.tr("مرسل"),
            self.tr("مؤكد"),
            self.tr("قيد التحضير"),
            self.tr("جاهز للشحن"),
            self.tr("مشحون"),
            self.tr("مستلم"),
            self.tr("ملغي")
        ])
        order_layout.addRow(self.tr("حالة الطلب:"), self.order_status_combo)
        
        self.expected_delivery_edit = QDateEdit(self)
        self.expected_delivery_edit.setDate(QDate.currentDate().addDays(7))
        order_layout.addRow(self.tr("تاريخ التسليم المتوقع:"), self.expected_delivery_edit)
        
        self.notes_edit = QTextEdit(self)
        self.notes_edit.setMaximumHeight(100)
        order_layout.addRow(self.tr("ملاحظات:"), self.notes_edit)
        
        order_group.setContentLayout(order_layout)
        layout.addWidget(order_group)
        
        layout.addStretch()
        scroll.setWidget(container)
        
        tab_layout = QVBoxLayout(tab)
        tab_layout.addWidget(scroll)
        return tab

    def create_products_tab(self):
        """إنشاء تبويب المنتجات"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # أزرار إضافة المنتجات
        btn_layout = QHBoxLayout()
        self.add_product_btn = QPushButton(self.tr("إضافة منتج"), self)
        self.add_product_btn.clicked.connect(self.add_product_item)
        btn_layout.addWidget(self.add_product_btn)
        btn_layout.addStretch()
        layout.addLayout(btn_layout)
        
        # جدول المنتجات
        self.products_table = QTableWidget(self)
        self.products_table.setColumnCount(9)
        self.products_table.setHorizontalHeaderLabels([
            self.tr("المنتج"),
            self.tr("الكمية المطلوبة"),
            self.tr("سعر الوحدة"),
            self.tr("الخصم %"),
            self.tr("الضريبة %"),
            self.tr("المجموع"),
            self.tr("المواصفات"),
            self.tr("متطلبات الجودة"),
            self.tr("إجراءات")
        ])
        
        # تخصيص عرض الأعمدة
        self.products_table.setColumnWidth(0, 200)  # المنتج
        self.products_table.setColumnWidth(1, 100)  # الكمية المطلوبة
        self.products_table.setColumnWidth(2, 100)  # سعر الوحدة
        self.products_table.setColumnWidth(3, 80)   # الخصم %
        self.products_table.setColumnWidth(4, 80)   # الضريبة %
        self.products_table.setColumnWidth(5, 100)  # المجموع
        self.products_table.setColumnWidth(6, 150)  # المواصفات
        self.products_table.setColumnWidth(7, 150)  # متطلبات الجودة
        self.products_table.setColumnWidth(8, 100)  # إجراءات
        
        layout.addWidget(self.products_table)
        return tab

    def create_shipping_tab(self):
        """إنشاء تبويب الشحن والتوصيل"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # معلومات الشحن
        shipping_group = QGroupBox(self.tr("معلومات الشحن والتوصيل"))
        shipping_layout = QFormLayout(shipping_group)
        
        self.delivery_method_combo = QComboBox(self)
        self.delivery_method_combo.addItems([
            self.tr("شحن بحري"),
            self.tr("شحن جوي"),
            self.tr("شحن بري"),
            self.tr("استلام من المورد")
        ])
        shipping_layout.addRow(self.tr("طريقة التوصيل:"), self.delivery_method_combo)
        
        self.shipping_cost_spin = QDoubleSpinBox(self)
        self.shipping_cost_spin.setMaximum(10000.0)
        self.shipping_cost_spin.setValue(0.0)
        self.shipping_cost_spin.valueChanged.connect(self.calculate_totals)
        shipping_layout.addRow(self.tr("تكلفة الشحن:"), self.shipping_cost_spin)
        
        self.handling_cost_spin = QDoubleSpinBox(self)
        self.handling_cost_spin.setMaximum(5000.0)
        self.handling_cost_spin.setValue(0.0)
        self.handling_cost_spin.valueChanged.connect(self.calculate_totals)
        shipping_layout.addRow(self.tr("تكلفة المعالجة:"), self.handling_cost_spin)
        
        self.shipping_notes_edit = QTextEdit(self)
        self.shipping_notes_edit.setMaximumHeight(80)
        shipping_layout.addRow(self.tr("ملاحظات الشحن:"), self.shipping_notes_edit)
        
        layout.addWidget(shipping_group)
        layout.addStretch()
        return tab

    def create_payment_tab(self):
        """إنشاء تبويب الدفع"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # معلومات الدفع
        payment_group = QGroupBox(self.tr("معلومات الدفع"))
        payment_layout = QFormLayout(payment_group)
        
        self.payment_terms_combo = QComboBox(self)
        self.payment_terms_combo.addItems([
            self.tr("فوري"),
            self.tr("30 يوم"),
            self.tr("60 يوم"),
            self.tr("90 يوم")
        ])
        payment_layout.addRow(self.tr("شروط الدفع:"), self.payment_terms_combo)
        
        self.payment_status_combo = QComboBox(self)
        self.payment_status_combo.addItems([
            self.tr("غير مدفوع"),
            self.tr("مدفوع جزئياً"),
            self.tr("مدفوع بالكامل")
        ])
        payment_layout.addRow(self.tr("حالة الدفع:"), self.payment_status_combo)
        
        self.paid_amount_spin = QDoubleSpinBox(self)
        self.paid_amount_spin.setMaximum(100000.0)
        self.paid_amount_spin.setValue(0.0)
        self.paid_amount_spin.valueChanged.connect(self.calculate_remaining)
        payment_layout.addRow(self.tr("المبلغ المدفوع:"), self.paid_amount_spin)
        
        self.remaining_amount_label = QLabel("0.00")
        payment_layout.addRow(self.tr("المبلغ المتبقي:"), self.remaining_amount_label)
        
        layout.addWidget(payment_group)
        layout.addStretch()
        return tab

    def create_approval_tab(self):
        """إنشاء تبويب الموافقات"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # معلومات الموافقة
        approval_group = QGroupBox(self.tr("معلومات الموافقة"))
        approval_layout = QFormLayout(approval_group)
        
        self.approval_status_combo = QComboBox(self)
        self.approval_status_combo.addItems([
            self.tr("في انتظار الموافقة"),
            self.tr("موافق عليه"),
            self.tr("مرفوض"),
            self.tr("معلق")
        ])
        approval_layout.addRow(self.tr("حالة الموافقة:"), self.approval_status_combo)
        
        self.approver_combo = QComboBox(self)
        self.approver_combo.addItems([
            self.tr("مدير المشتريات"),
            self.tr("المدير المالي"),
            self.tr("المدير العام")
        ])
        approval_layout.addRow(self.tr("الموافق:"), self.approver_combo)
        
        self.approval_date_edit = QDateEdit(self)
        self.approval_date_edit.setDate(QDate.currentDate())
        approval_layout.addRow(self.tr("تاريخ الموافقة:"), self.approval_date_edit)
        
        self.approval_notes_edit = QTextEdit(self)
        self.approval_notes_edit.setMaximumHeight(80)
        approval_layout.addRow(self.tr("ملاحظات الموافقة:"), self.approval_notes_edit)
        
        layout.addWidget(approval_group)
        layout.addStretch()
        return tab

    def on_supplier_changed(self):
        """عند تغيير المورد"""
        supplier_id = self.supplier_combo.currentData()
        if supplier_id:
            supplier = next((s for s in self.suppliers if s['id'] == supplier_id), None)
            if supplier:
                self.supplier_phone_edit.setText(supplier['phone'])
                self.supplier_email_edit.setText(supplier['email'])

    def add_product_item(self):
        """إضافة منتج إلى الجدول"""
        # إنشاء نافذة حوار لاختيار المنتج
        dialog = ProductSelectionDialog(self.products, self)
        if dialog.exec() == QDialog.Accepted:
            product_data = dialog.get_selected_product()
            if product_data:
                self.add_product_to_table(product_data)

    def add_product_to_table(self, product_data):
        """إضافة منتج إلى الجدول"""
        row = self.products_table.rowCount()
        self.products_table.insertRow(row)
        
        # اختيار المنتج
        product_combo = QComboBox()
        for product in self.products:
            product_combo.addItem(f"{product['name']} ({product['code']})", product['id'])
        product_combo.setCurrentText(f"{product_data['name']} ({product_data['code']})")
        product_combo.currentIndexChanged.connect(lambda: self.calculate_row_total(row))
        self.products_table.setCellWidget(row, 0, product_combo)
        
        # الكمية المطلوبة
        qty_spin = QSpinBox()
        qty_spin.setMaximum(10000)
        qty_spin.setValue(1)
        qty_spin.valueChanged.connect(lambda: self.calculate_row_total(row))
        self.products_table.setCellWidget(row, 1, qty_spin)
        
        # سعر الوحدة
        price_spin = QDoubleSpinBox()
        price_spin.setMaximum(100000.0)
        price_spin.setValue(product_data['price'])
        price_spin.valueChanged.connect(lambda: self.calculate_row_total(row))
        self.products_table.setCellWidget(row, 2, price_spin)
        
        # الخصم %
        discount_spin = QDoubleSpinBox()
        discount_spin.setMaximum(100.0)
        discount_spin.setValue(0.0)
        discount_spin.valueChanged.connect(lambda: self.calculate_row_total(row))
        self.products_table.setCellWidget(row, 3, discount_spin)
        
        # الضريبة %
        tax_spin = QDoubleSpinBox()
        tax_spin.setMaximum(100.0)
        tax_spin.setValue(15.0)  # ضريبة القيمة المضافة
        tax_spin.valueChanged.connect(lambda: self.calculate_row_total(row))
        self.products_table.setCellWidget(row, 4, tax_spin)
        
        # المجموع
        total_item = QTableWidgetItem("0.00")
        self.products_table.setItem(row, 5, total_item)
        
        # المواصفات
        specs_edit = QLineEdit()
        self.products_table.setCellWidget(row, 6, specs_edit)
        
        # متطلبات الجودة
        quality_edit = QLineEdit()
        self.products_table.setCellWidget(row, 7, quality_edit)
        
        # إجراءات
        actions_widget = QWidget()
        actions_layout = QHBoxLayout(actions_widget)
        actions_layout.setContentsMargins(0, 0, 0, 0)
        
        delete_btn = QPushButton()
        delete_btn.setIcon(QIcon('accounting_app/resources/icons/material/red-trash-can-icon.svg'))
        delete_btn.setIconSize(QSize(16, 16))
        delete_btn.setStyleSheet("QPushButton { background: #ffebee; color: #c62828; border-radius: 4px; padding: 2px; }")
        delete_btn.clicked.connect(lambda: self.delete_product_row(row))
        
        actions_layout.addWidget(delete_btn)
        self.products_table.setCellWidget(row, 8, actions_widget)
        
        # حساب المجموع الأولي
        self.calculate_row_total(row)

    def calculate_row_total(self, row):
        """حساب مجموع الصف"""
        try:
            qty = self.products_table.cellWidget(row, 1).value()
            price = self.products_table.cellWidget(row, 2).value()
            discount_percent = self.products_table.cellWidget(row, 3).value()
            tax_percent = self.products_table.cellWidget(row, 4).value()
            
            subtotal = qty * price
            discount_amount = subtotal * (discount_percent / 100)
            after_discount = subtotal - discount_amount
            tax_amount = after_discount * (tax_percent / 100)
            total = after_discount + tax_amount
            
            self.products_table.item(row, 5).setText(f"{total:.2f}")
            self.calculate_totals()
        except Exception as e:
            logging.error(f"Error calculating row total: {e}")

    def delete_product_row(self, row):
        """حذف صف المنتج"""
        self.products_table.removeRow(row)
        self.calculate_totals()

    def calculate_totals(self):
        """حساب المجاميع"""
        try:
            subtotal = 0.0
            total_discount = 0.0
            total_tax = 0.0
            
            for row in range(self.products_table.rowCount()):
                qty = self.products_table.cellWidget(row, 1).value()
                price = self.products_table.cellWidget(row, 2).value()
                discount_percent = self.products_table.cellWidget(row, 3).value()
                tax_percent = self.products_table.cellWidget(row, 4).value()
                
                row_subtotal = qty * price
                row_discount = row_subtotal * (discount_percent / 100)
                row_after_discount = row_subtotal - row_discount
                row_tax = row_after_discount * (tax_percent / 100)
                
                subtotal += row_subtotal
                total_discount += row_discount
                total_tax += row_tax
            
            shipping_cost = self.shipping_cost_spin.value()
            handling_cost = self.handling_cost_spin.value()
            grand_total = subtotal - total_discount + total_tax + shipping_cost + handling_cost
            
            self.subtotal_label.setText(self.tr(f"المجموع الفرعي: {subtotal:.2f}"))
            self.discount_label.setText(self.tr(f"الخصم: {total_discount:.2f}"))
            self.tax_label.setText(self.tr(f"الضريبة: {total_tax:.2f}"))
            self.shipping_label.setText(self.tr(f"الشحن: {shipping_cost:.2f}"))
            self.handling_label.setText(self.tr(f"المعالجة: {handling_cost:.2f}"))
            self.total_label.setText(self.tr(f"المجموع الكلي: {grand_total:.2f}"))
            
            # تحديث المبلغ المتبقي
            self.calculate_remaining()
            
        except Exception as e:
            logging.error(f"Error calculating totals: {e}")

    def calculate_remaining(self):
        """حساب المبلغ المتبقي"""
        try:
            total_text = self.total_label.text()
            total = float(total_text.split(":")[1].strip())
            paid = self.paid_amount_spin.value()
            remaining = total - paid
            self.remaining_amount_label.setText(f"{remaining:.2f}")
        except Exception as e:
            logging.error(f"Error calculating remaining: {e}")

    def save_order(self):
        """حفظ الطلب"""
        try:
            # التحقق من صحة البيانات
            if self.products_table.rowCount() == 0:
                QMessageBox.warning(self, self.tr("تنبيه"), self.tr("يجب إضافة منتج واحد على الأقل"))
                return
            
            # جمع بيانات الطلب
            order_data = self.get_order_data()
            
            # هنا سيتم حفظ البيانات في قاعدة البيانات
            QMessageBox.information(self, self.tr("نجح"), self.tr("تم حفظ أمر الشراء بنجاح"))
            self.accept()
            
        except Exception as e:
            logging.error(f"Error saving order: {e}")
            QMessageBox.critical(self, self.tr("خطأ"), self.tr(f"حدث خطأ أثناء حفظ الطلب: {e}"))

    def get_order_data(self):
        """الحصول على بيانات الطلب"""
        order_data = {
            "supplier_id": self.supplier_combo.currentData(),
            "order_date": self.order_date_edit.date().toString("yyyy-MM-dd"),
            "order_status": self.order_status_combo.currentText(),
            "expected_delivery_date": self.expected_delivery_edit.date().toString("yyyy-MM-dd"),
            "delivery_method": self.delivery_method_combo.currentText(),
            "payment_terms": self.payment_terms_combo.currentText(),
            "payment_status": self.payment_status_combo.currentText(),
            "approval_status": self.approval_status_combo.currentText(),
            "approver": self.approver_combo.currentText(),
            "approval_date": self.approval_date_edit.date().toString("yyyy-MM-dd"),
            "shipping_cost": self.shipping_cost_spin.value(),
            "handling_cost": self.handling_cost_spin.value(),
            "paid_amount": self.paid_amount_spin.value(),
            "notes": self.notes_edit.toPlainText(),
            "shipping_notes": self.shipping_notes_edit.toPlainText(),
            "approval_notes": self.approval_notes_edit.toPlainText(),
            "items": []
        }
        
        # جمع المنتجات
        for row in range(self.products_table.rowCount()):
            product_combo = self.products_table.cellWidget(row, 0)
            product_id = product_combo.currentData()
            qty = self.products_table.cellWidget(row, 1).value()
            price = self.products_table.cellWidget(row, 2).value()
            discount = self.products_table.cellWidget(row, 3).value()
            tax = self.products_table.cellWidget(row, 4).value()
            specs = self.products_table.cellWidget(row, 6).text()
            quality = self.products_table.cellWidget(row, 7).text()
            
            order_data["items"].append({
                "product_id": product_id,
                "quantity": qty,
                "unit_price": price,
                "discount_percent": discount,
                "tax_percent": tax,
                "specifications": specs,
                "quality_requirements": quality
            })
        
        return order_data

    def set_order_data(self, order):
        """تعيين بيانات الطلب للتعديل"""
        # سيتم تنفيذ هذا لاحقاً
        pass

class ProductSelectionDialog(QDialog):
    """نافذة اختيار المنتج"""
    
    def __init__(self, products, parent=None):
        super().__init__(parent)
        self.setWindowTitle(self.tr("اختيار المنتج"))
        self.setMinimumWidth(400)
        self.products = products
        self.selected_product = None
        self.init_ui()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)
        
        # جدول المنتجات
        self.products_table = QTableWidget(self)
        self.products_table.setColumnCount(4)
        self.products_table.setHorizontalHeaderLabels([
            self.tr("المنتج"),
            self.tr("الرمز"),
            self.tr("السعر"),
            self.tr("المخزون")
        ])
        
        # ملء الجدول
        for product in self.products:
            row = self.products_table.rowCount()
            self.products_table.insertRow(row)
            self.products_table.setItem(row, 0, QTableWidgetItem(product['name']))
            self.products_table.setItem(row, 1, QTableWidgetItem(product['code']))
            self.products_table.setItem(row, 2, QTableWidgetItem(str(product['price'])))
            self.products_table.setItem(row, 3, QTableWidgetItem(str(product['stock'])))
        
        self.products_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.products_table.setSelectionMode(QTableWidget.SingleSelection)
        self.products_table.itemSelectionChanged.connect(self.on_selection_changed)
        
        layout.addWidget(self.products_table)
        
        # أزرار
        button_box = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

    def on_selection_changed(self):
        """عند تغيير الاختيار"""
        current_row = self.products_table.currentRow()
        if current_row >= 0:
            self.selected_product = self.products[current_row]

    def get_selected_product(self):
        """الحصول على المنتج المختار"""
        return self.selected_product


class PurchaseOrdersTab(QWidget):
    """التبويب الرئيسي لأوامر الشراء"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("PurchaseOrdersTab")
        self.orders_data = []
        self.init_ui()
        self.load_sample_data()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)

        # شريط الأدوات العلوي
        toolbar = self.create_toolbar()
        layout.addWidget(toolbar)

        # منطقة البحث والفلترة
        search_area = self.create_search_area()
        layout.addWidget(search_area)

        # جدول أوامر الشراء
        self.orders_table = QTableWidget()
        self.orders_table.setColumnCount(7)
        self.orders_table.setHorizontalHeaderLabels([
            self.tr("رقم الطلب"),
            self.tr("المورد"),
            self.tr("تاريخ الطلب"),
            self.tr("تاريخ التسليم المتوقع"),
            self.tr("المبلغ الكلي"),
            self.tr("الحالة"),
            self.tr("نسبة الاستلام")
        ])

        # تخصيص الجدول
        header = self.orders_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.Interactive)

        self.orders_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.orders_table.setSelectionMode(QTableWidget.SingleSelection)
        self.orders_table.setAlternatingRowColors(True)
        self.orders_table.itemSelectionChanged.connect(self.on_order_selected)

        layout.addWidget(self.orders_table)

        # شريط الحالة
        status_bar = self.create_status_bar()
        layout.addWidget(status_bar)

    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar = QFrame()
        toolbar.setFrameStyle(QFrame.StyledPanel)
        layout = QHBoxLayout(toolbar)

        # أزرار العمليات الرئيسية
        self.add_order_btn = QPushButton(self.tr("إنشاء طلب شراء جديد"))
        self.add_order_btn.clicked.connect(self.add_new_order)

        self.edit_order_btn = QPushButton(self.tr("تعديل"))
        self.edit_order_btn.clicked.connect(self.edit_order)
        self.edit_order_btn.setEnabled(False)

        self.approve_order_btn = QPushButton(self.tr("موافقة"))
        self.approve_order_btn.clicked.connect(self.approve_order)
        self.approve_order_btn.setEnabled(False)

        self.cancel_order_btn = QPushButton(self.tr("إلغاء"))
        self.cancel_order_btn.clicked.connect(self.cancel_order)
        self.cancel_order_btn.setEnabled(False)

        self.print_order_btn = QPushButton(self.tr("طباعة"))
        self.print_order_btn.clicked.connect(self.print_order)
        self.print_order_btn.setEnabled(False)

        # إضافة الأزرار
        layout.addWidget(self.add_order_btn)
        layout.addWidget(self.edit_order_btn)
        layout.addWidget(self.approve_order_btn)
        layout.addWidget(self.cancel_order_btn)
        layout.addWidget(self.print_order_btn)
        layout.addStretch()

        return toolbar

    def create_search_area(self):
        """إنشاء منطقة البحث والفلترة"""
        search_group = QGroupBox(self.tr("البحث والفلترة"))
        layout = QHBoxLayout(search_group)

        # حقل البحث
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText(self.tr("البحث في أوامر الشراء..."))
        self.search_input.textChanged.connect(self.filter_orders)

        # فلتر الحالة
        self.status_filter = QComboBox()
        self.status_filter.addItem(self.tr("جميع الحالات"), "")
        self.status_filter.addItems([
            self.tr("مسودة"),
            self.tr("مرسل"),
            self.tr("مؤكد"),
            self.tr("قيد التحضير"),
            self.tr("مستلم"),
            self.tr("ملغي")
        ])
        self.status_filter.currentTextChanged.connect(self.filter_orders)

        layout.addWidget(QLabel(self.tr("البحث:")))
        layout.addWidget(self.search_input)
        layout.addWidget(QLabel(self.tr("الحالة:")))
        layout.addWidget(self.status_filter)
        layout.addStretch()

        return search_group

    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_bar = QFrame()
        status_bar.setFrameStyle(QFrame.StyledPanel)
        layout = QHBoxLayout(status_bar)

        self.status_label = QLabel(self.tr("جاهز"))
        self.total_orders_label = QLabel(self.tr("إجمالي الطلبات: 0"))
        self.total_amount_label = QLabel(self.tr("إجمالي المبلغ: 0.00"))

        layout.addWidget(self.status_label)
        layout.addStretch()
        layout.addWidget(self.total_orders_label)
        layout.addWidget(self.total_amount_label)

        return status_bar

    def load_sample_data(self):
        """تحميل بيانات وهمية للاختبار"""
        sample_orders = [
            {
                'order_number': 'PO-202401-1001',
                'supplier_name': 'شركة المواد الغذائية المتقدمة',
                'order_date': '2024-01-15',
                'expected_delivery_date': '2024-01-25',
                'total_amount': 15000.00,
                'status': 'مؤكد',
                'received_percentage': 75
            },
            {
                'order_number': 'PO-202401-1002',
                'supplier_name': 'مؤسسة التقنية الحديثة',
                'order_date': '2024-01-20',
                'expected_delivery_date': '2024-01-30',
                'total_amount': 25000.00,
                'status': 'مرسل',
                'received_percentage': 0
            },
            {
                'order_number': 'PO-202401-1003',
                'supplier_name': 'أحمد محمد للتجارة',
                'order_date': '2024-01-22',
                'expected_delivery_date': '2024-02-01',
                'total_amount': 8500.00,
                'status': 'مسودة',
                'received_percentage': 0
            }
        ]

        self.orders_data = sample_orders
        self.populate_orders_table()
        self.update_statistics()

    def populate_orders_table(self):
        """ملء جدول أوامر الشراء"""
        self.orders_table.setRowCount(len(self.orders_data))

        for row, order in enumerate(self.orders_data):
            # رقم الطلب
            self.orders_table.setItem(row, 0, QTableWidgetItem(order['order_number']))

            # المورد
            self.orders_table.setItem(row, 1, QTableWidgetItem(order['supplier_name']))

            # تاريخ الطلب
            self.orders_table.setItem(row, 2, QTableWidgetItem(order['order_date']))

            # تاريخ التسليم المتوقع
            self.orders_table.setItem(row, 3, QTableWidgetItem(order['expected_delivery_date']))

            # المبلغ الكلي
            self.orders_table.setItem(row, 4, QTableWidgetItem(f"{order['total_amount']:,.2f}"))

            # الحالة
            status_item = QTableWidgetItem(order['status'])
            if order['status'] == 'مؤكد':
                status_item.setBackground(Qt.green)
            elif order['status'] == 'مرسل':
                status_item.setBackground(Qt.yellow)
            elif order['status'] == 'مسودة':
                status_item.setBackground(Qt.lightGray)
            else:
                status_item.setBackground(Qt.red)
            self.orders_table.setItem(row, 5, status_item)

            # نسبة الاستلام
            self.orders_table.setItem(row, 6, QTableWidgetItem(f"{order['received_percentage']}%"))

    def update_statistics(self):
        """تحديث الإحصائيات"""
        total_orders = len(self.orders_data)
        total_amount = sum(order['total_amount'] for order in self.orders_data)

        self.total_orders_label.setText(f"إجمالي الطلبات: {total_orders}")
        self.total_amount_label.setText(f"إجمالي المبلغ: {total_amount:,.2f}")

    def filter_orders(self):
        """فلترة أوامر الشراء"""
        search_text = self.search_input.text().lower()
        status_filter = self.status_filter.currentText()

        for row in range(self.orders_table.rowCount()):
            show_row = True

            # فلترة النص
            if search_text:
                order_number = self.orders_table.item(row, 0).text().lower()
                supplier_name = self.orders_table.item(row, 1).text().lower()
                if search_text not in order_number and search_text not in supplier_name:
                    show_row = False

            # فلترة الحالة
            if status_filter and status_filter != self.tr("جميع الحالات") and show_row:
                order_status = self.orders_table.item(row, 5).text()
                if order_status != status_filter:
                    show_row = False

            self.orders_table.setRowHidden(row, not show_row)

    def on_order_selected(self):
        """عند اختيار طلب من القائمة"""
        current_row = self.orders_table.currentRow()
        if current_row >= 0:
            # تفعيل الأزرار
            self.edit_order_btn.setEnabled(True)
            self.approve_order_btn.setEnabled(True)
            self.cancel_order_btn.setEnabled(True)
            self.print_order_btn.setEnabled(True)

            order_number = self.orders_table.item(current_row, 0).text()
            self.status_label.setText(f"تم اختيار الطلب: {order_number}")
        else:
            self.edit_order_btn.setEnabled(False)
            self.approve_order_btn.setEnabled(False)
            self.cancel_order_btn.setEnabled(False)
            self.print_order_btn.setEnabled(False)

    def add_new_order(self):
        """إضافة طلب شراء جديد"""
        dialog = PurchaseOrderDialog(self)
        if dialog.exec() == QDialog.Accepted:
            self.status_label.setText(self.tr("تم إضافة طلب شراء جديد بنجاح"))
            # إعادة تحميل البيانات
            self.load_sample_data()

    def edit_order(self):
        """تعديل الطلب المختار"""
        current_row = self.orders_table.currentRow()
        if current_row >= 0:
            order_number = self.orders_table.item(current_row, 0).text()
            order_data = next((order for order in self.orders_data if order['order_number'] == order_number), None)

            if order_data:
                dialog = PurchaseOrderDialog(self, order_data)
                if dialog.exec() == QDialog.Accepted:
                    self.status_label.setText(self.tr("تم تحديث الطلب بنجاح"))

    def approve_order(self):
        """الموافقة على الطلب"""
        current_row = self.orders_table.currentRow()
        if current_row >= 0:
            order_number = self.orders_table.item(current_row, 0).text()

            reply = QMessageBox.question(
                self,
                self.tr("تأكيد الموافقة"),
                self.tr(f"هل أنت متأكد من الموافقة على الطلب '{order_number}'؟"),
                QMessageBox.Yes | QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # تحديث حالة الطلب
                for order in self.orders_data:
                    if order['order_number'] == order_number:
                        order['status'] = 'مؤكد'
                        break

                self.populate_orders_table()
                self.status_label.setText(self.tr("تم الموافقة على الطلب بنجاح"))

    def cancel_order(self):
        """إلغاء الطلب"""
        current_row = self.orders_table.currentRow()
        if current_row >= 0:
            order_number = self.orders_table.item(current_row, 0).text()

            reply = QMessageBox.question(
                self,
                self.tr("تأكيد الإلغاء"),
                self.tr(f"هل أنت متأكد من إلغاء الطلب '{order_number}'؟"),
                QMessageBox.Yes | QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # تحديث حالة الطلب
                for order in self.orders_data:
                    if order['order_number'] == order_number:
                        order['status'] = 'ملغي'
                        break

                self.populate_orders_table()
                self.status_label.setText(self.tr("تم إلغاء الطلب"))

    def print_order(self):
        """طباعة الطلب"""
        current_row = self.orders_table.currentRow()
        if current_row >= 0:
            order_number = self.orders_table.item(current_row, 0).text()
            QMessageBox.information(
                self,
                self.tr("طباعة"),
                self.tr(f"سيتم طباعة الطلب '{order_number}'")
            )