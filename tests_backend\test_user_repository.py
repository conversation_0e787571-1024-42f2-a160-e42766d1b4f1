import sys
import os
import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

# إضافة مسار المشروع الجذري لمسار الاستيراد
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from database.models import Base, AppUser
from database.repositories.user_repository import UserRepository
from database.utils import hash_password

@pytest.fixture(scope="function")
def db_session():
    engine = create_engine('sqlite:///:memory:')
    Base.metadata.create_all(engine)
    Session = sessionmaker(bind=engine)
    session = Session()
    yield session
    session.close()


def test_create_and_get_user(db_session):
    repo = UserRepository(db_session)
    user = AppUser(username="testuser", password_hash=hash_password("pass"), role="admin")
    repo.create(user)
    fetched = repo.get_by_username("testuser")
    assert fetched is not None
    assert fetched.username == "testuser"
    assert fetched.role == "admin" 