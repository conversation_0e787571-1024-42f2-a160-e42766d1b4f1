2025-07-15 18:32:12 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: could not convert string to float: ''
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-15 20:53:02 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'active_check'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 13:36:01 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: cannot access local variable 'QPushButton' where it is not associated with a value
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 13:36:03 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: cannot access local variable 'QPushButton' where it is not associated with a value
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 13:36:03 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: cannot access local variable 'QPushButton' where it is not associated with a value
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 13:36:03 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: cannot access local variable 'QPushButton' where it is not associated with a value
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 13:36:04 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: cannot access local variable 'QPushButton' where it is not associated with a value
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 13:36:05 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: cannot access local variable 'QPushButton' where it is not associated with a value
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 13:36:41 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: cannot access local variable 'QPushButton' where it is not associated with a value
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 13:36:42 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: cannot access local variable 'QPushButton' where it is not associated with a value
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 13:37:43 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: cannot access local variable 'QPushButton' where it is not associated with a value
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 13:38:19 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: cannot access local variable 'QPushButton' where it is not associated with a value
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 13:38:20 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: cannot access local variable 'QPushButton' where it is not associated with a value
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 13:38:20 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: cannot access local variable 'QPushButton' where it is not associated with a value
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 13:38:20 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: cannot access local variable 'QPushButton' where it is not associated with a value
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 13:38:21 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: cannot access local variable 'QPushButton' where it is not associated with a value
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 13:38:21 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: cannot access local variable 'QPushButton' where it is not associated with a value
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 13:38:38 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: cannot access local variable 'QPushButton' where it is not associated with a value
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 13:38:38 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: cannot access local variable 'QPushButton' where it is not associated with a value
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 13:38:38 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: cannot access local variable 'QPushButton' where it is not associated with a value
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 13:38:38 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: cannot access local variable 'QPushButton' where it is not associated with a value
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 13:42:10 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'name_edit'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 13:42:11 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'name_edit'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 13:42:11 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'name_edit'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 13:42:12 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'name_edit'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 13:42:12 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'name_edit'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 14:28:39 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: cannot import name 'QPropertyAnimation' from 'PySide6.QtWidgets' (G:\Gaea\GaeaDev\1l0.00\l0.31\.venv\Lib\site-packages\PySide6\QtWidgets.pyd)
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 14:31:54 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: cannot import name 'QPropertyAnimation' from 'PySide6.QtWidgets' (G:\Gaea\GaeaDev\1l0.00\l0.31\.venv\Lib\site-packages\PySide6\QtWidgets.pyd)
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 14:32:07 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: cannot import name 'QPropertyAnimation' from 'PySide6.QtWidgets' (G:\Gaea\GaeaDev\1l0.00\l0.31\.venv\Lib\site-packages\PySide6\QtWidgets.pyd)
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 14:32:57 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'name_edit'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 14:32:59 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'name_edit'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 14:32:59 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'name_edit'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 14:33:02 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'name_edit'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 14:33:03 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'name_edit'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 14:33:21 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'name_edit'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 14:33:21 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'name_edit'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 15:49:50 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute '_create_arrow_icon'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 15:49:52 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute '_create_arrow_icon'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 15:49:52 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute '_create_arrow_icon'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 15:49:52 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute '_create_arrow_icon'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 15:49:53 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute '_create_arrow_icon'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 15:49:53 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute '_create_arrow_icon'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 15:49:53 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute '_create_arrow_icon'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 15:49:54 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute '_create_arrow_icon'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 15:52:13 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: name 'QPoint' is not defined
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 15:52:14 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: name 'QPoint' is not defined
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 15:52:14 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: name 'QPoint' is not defined
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 15:52:14 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: name 'QPoint' is not defined
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 16:15:41 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'carton_qty_edit'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 16:15:41 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'carton_qty_edit'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 16:15:42 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'carton_qty_edit'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 16:15:42 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'carton_qty_edit'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 16:16:55 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'carton_qty_edit'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 16:16:56 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'carton_qty_edit'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 16:16:56 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'carton_qty_edit'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 16:16:57 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'carton_qty_edit'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 16:18:48 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'qty_edit'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 16:18:48 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'qty_edit'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 16:18:48 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'qty_edit'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 16:18:49 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'qty_edit'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 16:18:49 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'qty_edit'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 16:18:49 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'qty_edit'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 16:18:49 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'qty_edit'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 16:18:50 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'qty_edit'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 16:18:50 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'qty_edit'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 16:18:50 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'qty_edit'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 16:18:50 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'qty_edit'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 16:18:51 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'qty_edit'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 16:18:51 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'qty_edit'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 16:18:51 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'qty_edit'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 16:18:52 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'qty_edit'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 16:18:52 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'qty_edit'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 16:19:17 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'qty_edit'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 16:19:18 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'qty_edit'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 16:19:18 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'qty_edit'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 16:19:18 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'qty_edit'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 16:20:18 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'qty_edit'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 16:20:19 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'qty_edit'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 16:20:19 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'qty_edit'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 16:20:36 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'qty_edit'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 16:20:36 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'qty_edit'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 16:20:37 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'qty_edit'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 16:20:37 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'qty_edit'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 17:22:49 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'warehouse_advanced_widget'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 17:22:50 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'warehouse_advanced_widget'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 17:22:51 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'warehouse_advanced_widget'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 17:22:51 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'warehouse_advanced_widget'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 17:22:51 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'warehouse_advanced_widget'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 17:22:52 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'warehouse_advanced_widget'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 17:22:52 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'warehouse_advanced_widget'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 17:23:16 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'warehouse_advanced_widget'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 17:23:17 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'warehouse_advanced_widget'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 17:23:17 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'warehouse_advanced_widget'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 17:41:04 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: cannot access local variable 'QWidget' where it is not associated with a value
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 17:41:06 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: cannot access local variable 'QWidget' where it is not associated with a value
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 17:41:06 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: cannot access local variable 'QWidget' where it is not associated with a value
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 17:41:06 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: cannot access local variable 'QWidget' where it is not associated with a value
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 18:15:25 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'ProductDialog' object has no attribute 'description_edit'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 18:52:59 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'WarehouseDialog' object has no attribute 'location_edit'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 18:56:10 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: cannot access local variable 'QVBoxLayout' where it is not associated with a value
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 18:56:11 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: cannot access local variable 'QVBoxLayout' where it is not associated with a value
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 18:56:12 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: cannot access local variable 'QVBoxLayout' where it is not associated with a value
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 18:56:12 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: cannot access local variable 'QVBoxLayout' where it is not associated with a value
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 18:57:29 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: cannot access local variable 'QVBoxLayout' where it is not associated with a value
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 18:57:29 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: cannot access local variable 'QVBoxLayout' where it is not associated with a value
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 18:57:30 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: cannot access local variable 'QVBoxLayout' where it is not associated with a value
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 18:57:30 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: cannot access local variable 'QVBoxLayout' where it is not associated with a value
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 18:57:30 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: cannot access local variable 'QVBoxLayout' where it is not associated with a value
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 18:57:30 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: cannot access local variable 'QVBoxLayout' where it is not associated with a value
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 18:57:31 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: cannot access local variable 'QVBoxLayout' where it is not associated with a value
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-16 18:58:43 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'WarehouseDialog' object has no attribute 'location_edit'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-18 20:53:08 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'WarehouseDialog' object has no attribute 'main_image_path'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-18 20:53:15 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'WarehouseDialog' object has no attribute 'rack_image_path'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-18 21:13:08 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-18 21:13:09 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-18 21:24:20 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-18 21:32:22 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-18 21:32:22 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-18 21:32:23 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-18 21:32:23 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-18 21:32:23 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-18 21:32:25 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-18 21:32:26 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-18 21:32:29 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-18 21:32:39 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-18 21:32:39 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-19 20:04:14 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-19 20:04:15 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-19 20:04:15 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-19 20:04:27 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-19 20:04:32 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-19 20:04:32 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-19 20:04:32 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-19 20:04:34 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-19 20:04:34 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-19 20:35:37 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-19 20:35:38 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-19 20:35:38 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-19 21:03:48 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-19 21:03:49 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-19 21:03:49 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-21 16:30:33 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-21 16:30:34 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-21 16:30:34 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-21 16:30:45 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-21 16:31:03 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-21 16:31:04 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-21 16:31:04 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-21 16:31:04 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-21 16:31:05 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-21 16:32:00 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-21 16:32:00 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-21 16:32:00 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-21 16:33:56 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-21 16:33:56 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-21 16:33:56 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-21 16:34:05 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-21 16:34:06 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-21 16:34:06 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-21 16:34:07 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-21 16:34:08 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-21 16:34:09 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-21 16:34:38 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'WarehouseDialog' object has no attribute 'set_warehouse_data'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-21 16:41:12 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'WarehouseDialog' object has no attribute 'location_edit'
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-27 09:01:16 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-27 09:11:31 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-07-27 09:11:39 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-08-02 21:33:38 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-08-02 21:39:43 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-08-02 21:40:06 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-08-02 21:40:15 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-08-02 21:40:22 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
2025-08-02 21:40:42 [CRITICAL] SmartAccountingApp.ErrorHandler: خطأ system: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
File: error_handler.py:144
Function: _log_error
Traceback: (None, None, None)
NoneType: None
