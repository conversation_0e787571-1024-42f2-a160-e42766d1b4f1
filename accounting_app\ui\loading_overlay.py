from PySide6.QtWidgets import QWidget, QVBoxLayout, QLabel, QProgressBar
from PySide6.QtCore import Qt

class LoadingOverlay(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowFlags(Qt.Dialog | Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setAttribute(Qt.WA_DeleteOnClose)
        self.setStyleSheet("background: rgba(30, 41, 55, 0.6);")
        self.setFixedSize(parent.size())
        layout = QVBoxLayout()
        layout.setAlignment(Qt.AlignCenter)
        self.label = QLabel(self.tr("جاري التحميل..."))
        self.label.setStyleSheet("color: #FFF; font-size: 18px;")
        self.progress = QProgressBar()
        self.progress.setRange(0, 0)  # Indeterminate
        self.progress.setFixedWidth(200)
        layout.addWidget(self.label)
        layout.addWidget(self.progress)
        self.setLayout(layout)
        self.hide()

    def show_overlay(self, text=None):
        if text is None:
            text = self.tr("جاري التحميل...")
        self.label.setText(text)
        self.setFixedSize(self.parent().size())
        self.show()

    def hide_overlay(self):
        self.hide() 