# دليل آلية منع تشغيل مثيلات متعددة

## نظرة عامة

آلية منع تشغيل مثيلات متعددة للتطبيق هي نظام يضمن تشغيل نسخة واحدة فقط من التطبيق في نفس الوقت. تستخدم هذه الآلية مزيجاً من ملف قفل (Lock File) ومنفذ Socket للتحقق من وجود نسخة أخرى قيد التشغيل.

## المكونات الرئيسية

### 1. فئة `SingleInstanceApp`

الفئة الرئيسية المسؤولة عن إدارة قفل التطبيق.

#### الخصائص:
- `app_name`: اسم التطبيق (يستخدم في اسم ملف القفل)
- `port`: رقم المنفذ للتحقق من وجود نسخة أخرى
- `lock_file`: كائن ملف القفل
- `socket_server`: خادم Socket
- `is_running`: حالة تشغيل التطبيق

#### الطرق الرئيسية:

##### `acquire_lock() -> bool`
محاولة الحصول على قفل التطبيق:
- إنشاء ملف قفل مع معرف العملية
- فحص وجود نسخة أخرى قيد التشغيل
- حجز منفذ Socket
- تسجيل دوال التنظيف

##### `release_lock()`
إطلاق قفل التطبيق:
- إغلاق منفذ Socket
- إغلاق وحذف ملف القفل
- تنظيف الموارد

##### `check_if_running() -> Tuple[bool, Optional[str]]`
فحص ما إذا كان التطبيق قيد التشغيل:
- فحص ملف القفل
- فحص المنفذ
- إرجاع حالة التشغيل ورسالة توضيحية

### 2. دالة `ensure_single_instance()`

دالة مساعدة لضمان تشغيل نسخة واحدة من التطبيق.

#### المعاملات:
- `app_name`: اسم التطبيق
- `port`: رقم المنفذ

#### القيم المُرجعة:
- كائن `SingleInstanceApp` إذا نجح في الحصول على القفل

#### الاستثناءات:
- `RuntimeError`: إذا كان التطبيق قيد التشغيل بالفعل

## آلية العمل

### 1. ملف القفل (Lock File)

```python
# إنشاء ملف قفل في مجلد مؤقت
lock_file_path = os.path.join(tempfile.gettempdir(), f"{app_name}.lock")

# كتابة معرف العملية في الملف
with open(lock_file_path, 'w') as f:
    f.write(str(os.getpid()))
```

**المزايا:**
- بسيط وسريع
- يعمل على جميع أنظمة التشغيل
- يحتفظ بمعرف العملية للتحقق

**القيود:**
- قد يبقى الملف إذا انتهت العملية بشكل غير طبيعي
- لا يعمل عبر الشبكة

### 2. منفذ Socket

```python
# محاولة فتح منفذ Socket
socket_server = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
socket_server.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
socket_server.bind(('localhost', port))
socket_server.listen(1)
```

**المزايا:**
- أكثر موثوقية
- يعمل عبر الشبكة
- تنظيف تلقائي عند انتهاء العملية

**القيود:**
- قد يكون المنفذ محجوزاً من تطبيق آخر
- يتطلب صلاحيات إضافية في بعض الأنظمة

## الاستخدام

### الاستخدام الأساسي

```python
from accounting_app.utils import ensure_single_instance

def main():
    try:
        # محاولة الحصول على قفل التطبيق
        single_instance = ensure_single_instance("my_app", 12345)
        print("تم الحصول على قفل التطبيق بنجاح")
        
        # تشغيل التطبيق
        # ...
        
    except RuntimeError as e:
        print(f"لا يمكن تشغيل نسخة أخرى: {e}")
        sys.exit(1)
    finally:
        # إطلاق القفل عند الإغلاق
        if 'single_instance' in locals():
            single_instance.release_lock()
```

### الاستخدام المتقدم

```python
from accounting_app.utils import SingleInstanceApp

def main():
    # إنشاء كائن النسخة الواحدة
    instance = SingleInstanceApp("my_app", 12345)
    
    # فحص حالة التطبيق
    is_running, message = instance.check_if_running()
    if is_running:
        print(f"التطبيق قيد التشغيل: {message}")
        return
    
    # محاولة الحصول على القفل
    if instance.acquire_lock():
        print("تم الحصول على قفل التطبيق")
        
        # تشغيل التطبيق
        # ...
        
        # إطلاق القفل
        instance.release_lock()
    else:
        print("فشل في الحصول على قفل التطبيق")
```

## التكامل مع التطبيق

### في `main.py`

```python
def main():
    app = QApplication(sys.argv)
    
    # التحقق من عدم تشغيل نسخة أخرى
    try:
        single_instance = ensure_single_instance("accounting_app", 12345)
        print("✓ تم الحصول على قفل التطبيق بنجاح")
    except RuntimeError as e:
        # عرض رسالة تحذير للمستخدم
        msg_box = QMessageBox()
        msg_box.setIcon(QMessageBox.Warning)
        msg_box.setWindowTitle("تحذير")
        msg_box.setText("لا يمكن تشغيل نسخة أخرى من التطبيق")
        msg_box.setInformativeText(str(e))
        msg_box.setStandardButtons(QMessageBox.Ok)
        msg_box.exec()
        
        print(f"❌ {e}")
        sys.exit(1)
    
    # تشغيل التطبيق
    try:
        sys.exit(app.exec())
    finally:
        # إطلاق قفل التطبيق عند الإغلاق
        if 'single_instance' in locals():
            single_instance.release_lock()
```

## معالجة الأخطاء

### 1. ملف قفل قديم

```python
def _acquire_file_lock(self) -> bool:
    lock_file_path = self._get_lock_file_path()
    if os.path.exists(lock_file_path):
        try:
            with open(lock_file_path, 'r') as f:
                pid_str = f.read().strip()
                if pid_str.isdigit():
                    pid = int(pid_str)
                    # فحص ما إذا كانت العملية لا تزال قيد التشغيل
                    if self._is_process_running(pid):
                        return False
                    else:
                        # العملية غير موجودة، حذف الملف القديم
                        os.remove(lock_file_path)
        except:
            # في حالة خطأ في قراءة الملف، حذفه
            try:
                os.remove(lock_file_path)
            except:
                pass
    
    return True
```

### 2. منفذ محجوز

```python
def _acquire_socket_lock(self) -> bool:
    try:
        self.socket_server = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.socket_server.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        self.socket_server.bind(('localhost', self.port))
        self.socket_server.listen(1)
        return True
    except socket.error as e:
        logger.warning(f"المنفذ {self.port} محجوز بالفعل: {e}")
        return False
```

## التنظيف التلقائي

### 1. تسجيل دوال التنظيف

```python
if not self._cleanup_registered:
    atexit.register(self.release_lock)
    signal.signal(signal.SIGINT, self._signal_handler)
    signal.signal(signal.SIGTERM, self._signal_handler)
    self._cleanup_registered = True
```

### 2. معالج الإشارات

```python
def _signal_handler(self, signum, frame):
    logger.info(f"استلام إشارة {signum}، إغلاق التطبيق...")
    self.release_lock()
    sys.exit(0)
```

## الاختبار

### تشغيل الاختبارات

```bash
cd accounting_app/scripts
python test_single_instance.py
```

### أنواع الاختبارات

1. **الاختبار الأساسي**: فحص الحصول على قفل وإطلاقه
2. **اختبار المنفذ**: فحص حجز المنفذ
3. **اختبار ملف القفل**: فحص إنشاء وحذف ملف القفل
4. **اختبار المحاولات المتزامنة**: فحص منع الحصول على قفل متعدد
5. **اختبار التنظيف**: فحص التنظيف عند الإغلاق

## أفضل الممارسات

### 1. اختيار اسم فريد للتطبيق

```python
# استخدم اسم فريد لتجنب التعارض مع تطبيقات أخرى
single_instance = ensure_single_instance("my_unique_app_name", 12345)
```

### 2. اختيار منفذ متاح

```python
# استخدم منفذ في النطاق المتاح (1024-65535)
# تجنب المنافذ المعروفة (80, 443, 3306, إلخ)
single_instance = ensure_single_instance("my_app", 12345)
```

### 3. معالجة الأخطاء

```python
try:
    single_instance = ensure_single_instance("my_app", 12345)
except RuntimeError as e:
    # عرض رسالة مناسبة للمستخدم
    show_error_message(str(e))
    sys.exit(1)
```

### 4. التنظيف التلقائي

```python
try:
    # تشغيل التطبيق
    sys.exit(app.exec())
finally:
    # إطلاق القفل عند الإغلاق
    if 'single_instance' in locals():
        single_instance.release_lock()
```

## استكشاف الأخطاء

### 1. التطبيق لا يبدأ

**الأسباب المحتملة:**
- ملف قفل قديم من عملية سابقة
- منفذ محجوز من تطبيق آخر
- مشاكل في الصلاحيات

**الحلول:**
- حذف ملف القفل يدوياً
- تغيير رقم المنفذ
- تشغيل التطبيق بصلاحيات إدارية

### 2. التطبيق لا يغلق بشكل صحيح

**الأسباب المحتملة:**
- عدم تسجيل دوال التنظيف
- انتهاء العملية بشكل غير طبيعي

**الحلول:**
- التأكد من تسجيل `atexit` و `signal handlers`
- إضافة `try/finally` في `main()`

### 3. تعارض مع تطبيقات أخرى

**الأسباب المحتملة:**
- استخدام نفس اسم التطبيق أو المنفذ

**الحلول:**
- تغيير اسم التطبيق أو المنفذ
- استخدام أسماء فريدة

## الخلاصة

آلية منع تشغيل مثيلات متعددة هي أداة مهمة لضمان استقرار التطبيق ومنع تضارب البيانات. تستخدم مزيجاً من ملف قفل ومنفذ Socket لتوفير حماية شاملة وموثوقة. 