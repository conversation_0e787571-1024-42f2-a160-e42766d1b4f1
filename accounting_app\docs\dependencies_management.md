# إدارة الاعتمادات (Dependencies Management)

## 📋 **ملخص الملفات:**

### **ملفات الاعتمادات:**
```
accounting_app/
├── requirements.txt              # تبعيات الإنتاج الأساسية
├── requirements-minimal.txt      # تبعيات الإنتاج الدنيا
├── requirements-lock.txt         # تبعيات مقفلة مع التبعيات الفرعية
├── dev-requirements.txt          # تبعيات التطوير
├── setup.py                      # إعداد الحزمة (legacy)
├── pyproject.toml               # إعداد الحزمة (modern)
├── .pre-commit-config.yaml      # تكوين pre-commit
└── Makefile                     # أوامر إدارة المشروع
```

## 🎯 **استراتيجية إدارة الاعتمادات:**

### **1. فصل بيئات التطوير والإنتاج:**

#### **بيئة الإنتاج (Production):**
```bash
# تثبيت التبعيات الأساسية فقط
pip install -r requirements.txt

# أو التبعيات الدنيا
pip install -r requirements-minimal.txt
```

#### **بيئة التطوير (Development):**
```bash
# تثبيت جميع تبعيات التطوير
pip install -r dev-requirements.txt
```

### **2. تجميد الإصدارات:**

#### **قبل الإصلاح (مشكل):**
```txt
PySide6>=6.5
pygame>=2.0.0
```

#### **بعد الإصلاح (صحيح):**
```txt
PySide6==6.6.1
pygame==2.5.2
```

## 📦 **تفاصيل الملفات:**

### **`requirements.txt` - تبعيات الإنتاج:**
```txt
# Production Dependencies - التبعيات المطلوبة للإنتاج
PySide6==6.6.1
pygame==2.5.2
```

**الميزات:**
- ✅ إصدارات محددة ومجمدة
- ✅ تبعيات الإنتاج الأساسية فقط
- ✅ حجم صغير للتثبيت السريع

### **`requirements-minimal.txt` - التبعيات الدنيا:**
```txt
# Minimal Production Dependencies - التبعيات الدنيا للإنتاج
PySide6==6.6.1
pygame==2.5.2
```

**الاستخدام:**
- 🚀 للإنتاج في بيئات محدودة الموارد
- 🐳 لحاويات Docker
- 📱 للتطبيقات المحمولة

### **`dev-requirements.txt` - تبعيات التطوير:**
```txt
# Development Dependencies - تبعيات التطوير
-r requirements.txt

# Testing & Quality Tools
pytest==7.4.3
pytest-qt==4.2.0
pytest-cov==4.1.0
coverage==7.3.2

# Code Quality & Formatting
black==23.11.0
flake8==6.1.0
isort==5.12.0
mypy==1.7.1

# Documentation
sphinx==7.2.6
sphinx-rtd-theme==1.3.0

# Development Utilities
pre-commit==3.5.0
jupyter==1.0.0
ipython==8.17.2
```

**الميزات:**
- ✅ يشمل تبعيات الإنتاج
- ✅ أدوات الاختبار والجودة
- ✅ أدوات التوثيق
- ✅ أدوات التطوير

### **`requirements-lock.txt` - التبعيات المقفلة:**
```txt
# Locked Dependencies - تبعيات مقفلة مع جميع التبعيات الفرعية
PySide6==6.6.1
PySide6-Qt6==6.6.1
PySide6-shiboken6==6.6.1
pygame==2.5.2
```

**الاستخدام:**
- 🔒 لضمان نفس البيئة في جميع الأماكن
- 🏭 للبيئات الإنتاجية الحرجة
- 🧪 للاختبارات المتكررة

## 🛠️ **أدوات إدارة المشروع:**

### **`setup.py` - إعداد الحزمة (Legacy):**
```python
setup(
    name="smart-accounting-app",
    version="1.0.0",
    install_requires=read_requirements('requirements.txt'),
    extras_require={
        'dev': read_requirements('dev-requirements.txt'),
        'test': ['pytest==7.4.3', 'pytest-qt==4.2.0'],
        'docs': ['sphinx==7.2.6', 'sphinx-rtd-theme==1.3.0'],
    },
)
```

### **`pyproject.toml` - إعداد الحزمة (Modern):**
```toml
[project]
name = "smart-accounting-app"
version = "1.0.0"
dependencies = [
    "PySide6==6.6.1",
    "pygame==2.5.2",
]

[project.optional-dependencies]
dev = [
    "pytest==7.4.3",
    "black==23.11.0",
    # ... المزيد
]
```

## 🔧 **أوامر إدارة المشروع:**

### **استخدام Makefile:**
```bash
# عرض جميع الأوامر المتاحة
make help

# إعداد بيئة التطوير
make setup-dev

# إعداد بيئة الإنتاج
make setup-prod

# تشغيل التطبيق
make run

# تشغيل الاختبارات
make test

# فحص جودة الكود
make lint

# تنسيق الكود
make format

# فحص الأنواع
make type-check

# تحويل ملفات الترجمة
make convert-ts
```

### **أوامر pip المباشرة:**
```bash
# تثبيت تبعيات الإنتاج
pip install -r requirements.txt

# تثبيت تبعيات التطوير
pip install -r dev-requirements.txt

# تثبيت التبعيات الدنيا
pip install -r requirements-minimal.txt

# تثبيت مع التبعيات الاختيارية
pip install -e .[dev,test,docs]
```

## 🚀 **سيناريوهات الاستخدام:**

### **1. مطور جديد:**
```bash
# إعداد بيئة التطوير الكاملة
make setup-dev

# أو يدوياً
python -m venv .venv
source .venv/bin/activate  # Linux/Mac
pip install -r dev-requirements.txt
pre-commit install
```

### **2. بيئة الإنتاج:**
```bash
# تثبيت التبعيات الدنيا
pip install -r requirements-minimal.txt

# أو التبعيات الكاملة
pip install -r requirements.txt
```

### **3. بيئة الاختبار:**
```bash
# تثبيت مع أدوات الاختبار
pip install -e .[test]

# تشغيل الاختبارات
make test
```

### **4. بيئة التوثيق:**
```bash
# تثبيت مع أدوات التوثيق
pip install -e .[docs]

# بناء التوثيق
make docs
```

## 🔒 **أمان وجودة الكود:**

### **Pre-commit Hooks:**
```yaml
# .pre-commit-config.yaml
repos:
  - repo: https://github.com/psf/black
    rev: 23.11.0
    hooks:
      - id: black
  - repo: https://github.com/pycqa/flake8
    rev: 6.1.0
    hooks:
      - id: flake8
```

### **إعداد Pre-commit:**
```bash
# تثبيت pre-commit
pip install pre-commit

# إعداد hooks
pre-commit install
pre-commit install --hook-type commit-msg
```

## 📊 **مراقبة التبعيات:**

### **فحص التبعيات القديمة:**
```bash
# فحص التبعيات القديمة
pip list --outdated

# تحديث التبعيات
pip install --upgrade package-name
```

### **فحص الأمان:**
```bash
# فحص الثغرات الأمنية
bandit -r accounting_app/

# فحص التبعيات المعروفة
safety check
```

## 🎯 **أفضل الممارسات:**

### **1. تجميد الإصدارات:**
- ✅ استخدم `==` بدلاً من `>=`
- ✅ حدد الإصدارات الدقيقة
- ✅ وثق سبب اختيار كل إصدار

### **2. فصل البيئات:**
- ✅ بيئة تطوير منفصلة
- ✅ بيئة إنتاج منفصلة
- ✅ بيئة اختبار منفصلة

### **3. مراجعة دورية:**
- ✅ راجع التبعيات شهرياً
- ✅ حدث التبعيات تدريجياً
- ✅ اختبر بعد كل تحديث

### **4. التوثيق:**
- ✅ وثق سبب كل تبعية
- ✅ وثق الإصدارات المطلوبة
- ✅ وثق كيفية التثبيت

## 🚀 **النتيجة:**

✅ **نظام إدارة اعتمادات قوي ومحسن!**

الآن المشروع لديه:
- 🔒 إصدارات مجمدة ومستقرة
- 🏭 فصل واضح بين بيئات التطوير والإنتاج
- 🛠️ أدوات جودة كود متقدمة
- 📦 إدارة حزم حديثة
- 🔧 أوامر إدارة سهلة
- 📚 توثيق شامل 