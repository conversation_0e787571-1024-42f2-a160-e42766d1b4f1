from PySide6.QtWidgets import QWidget, QVBoxLayout, QLineEdit, QTableWidget, QTableWidgetItem, QHeaderView

class StockAlertsTab(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("StockAlertsTab")
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout(self)
        self.search_box = QLineEdit(self)
        self.search_box.setPlaceholderText(self.tr("بحث في التنبيهات..."))
        self.search_box.textChanged.connect(self.filter_table)
        layout.addWidget(self.search_box)
        self.table = QTableWidget(self)
        self.table.setColumnCount(4)
        self.table.setHorizontalHeaderLabels([
            self.tr("النوع"), self.tr("المنتج/الدفعة"), self.tr("الوصف"), self.tr("التاريخ")
        ])
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        layout.addWidget(self.table)
        self.setLayout(layout)
        self.alerts = [
            {"type": "انخفاض كمية", "item": "منتج 1", "desc": "الكمية أقل من الحد الأدنى", "date": "2024-06-01"},
            {"type": "انتهاء صلاحية", "item": "دفعة 123", "desc": "ستنتهي الصلاحية خلال 3 أيام", "date": "2024-06-05"},
        ]
        self.refresh_table()

    def refresh_table(self):
        self.table.setRowCount(0)
        for alert in self.alerts:
            row = self.table.rowCount()
            self.table.insertRow(row)
            self.table.setItem(row, 0, QTableWidgetItem(alert["type"]))
            self.table.setItem(row, 1, QTableWidgetItem(alert["item"]))
            self.table.setItem(row, 2, QTableWidgetItem(alert["desc"]))
            self.table.setItem(row, 3, QTableWidgetItem(alert["date"]))

    def filter_table(self, text):
        for row in range(self.table.rowCount()):
            match = False
            for col in range(4):
                item = self.table.item(row, col)
                if item and text in item.text():
                    match = True
            self.table.setRowHidden(row, not match) 