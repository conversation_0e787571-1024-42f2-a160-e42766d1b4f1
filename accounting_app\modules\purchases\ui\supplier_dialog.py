"""
نافذة حوار إضافة/تعديل المورد - واجهة شاملة لإدخال جميع معلومات المورد
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTabWidget, QWidget, QFormLayout,
    QLineEdit, QComboBox, QTextEdit, QSpinBox, QDoubleSpinBox, QCheckBox,
    QDateEdit, QPushButton, QDialogButtonBox, QGroupBox, QTableWidget,
    QTableWidgetItem, QHeaderView, QMessageBox, QLabel, QFrame,
    QScrollArea, QGridLayout, QListWidget, QListWidgetItem
)
from PySide6.QtCore import Qt, QDate, Signal
from PySide6.QtGui import QFont, QIcon
from datetime import datetime, date
import logging

# استيراد النماذج
from ..models.supplier import (
    Supplier, SupplierContact, SupplierAddress, SupplierBankAccount,
    SupplierDocument, SupplierRating, SupplierStatus, SupplierType, PaymentTerms
)

# استيراد النوافذ الفرعية
from .supplier_dialogs import ContactDialog, AddressDialog, BankAccountDialog, DocumentDialog


class SupplierDialog(QDialog):
    """نافذة حوار إضافة/تعديل المورد"""

    def tr(self, text):
        """دالة ترجمة بسيطة"""
        return text

    def __init__(self, parent=None, supplier_data=None):
        super().__init__(parent)
        self.supplier_data = supplier_data
        self.is_edit_mode = supplier_data is not None
        
        self.setWindowTitle(
            self.tr("تعديل المورد") if self.is_edit_mode else self.tr("إضافة مورد جديد")
        )
        self.setMinimumSize(800, 600)
        self.setModal(True)
        
        self.init_ui()
        
        if self.is_edit_mode:
            self.load_supplier_data()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)
        
        # التبويبات الرئيسية
        self.tab_widget = QTabWidget()
        
        # تبويب المعلومات الأساسية
        basic_tab = self.create_basic_info_tab()
        self.tab_widget.addTab(basic_tab, self.tr("المعلومات الأساسية"))
        
        # تبويب جهات الاتصال
        contacts_tab = self.create_contacts_tab()
        self.tab_widget.addTab(contacts_tab, self.tr("جهات الاتصال"))
        
        # تبويب العناوين
        addresses_tab = self.create_addresses_tab()
        self.tab_widget.addTab(addresses_tab, self.tr("العناوين"))
        
        # تبويب المعلومات المالية
        financial_tab = self.create_financial_tab()
        self.tab_widget.addTab(financial_tab, self.tr("المعلومات المالية"))
        
        # تبويب الوثائق
        documents_tab = self.create_documents_tab()
        self.tab_widget.addTab(documents_tab, self.tr("الوثائق"))
        
        layout.addWidget(self.tab_widget)
        
        # أزرار الحفظ والإلغاء
        button_box = QDialogButtonBox(
            QDialogButtonBox.Save | QDialogButtonBox.Cancel
        )
        button_box.accepted.connect(self.save_supplier)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
    
    def create_basic_info_tab(self):
        """إنشاء تبويب المعلومات الأساسية"""
        tab = QWidget()
        scroll = QScrollArea(tab)
        scroll.setWidgetResizable(True)
        
        container = QWidget()
        layout = QVBoxLayout(container)
        
        # المعلومات الأساسية
        basic_group = QGroupBox(self.tr("المعلومات الأساسية"))
        basic_layout = QFormLayout(basic_group)
        
        self.supplier_code_edit = QLineEdit()
        self.supplier_code_edit.setPlaceholderText(self.tr("سيتم إنشاؤه تلقائياً"))
        
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText(self.tr("اسم المورد باللغة العربية"))
        
        self.name_english_edit = QLineEdit()
        self.name_english_edit.setPlaceholderText(self.tr("اسم المورد باللغة الإنجليزية"))
        
        self.supplier_type_combo = QComboBox()
        for supplier_type in SupplierType:
            self.supplier_type_combo.addItem(supplier_type.value, supplier_type)
        
        self.status_combo = QComboBox()
        for status in SupplierStatus:
            self.status_combo.addItem(status.value, status)
        
        basic_layout.addRow(self.tr("رمز المورد:"), self.supplier_code_edit)
        basic_layout.addRow(self.tr("اسم المورد:"), self.name_edit)
        basic_layout.addRow(self.tr("الاسم بالإنجليزية:"), self.name_english_edit)
        basic_layout.addRow(self.tr("نوع المورد:"), self.supplier_type_combo)
        basic_layout.addRow(self.tr("الحالة:"), self.status_combo)
        
        layout.addWidget(basic_group)
        
        # المعلومات الضريبية
        tax_group = QGroupBox(self.tr("المعلومات الضريبية"))
        tax_layout = QFormLayout(tax_group)
        
        self.tax_number_edit = QLineEdit()
        self.commercial_registration_edit = QLineEdit()
        self.is_tax_exempt_check = QCheckBox(self.tr("معفي من الضريبة"))
        self.tax_exemption_reason_edit = QLineEdit()
        
        tax_layout.addRow(self.tr("الرقم الضريبي:"), self.tax_number_edit)
        tax_layout.addRow(self.tr("السجل التجاري:"), self.commercial_registration_edit)
        tax_layout.addRow("", self.is_tax_exempt_check)
        tax_layout.addRow(self.tr("سبب الإعفاء:"), self.tax_exemption_reason_edit)
        
        layout.addWidget(tax_group)
        
        # معلومات إضافية
        additional_group = QGroupBox(self.tr("معلومات إضافية"))
        additional_layout = QFormLayout(additional_group)
        
        self.categories_edit = QLineEdit()
        self.categories_edit.setPlaceholderText(self.tr("الفئات مفصولة بفاصلة"))
        
        self.lead_time_spin = QSpinBox()
        self.lead_time_spin.setRange(1, 365)
        self.lead_time_spin.setValue(7)
        self.lead_time_spin.setSuffix(self.tr(" يوم"))
        
        self.minimum_order_spin = QDoubleSpinBox()
        self.minimum_order_spin.setRange(0, 1000000)
        self.minimum_order_spin.setDecimals(2)
        self.minimum_order_spin.setSuffix(self.tr(" ريال"))
        
        self.discount_percentage_spin = QDoubleSpinBox()
        self.discount_percentage_spin.setRange(0, 100)
        self.discount_percentage_spin.setDecimals(2)
        self.discount_percentage_spin.setSuffix("%")
        
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(100)
        
        additional_layout.addRow(self.tr("الفئات:"), self.categories_edit)
        additional_layout.addRow(self.tr("مدة التسليم:"), self.lead_time_spin)
        additional_layout.addRow(self.tr("الحد الأدنى للطلب:"), self.minimum_order_spin)
        additional_layout.addRow(self.tr("نسبة الخصم:"), self.discount_percentage_spin)
        additional_layout.addRow(self.tr("ملاحظات:"), self.notes_edit)
        
        layout.addWidget(additional_group)
        
        scroll.setWidget(container)
        
        tab_layout = QVBoxLayout(tab)
        tab_layout.addWidget(scroll)
        
        return tab
    
    def create_contacts_tab(self):
        """إنشاء تبويب جهات الاتصال"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # أزرار إدارة جهات الاتصال
        buttons_layout = QHBoxLayout()
        add_contact_btn = QPushButton(self.tr("إضافة جهة اتصال"))
        add_contact_btn.clicked.connect(self.add_contact)
        edit_contact_btn = QPushButton(self.tr("تعديل"))
        delete_contact_btn = QPushButton(self.tr("حذف"))
        
        buttons_layout.addWidget(add_contact_btn)
        buttons_layout.addWidget(edit_contact_btn)
        buttons_layout.addWidget(delete_contact_btn)
        buttons_layout.addStretch()
        
        layout.addLayout(buttons_layout)
        
        # جدول جهات الاتصال
        self.contacts_table = QTableWidget()
        self.contacts_table.setColumnCount(6)
        self.contacts_table.setHorizontalHeaderLabels([
            self.tr("الاسم"),
            self.tr("المنصب"),
            self.tr("الهاتف"),
            self.tr("الجوال"),
            self.tr("البريد الإلكتروني"),
            self.tr("رئيسي")
        ])
        
        header = self.contacts_table.horizontalHeader()
        header.setStretchLastSection(True)
        
        layout.addWidget(self.contacts_table)
        
        return tab
    
    def create_addresses_tab(self):
        """إنشاء تبويب العناوين"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # أزرار إدارة العناوين
        buttons_layout = QHBoxLayout()
        add_address_btn = QPushButton(self.tr("إضافة عنوان"))
        add_address_btn.clicked.connect(self.add_address)
        edit_address_btn = QPushButton(self.tr("تعديل"))
        delete_address_btn = QPushButton(self.tr("حذف"))
        
        buttons_layout.addWidget(add_address_btn)
        buttons_layout.addWidget(edit_address_btn)
        buttons_layout.addWidget(delete_address_btn)
        buttons_layout.addStretch()
        
        layout.addLayout(buttons_layout)
        
        # جدول العناوين
        self.addresses_table = QTableWidget()
        self.addresses_table.setColumnCount(7)
        self.addresses_table.setHorizontalHeaderLabels([
            self.tr("النوع"),
            self.tr("الشارع"),
            self.tr("المدينة"),
            self.tr("المنطقة"),
            self.tr("الرمز البريدي"),
            self.tr("الدولة"),
            self.tr("رئيسي")
        ])
        
        header = self.addresses_table.horizontalHeader()
        header.setStretchLastSection(True)
        
        layout.addWidget(self.addresses_table)
        
        return tab
    
    def create_financial_tab(self):
        """إنشاء تبويب المعلومات المالية"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # معلومات الدفع
        payment_group = QGroupBox(self.tr("معلومات الدفع"))
        payment_layout = QFormLayout(payment_group)
        
        self.payment_terms_combo = QComboBox()
        for terms in PaymentTerms:
            self.payment_terms_combo.addItem(terms.value, terms)
        
        self.credit_limit_spin = QDoubleSpinBox()
        self.credit_limit_spin.setRange(0, 10000000)
        self.credit_limit_spin.setDecimals(2)
        self.credit_limit_spin.setSuffix(self.tr(" ريال"))
        
        self.currency_combo = QComboBox()
        self.currency_combo.addItems(["SAR", "USD", "EUR", "GBP"])
        
        payment_layout.addRow(self.tr("شروط الدفع:"), self.payment_terms_combo)
        payment_layout.addRow(self.tr("حد الائتمان:"), self.credit_limit_spin)
        payment_layout.addRow(self.tr("العملة:"), self.currency_combo)
        
        layout.addWidget(payment_group)
        
        # الحسابات البنكية
        bank_group = QGroupBox(self.tr("الحسابات البنكية"))
        bank_layout = QVBoxLayout(bank_group)
        
        # أزرار إدارة الحسابات البنكية
        bank_buttons_layout = QHBoxLayout()
        add_bank_btn = QPushButton(self.tr("إضافة حساب بنكي"))
        add_bank_btn.clicked.connect(self.add_bank_account)
        edit_bank_btn = QPushButton(self.tr("تعديل"))
        delete_bank_btn = QPushButton(self.tr("حذف"))
        
        bank_buttons_layout.addWidget(add_bank_btn)
        bank_buttons_layout.addWidget(edit_bank_btn)
        bank_buttons_layout.addWidget(delete_bank_btn)
        bank_buttons_layout.addStretch()
        
        bank_layout.addLayout(bank_buttons_layout)
        
        # جدول الحسابات البنكية
        self.bank_accounts_table = QTableWidget()
        self.bank_accounts_table.setColumnCount(5)
        self.bank_accounts_table.setHorizontalHeaderLabels([
            self.tr("اسم البنك"),
            self.tr("رقم الحساب"),
            self.tr("الآيبان"),
            self.tr("اسم صاحب الحساب"),
            self.tr("رئيسي")
        ])
        
        bank_layout.addWidget(self.bank_accounts_table)
        layout.addWidget(bank_group)
        
        return tab
    
    def create_documents_tab(self):
        """إنشاء تبويب الوثائق"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # أزرار إدارة الوثائق
        buttons_layout = QHBoxLayout()
        add_document_btn = QPushButton(self.tr("إضافة وثيقة"))
        add_document_btn.clicked.connect(self.add_document)
        edit_document_btn = QPushButton(self.tr("تعديل"))
        delete_document_btn = QPushButton(self.tr("حذف"))
        
        buttons_layout.addWidget(add_document_btn)
        buttons_layout.addWidget(edit_document_btn)
        buttons_layout.addWidget(delete_document_btn)
        buttons_layout.addStretch()
        
        layout.addLayout(buttons_layout)
        
        # جدول الوثائق
        self.documents_table = QTableWidget()
        self.documents_table.setColumnCount(5)
        self.documents_table.setHorizontalHeaderLabels([
            self.tr("نوع الوثيقة"),
            self.tr("رقم الوثيقة"),
            self.tr("تاريخ الإصدار"),
            self.tr("تاريخ الانتهاء"),
            self.tr("ملاحظات")
        ])
        
        header = self.documents_table.horizontalHeader()
        header.setStretchLastSection(True)
        
        layout.addWidget(self.documents_table)
        
        return tab

    def load_supplier_data(self):
        """تحميل بيانات المورد للتعديل"""
        if not self.supplier_data:
            return

        # تحميل المعلومات الأساسية
        self.supplier_code_edit.setText(self.supplier_data.get('supplier_code', ''))
        self.name_edit.setText(self.supplier_data.get('name', ''))
        self.name_english_edit.setText(self.supplier_data.get('name_english', ''))

        # تحديد نوع المورد
        supplier_type = self.supplier_data.get('supplier_type')
        if supplier_type:
            index = self.supplier_type_combo.findData(supplier_type)
            if index >= 0:
                self.supplier_type_combo.setCurrentIndex(index)

        # تحديد حالة المورد
        status = self.supplier_data.get('status')
        if status:
            index = self.status_combo.findData(status)
            if index >= 0:
                self.status_combo.setCurrentIndex(index)

    def add_contact(self):
        """إضافة جهة اتصال جديدة"""
        dialog = ContactDialog(self)
        if dialog.exec() == QDialog.Accepted:
            contact_data = dialog.get_contact_data()
            self.add_contact_to_table(contact_data)

    def add_contact_to_table(self, contact_data):
        """إضافة جهة اتصال إلى الجدول"""
        row = self.contacts_table.rowCount()
        self.contacts_table.insertRow(row)

        self.contacts_table.setItem(row, 0, QTableWidgetItem(contact_data.get('name', '')))
        self.contacts_table.setItem(row, 1, QTableWidgetItem(contact_data.get('position', '')))
        self.contacts_table.setItem(row, 2, QTableWidgetItem(contact_data.get('phone', '')))
        self.contacts_table.setItem(row, 3, QTableWidgetItem(contact_data.get('mobile', '')))
        self.contacts_table.setItem(row, 4, QTableWidgetItem(contact_data.get('email', '')))

        # خانة اختيار للجهة الرئيسية
        primary_check = QCheckBox()
        primary_check.setChecked(contact_data.get('is_primary', False))
        self.contacts_table.setCellWidget(row, 5, primary_check)

    def add_address(self):
        """إضافة عنوان جديد"""
        dialog = AddressDialog(self)
        if dialog.exec() == QDialog.Accepted:
            address_data = dialog.get_address_data()
            self.add_address_to_table(address_data)

    def add_address_to_table(self, address_data):
        """إضافة عنوان إلى الجدول"""
        row = self.addresses_table.rowCount()
        self.addresses_table.insertRow(row)

        self.addresses_table.setItem(row, 0, QTableWidgetItem(address_data.get('type', '')))
        self.addresses_table.setItem(row, 1, QTableWidgetItem(address_data.get('street', '')))
        self.addresses_table.setItem(row, 2, QTableWidgetItem(address_data.get('city', '')))
        self.addresses_table.setItem(row, 3, QTableWidgetItem(address_data.get('state', '')))
        self.addresses_table.setItem(row, 4, QTableWidgetItem(address_data.get('postal_code', '')))
        self.addresses_table.setItem(row, 5, QTableWidgetItem(address_data.get('country', '')))

        # خانة اختيار للعنوان الرئيسي
        primary_check = QCheckBox()
        primary_check.setChecked(address_data.get('is_primary', False))
        self.addresses_table.setCellWidget(row, 6, primary_check)

    def add_bank_account(self):
        """إضافة حساب بنكي جديد"""
        dialog = BankAccountDialog(self)
        if dialog.exec() == QDialog.Accepted:
            bank_data = dialog.get_bank_data()
            self.add_bank_account_to_table(bank_data)

    def add_bank_account_to_table(self, bank_data):
        """إضافة حساب بنكي إلى الجدول"""
        row = self.bank_accounts_table.rowCount()
        self.bank_accounts_table.insertRow(row)

        self.bank_accounts_table.setItem(row, 0, QTableWidgetItem(bank_data.get('bank_name', '')))
        self.bank_accounts_table.setItem(row, 1, QTableWidgetItem(bank_data.get('account_number', '')))
        self.bank_accounts_table.setItem(row, 2, QTableWidgetItem(bank_data.get('iban', '')))
        self.bank_accounts_table.setItem(row, 3, QTableWidgetItem(bank_data.get('account_holder_name', '')))

        # خانة اختيار للحساب الرئيسي
        primary_check = QCheckBox()
        primary_check.setChecked(bank_data.get('is_primary', False))
        self.bank_accounts_table.setCellWidget(row, 4, primary_check)

    def add_document(self):
        """إضافة وثيقة جديدة"""
        dialog = DocumentDialog(self)
        if dialog.exec() == QDialog.Accepted:
            document_data = dialog.get_document_data()
            self.add_document_to_table(document_data)

    def add_document_to_table(self, document_data):
        """إضافة وثيقة إلى الجدول"""
        row = self.documents_table.rowCount()
        self.documents_table.insertRow(row)

        self.documents_table.setItem(row, 0, QTableWidgetItem(document_data.get('document_type', '')))
        self.documents_table.setItem(row, 1, QTableWidgetItem(document_data.get('document_number', '')))
        self.documents_table.setItem(row, 2, QTableWidgetItem(document_data.get('issue_date', '')))
        self.documents_table.setItem(row, 3, QTableWidgetItem(document_data.get('expiry_date', '')))
        self.documents_table.setItem(row, 4, QTableWidgetItem(document_data.get('notes', '')))

    def save_supplier(self):
        """حفظ بيانات المورد"""
        # التحقق من صحة البيانات
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, self.tr("تنبيه"), self.tr("يجب إدخال اسم المورد"))
            return

        # جمع البيانات
        supplier_data = self.get_supplier_data()

        # هنا سيتم حفظ البيانات في قاعدة البيانات
        QMessageBox.information(
            self,
            self.tr("نجح"),
            self.tr("تم حفظ بيانات المورد بنجاح")
        )
        self.accept()

    def get_supplier_data(self):
        """الحصول على بيانات المورد من النموذج"""
        return {
            'supplier_code': self.supplier_code_edit.text(),
            'name': self.name_edit.text(),
            'name_english': self.name_english_edit.text(),
            'supplier_type': self.supplier_type_combo.currentData(),
            'status': self.status_combo.currentData(),
            'tax_number': self.tax_number_edit.text(),
            'commercial_registration': self.commercial_registration_edit.text(),
            'is_tax_exempt': self.is_tax_exempt_check.isChecked(),
            'categories': [cat.strip() for cat in self.categories_edit.text().split(',') if cat.strip()],
            'lead_time_days': self.lead_time_spin.value(),
            'minimum_order_amount': self.minimum_order_spin.value(),
            'discount_percentage': self.discount_percentage_spin.value(),
            'payment_terms': self.payment_terms_combo.currentData(),
            'credit_limit': self.credit_limit_spin.value(),
            'currency': self.currency_combo.currentText(),
            'notes': self.notes_edit.toPlainText(),
            'rating': 4.0,  # قيمة افتراضية
            'city': 'الرياض'  # قيمة افتراضية
        }
