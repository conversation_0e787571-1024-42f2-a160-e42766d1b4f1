/* الشريط العلوي والجانبي */
#TopBar, #SideBar {
    background-color: #E5E7EB;
    color: #1F2937;
    border-radius: 16px;
}
#TopBar QLabel, #TopBar QPushButton, #TopBar QToolButton {
    background: transparent;
    color: #1F2937;
    border: none;
}
#SideBarButton {
    background: #FFFFFF;
    color: #1F2937;
    border: none;
    border-radius: 8px;
    font-size: 15px;
    padding: 6px 0;
}
#SideBarButton:hover, #SideBarButton:pressed, #SideBarButton:selected {
    background-color: #D1D5DB;
    color: #232e3c;
}
Q<PERSON>rame, QStatusBar, #Footer {
    background-color: #F3F4F6;
    color: #232e3c;
    border-radius: 8px;
}
QWidget {
    font-family: 'Cairo', 'Segoe UI', sans-serif;
    border-radius: 12px;
}
QWidget#SideBar {
    background: #e0f2fe;
}
QWidget#TopBar {
    background: #e0f2fe;
}
QWidget#MainContent, QWidget#ContentArea {
    background: #fff;
}
QTabWidget::pane {
    background: #fff;
    border-radius: 16px;
    border: 1px solid #bae6fd;
}
QPushButton {
    background: #0ea5e9;
    color: #fff;
    border-radius: 10px;
    font-weight: bold;
    padding: 10px 20px;
    font-size: 15px;
    border: none;
    margin: 4px 0;
    /* تم إزالة box-shadow واستبداله بـ QGraphicsDropShadowEffect */
}
QPushButton:hover {
    background: #0369a1;
}
QPushButton:pressed {
    background: #0c4a6e;
}
QPushButton:disabled {
    background: #b0b0b0;
    color: #eee;
}
QLabel, QLineEdit, QComboBox {
    font-size: 15px;
}
QLineEdit, QComboBox {
    background: #f0f9ff;
    border-radius: 8px;
    padding: 6px 12px;
    border: 1px solid #bae6fd;
}
QTabBar::tab {
    background: #e0f2fe;
    color: #232e3c;
    border-radius: 8px 8px 0 0;
    padding: 8px 20px;
    margin: 0 2px;
    font-weight: bold;
}
QTabBar::tab:selected {
    background: #fff;
    color: #0ea5e9;
}
QTabBar::tab:!selected {
    background: #e0f2fe;
    color: #232e3c;
}
QStatusBar, QWidget#Footer {
    background: #e0f2fe;
    color: #232e3c;
    border-top: 1px solid #bae6fd;
} 