# دليل فاحص الإعدادات - Configuration Validator Guide

## نظرة عامة

فاحص الإعدادات هو نظام متكامل للتحقق من صحة واكتمال ملفات إعدادات التطبيق باستخدام JSON Schema. هذا يضمن عدم وجود إعدادات ناقصة أو غير صالحة عند تشغيل التطبيق.

## الميزات

### ✅ تحقق شامل من الإعدادات
- **JSON Schema Validation**: استخدام مكتبة jsonschema للتحقق من صحة البنية
- **التحقق الأساسي**: تحقق احتياطي عند عدم توفر jsonschema
- **إصلاح تلقائي**: إضافة القيم المفقودة وتصحيح القيم غير الصالحة

### 🔧 إدارة الإعدادات
- **إنشاء تلقائي**: إنشاء ملفات إعدادات افتراضية صحيحة
- **تحديث آمن**: تحديث الإعدادات مع الحفاظ على التوافق
- **نسخ احتياطية**: حفظ نسخ من الإعدادات قبل التعديل

### 📊 تقارير مفصلة
- **أخطاء التحقق**: تفاصيل الأخطاء في البنية أو القيم
- **تحذيرات**: تنبيهات حول القيم المفقودة أو غير المثالية
- **إصلاحات مطبقة**: قائمة بالقيم التي تم إصلاحها تلقائياً

## بنية الإعدادات

### الأقسام المطلوبة

#### 1. معلومات التطبيق (app_info)
```json
{
  "app_info": {
    "app_name": "Smart Accounting App",
    "app_version": "1.0.0",
    "app_description": "برنامج محاسبة ذكي مع مساعد ذكي مدمج",
    "environment": "development",
    "base_path": "",
    "resources_path": "resources",
    "translations_path": "translations"
  }
}
```

#### 2. تفضيلات المستخدم (user_preferences)
```json
{
  "user_preferences": {
    "language": "ar",
    "theme": "dark",
    "date_format": "dd/mm/yyyy",
    "time_format": "24h",
    "currency": "SAR",
    "decimal_separator": ".",
    "thousands_separator": ","
  }
}
```

#### 3. إعدادات الذكاء الاصطناعي (ai_settings)
```json
{
  "ai_settings": {
    "enabled": true,
    "model": "gpt-3.5-turbo",
    "api_key": "",
    "max_tokens": 1000,
    "temperature": 0.7,
    "context_window": 10,
    "auto_suggest": true
  }
}
```

#### 4. إعدادات الإشعارات (notification_settings)
```json
{
  "notification_settings": {
    "enabled": true,
    "sound_enabled": true,
    "sound_file": "notification.wav",
    "desktop_notifications": true,
    "notification_duration": 5,
    "notification_position": "top-right",
    "auto_hide": true
  }
}
```

#### 5. إعدادات قاعدة البيانات (database_settings)
```json
{
  "database_settings": {
    "type": "sqlite",
    "host": "localhost",
    "port": 5432,
    "database_name": "accounting_app",
    "username": "",
    "password": "",
    "auto_backup": true,
    "backup_interval": 7,
    "max_backups": 10
  }
}
```

#### 6. إعدادات الأمان (security_settings)
```json
{
  "security_settings": {
    "session_timeout": 30,
    "password_min_length": 8,
    "require_special_chars": true,
    "max_login_attempts": 5,
    "lockout_duration": 15,
    "encrypt_sensitive_data": true
  }
}
```

#### 7. إعدادات واجهة المستخدم (ui_settings)
```json
{
  "ui_settings": {
    "window_size": {
      "width": 1200,
      "height": 800
    },
    "sidebar_width": 250,
    "font_size": 12,
    "font_family": "Arial",
    "show_tooltips": true,
    "show_status_bar": true,
    "auto_save_ui_state": true
  }
}
```

#### 8. إعدادات الأداء (performance_settings)
```json
{
  "performance_settings": {
    "log_level": "INFO",
    "cache_enabled": true,
    "cache_size": 100,
    "max_memory_usage": 512,
    "auto_cleanup": true,
    "cleanup_interval": 7
  }
}
```

## الاستخدام

### الاستخدام الأساسي

```python
from accounting_app.utils import validate_config_file, create_valid_config_file

# التحقق من صحة ملف الإعدادات
result = validate_config_file("config.json")
if result.is_valid:
    print("الإعدادات صحيحة")
else:
    print("هناك أخطاء في الإعدادات")
    for error in result.errors:
        print(f"  - {error}")

# إنشاء ملف إعدادات صحيح
success = create_valid_config_file("config.json")
if success:
    print("تم إنشاء ملف الإعدادات بنجاح")
```

### الاستخدام المتقدم

```python
from accounting_app.utils import ConfigValidator

# إنشاء فاحص مخصص
validator = ConfigValidator()

# إنشاء إعدادات افتراضية
default_config = validator.create_default_config()

# التحقق من صحة الإعدادات
result = validator.validate_config(default_config)

# تطبيق الإصلاحات
if result.fixed_values:
    fixed_config = validator.apply_fixes(default_config, result.fixed_values)
```

### التكامل مع AppSettings

```python
from accounting_app.core.settings import AppSettings

# تحميل الإعدادات مع التحقق التلقائي
settings = AppSettings("config.json")

# الوصول إلى الإعدادات الجديدة
ai_enabled = settings.get_config_value("ai_settings", "enabled", True)
language = settings.get_config_value("user_preferences", "language", "ar")

# تعيين إعدادات جديدة
settings.set_config_value("user_preferences", "theme", "light")

# حفظ الإعدادات مع التحقق
settings.validate_and_save()
```

## JSON Schema

### المخطط الأساسي
```json
{
  "$schema": "http://json-schema.org/draft-07/schema#",
  "title": "Smart Accounting App Configuration Schema",
  "type": "object",
  "required": [
    "app_info",
    "user_preferences", 
    "ai_settings",
    "notification_settings",
    "database_settings",
    "security_settings",
    "ui_settings",
    "performance_settings"
  ],
  "properties": {
    // ... تفاصيل كل قسم
  }
}
```

### قواعد التحقق
- **الحقول المطلوبة**: جميع الأقسام الثمانية مطلوبة
- **أنواع البيانات**: تحقق من نوع البيانات (string, integer, boolean)
- **القيم المسموحة**: قوائم محددة للقيم المسموحة
- **النطاقات**: حدود دنيا وقصوى للقيم الرقمية
- **التنسيقات**: أنماط محددة للسلاسل النصية

## معالجة الأخطاء

### أنواع الأخطاء

1. **أخطاء البنية (Schema Errors)**
   ```
   ❌ Validation error: 'app_info' is a required property
   ```

2. **أخطاء القيم (Value Errors)**
   ```
   ❌ Invalid value for user_preferences.language: invalid_lang
   ```

3. **أخطاء الملف (File Errors)**
   ```
   ❌ Config file not found: config.json
   ❌ Invalid JSON format: Expecting ',' delimiter
   ```

### الإصلاح التلقائي

```python
# مثال على الإصلاح التلقائي
invalid_config = {
    "user_preferences": {
        "language": "invalid_lang",  # قيمة غير صحيحة
        "theme": "invalid_theme"     # قيمة غير صحيحة
    }
}

result = validator.validate_config(invalid_config)
# النتيجة:
# fixed_values = {
#     "user_preferences.language": "ar",
#     "user_preferences.theme": "dark"
# }
```

## التوافق مع النظام القديم

### الإعدادات القديمة
```json
{
  "language": "ar",
  "theme": "dark"
}
```

### الإعدادات الجديدة
```json
{
  "user_preferences": {
    "language": "ar",
    "theme": "dark"
  }
}
```

### التحويل التلقائي
- النظام الجديد يدعم كلا النوعين
- التحويل التلقائي عند التحميل
- الحفاظ على التوافق مع الكود القديم

## أفضل الممارسات

### 1. التحقق عند التطوير
```python
# التحقق من الإعدادات قبل النشر
def validate_before_deploy():
    result = validate_config_file("config.json")
    if not result.is_valid:
        raise ValueError("Configuration validation failed")
```

### 2. النسخ الاحتياطية
```python
# إنشاء نسخة احتياطية قبل التعديل
import shutil
import os

def backup_config():
    if os.path.exists("config.json"):
        shutil.copy("config.json", "config.json.backup")
```

### 3. التحقق الدوري
```python
# التحقق من الإعدادات بشكل دوري
import threading
import time

def periodic_validation():
    while True:
        time.sleep(3600)  # كل ساعة
        result = validate_config_file("config.json")
        if not result.is_valid:
            logger.warning("Configuration validation failed")
```

### 4. إعدادات البيئة
```python
# إعدادات مختلفة لكل بيئة
environments = {
    "development": "config.dev.json",
    "testing": "config.test.json", 
    "production": "config.prod.json"
}

config_file = environments.get(os.getenv("ENV", "development"))
settings = AppSettings(config_file)
```

## استكشاف الأخطاء

### مشاكل شائعة

1. **مكتبة jsonschema غير متوفرة**
   ```
   WARNING: jsonschema library not available. Schema validation will be disabled.
   ```
   **الحل**: تثبيت المكتبة `pip install jsonschema`

2. **ملف الإعدادات غير موجود**
   ```
   ERROR: Config file not found: config.json
   ```
   **الحل**: إنشاء ملف افتراضي `create_valid_config_file("config.json")`

3. **قيم غير صحيحة**
   ```
   ERROR: Invalid value for user_preferences.language: invalid_lang
   ```
   **الحل**: استخدام القيم المسموحة أو السماح بالإصلاح التلقائي

### رسائل التصحيح
```python
import logging
logging.getLogger('accounting_app.utils.config_validator').setLevel(logging.DEBUG)
```

## الخلاصة

فاحص الإعدادات يوفر:

1. **حماية من الأخطاء**: منع أخطاء الإعدادات غير الصحيحة
2. **إصلاح تلقائي**: تصحيح المشاكل الشائعة تلقائياً
3. **توثيق شامل**: مخطط واضح لجميع الإعدادات
4. **توافق مع النظام القديم**: دعم الإعدادات القديمة والجديدة
5. **مرونة في التطوير**: سهولة إضافة إعدادات جديدة

هذا النظام يضمن أن التطبيق سيعمل بشكل موثوق مع إعدادات صحيحة ومكتملة. 