"""
نموذج فاتورة الشراء - يحتوي على جميع المعلومات المتعلقة بفواتير الشراء
"""

from dataclasses import dataclass, field
from typing import Optional, List, Dict, Any
from datetime import datetime, date
from enum import Enum
from decimal import Decimal


class InvoiceStatus(Enum):
    """حالة الفاتورة"""
    DRAFT = "مسودة"
    PENDING = "معلقة"
    APPROVED = "موافق عليها"
    PAID = "مدفوعة"
    PARTIALLY_PAID = "مدفوعة جزئياً"
    OVERDUE = "متأخرة"
    CANCELLED = "ملغية"
    DISPUTED = "متنازع عليها"


class InvoiceType(Enum):
    """نوع الفاتورة"""
    STANDARD = "عادية"
    CREDIT_NOTE = "إشعار دائن"
    DEBIT_NOTE = "إشعار مدين"
    PROFORMA = "أولية"
    RECURRING = "متكررة"


class PaymentStatus(Enum):
    """حالة الدفع"""
    UNPAID = "غير مدفوعة"
    PARTIALLY_PAID = "مدفوعة جزئياً"
    FULLY_PAID = "مدفوعة بالكامل"
    OVERPAID = "مدفوعة زائد"
    REFUNDED = "مسترد"


@dataclass
class PurchaseInvoiceItem:
    """عنصر في فاتورة الشراء"""
    id: Optional[int] = None
    invoice_id: Optional[int] = None
    purchase_order_item_id: Optional[int] = None
    product_id: Optional[int] = None
    product_code: str = ""
    product_name: str = ""
    product_description: str = ""
    
    # الكميات والأسعار
    quantity: Decimal = Decimal('0')
    unit_price: Decimal = Decimal('0')
    
    # الخصومات والضرائب
    discount_percentage: Decimal = Decimal('0')
    discount_amount: Decimal = Decimal('0')
    tax_percentage: Decimal = Decimal('15')  # ضريبة القيمة المضافة
    tax_amount: Decimal = Decimal('0')
    
    # المجاميع
    subtotal: Decimal = Decimal('0')  # الكمية × السعر
    total_after_discount: Decimal = Decimal('0')  # بعد الخصم
    total_with_tax: Decimal = Decimal('0')  # المجموع النهائي
    
    # معلومات إضافية
    unit_of_measure: str = "قطعة"
    batch_number: str = ""  # رقم الدفعة
    serial_number: str = ""  # الرقم التسلسلي
    expiry_date: Optional[date] = None  # تاريخ الانتهاء
    manufacturing_date: Optional[date] = None  # تاريخ التصنيع
    notes: str = ""
    
    # معلومات النظام
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    def __post_init__(self):
        """حساب المجاميع تلقائياً"""
        self.calculate_totals()
        if not self.created_at:
            self.created_at = datetime.now()
        self.updated_at = datetime.now()
    
    def calculate_totals(self):
        """حساب المجاميع"""
        self.subtotal = self.quantity * self.unit_price
        
        # حساب الخصم
        if self.discount_percentage > 0:
            self.discount_amount = self.subtotal * (self.discount_percentage / 100)
        
        self.total_after_discount = self.subtotal - self.discount_amount
        
        # حساب الضريبة
        if self.tax_percentage > 0:
            self.tax_amount = self.total_after_discount * (self.tax_percentage / 100)
        
        self.total_with_tax = self.total_after_discount + self.tax_amount


@dataclass
class PaymentRecord:
    """سجل الدفع"""
    id: Optional[int] = None
    invoice_id: Optional[int] = None
    payment_date: date = field(default_factory=date.today)
    amount: Decimal = Decimal('0')
    payment_method: str = "تحويل بنكي"
    reference_number: str = ""
    bank_name: str = ""
    check_number: str = ""
    notes: str = ""
    created_at: Optional[datetime] = None
    created_by: str = ""


@dataclass
class PurchaseInvoice:
    """نموذج فاتورة الشراء الرئيسي"""
    
    # المعلومات الأساسية
    id: Optional[int] = None
    invoice_number: str = ""
    supplier_invoice_number: str = ""  # رقم فاتورة المورد
    purchase_order_id: Optional[int] = None
    purchase_order_number: str = ""
    supplier_id: Optional[int] = None
    supplier_name: str = ""
    
    # نوع وحالة الفاتورة
    invoice_type: InvoiceType = InvoiceType.STANDARD
    status: InvoiceStatus = InvoiceStatus.DRAFT
    payment_status: PaymentStatus = PaymentStatus.UNPAID
    
    # التواريخ
    invoice_date: date = field(default_factory=date.today)
    due_date: Optional[date] = None
    received_date: Optional[date] = None  # تاريخ استلام الفاتورة
    
    # المعلومات المالية
    currency: str = "SAR"
    exchange_rate: Decimal = Decimal('1')
    
    # المجاميع
    subtotal: Decimal = Decimal('0')
    total_discount: Decimal = Decimal('0')
    total_tax: Decimal = Decimal('0')
    shipping_cost: Decimal = Decimal('0')
    handling_cost: Decimal = Decimal('0')
    other_charges: Decimal = Decimal('0')
    total_amount: Decimal = Decimal('0')
    
    # معلومات الدفع
    paid_amount: Decimal = Decimal('0')
    remaining_amount: Decimal = Decimal('0')
    payment_terms: str = "30 يوم"
    
    # العناصر والمدفوعات
    items: List[PurchaseInvoiceItem] = field(default_factory=list)
    payments: List[PaymentRecord] = field(default_factory=list)
    
    # معلومات الموافقة
    approval_status: str = "في انتظار الموافقة"
    approved_by: str = ""
    approved_at: Optional[datetime] = None
    approval_notes: str = ""
    
    # ملاحظات
    notes: str = ""
    internal_notes: str = ""
    
    # معلومات النظام
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    created_by: str = ""
    updated_by: str = ""
    
    # معلومات إضافية
    department: str = ""
    project_code: str = ""
    cost_center: str = ""
    
    def __post_init__(self):
        """تهيئة إضافية بعد إنشاء الكائن"""
        if not self.created_at:
            self.created_at = datetime.now()
        self.updated_at = datetime.now()
        
        # إنشاء رقم الفاتورة إذا لم يكن موجوداً
        if not self.invoice_number:
            self.invoice_number = self.generate_invoice_number()
        
        # حساب المجاميع
        self.calculate_totals()
        
        # تحديث تاريخ الاستحقاق
        if not self.due_date and self.payment_terms:
            self.calculate_due_date()
    
    def generate_invoice_number(self) -> str:
        """إنشاء رقم الفاتورة تلقائياً"""
        today = date.today()
        year = today.year
        month = today.month
        import random
        sequence = random.randint(1000, 9999)
        return f"PI-{year}{month:02d}-{sequence}"
    
    def calculate_due_date(self):
        """حساب تاريخ الاستحقاق"""
        if "30" in self.payment_terms:
            days = 30
        elif "60" in self.payment_terms:
            days = 60
        elif "90" in self.payment_terms:
            days = 90
        else:
            days = 30  # افتراضي
        
        from datetime import timedelta
        self.due_date = self.invoice_date + timedelta(days=days)
    
    def add_item(self, item: PurchaseInvoiceItem):
        """إضافة عنصر إلى الفاتورة"""
        item.invoice_id = self.id
        self.items.append(item)
        self.calculate_totals()
    
    def remove_item(self, item_id: int):
        """حذف عنصر من الفاتورة"""
        self.items = [item for item in self.items if item.id != item_id]
        self.calculate_totals()
    
    def calculate_totals(self):
        """حساب المجاميع الإجمالية"""
        self.subtotal = sum(item.subtotal for item in self.items)
        self.total_discount = sum(item.discount_amount for item in self.items)
        self.total_tax = sum(item.tax_amount for item in self.items)
        
        self.total_amount = (self.subtotal - self.total_discount + 
                           self.total_tax + self.shipping_cost + 
                           self.handling_cost + self.other_charges)
        
        # حساب المبلغ المتبقي
        self.paid_amount = sum(payment.amount for payment in self.payments)
        self.remaining_amount = self.total_amount - self.paid_amount
        
        # تحديث حالة الدفع
        self.update_payment_status()
    
    def update_payment_status(self):
        """تحديث حالة الدفع"""
        if self.paid_amount == 0:
            self.payment_status = PaymentStatus.UNPAID
        elif self.paid_amount >= self.total_amount:
            self.payment_status = PaymentStatus.FULLY_PAID
        else:
            self.payment_status = PaymentStatus.PARTIALLY_PAID
    
    def add_payment(self, payment: PaymentRecord):
        """إضافة دفعة"""
        payment.invoice_id = self.id
        self.payments.append(payment)
        self.calculate_totals()
    
    def is_overdue(self) -> bool:
        """التحقق من تأخر الفاتورة"""
        if not self.due_date:
            return False
        return (date.today() > self.due_date and 
                self.payment_status != PaymentStatus.FULLY_PAID)
    
    def get_days_overdue(self) -> int:
        """الحصول على عدد أيام التأخير"""
        if not self.is_overdue():
            return 0
        return (date.today() - self.due_date).days
    
    def can_be_cancelled(self) -> bool:
        """التحقق من إمكانية إلغاء الفاتورة"""
        return (self.status in [InvoiceStatus.DRAFT, InvoiceStatus.PENDING] and
                self.payment_status == PaymentStatus.UNPAID)
    
    def approve(self, approved_by: str, notes: str = ""):
        """الموافقة على الفاتورة"""
        self.status = InvoiceStatus.APPROVED
        self.approval_status = "موافق عليها"
        self.approved_by = approved_by
        self.approved_at = datetime.now()
        self.approval_notes = notes
        self.updated_at = datetime.now()
    
    def cancel(self, reason: str = ""):
        """إلغاء الفاتورة"""
        if self.can_be_cancelled():
            self.status = InvoiceStatus.CANCELLED
            self.internal_notes += f"\nتم الإلغاء: {reason}" if reason else "\nتم الإلغاء"
            self.updated_at = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """تحويل الكائن إلى قاموس"""
        return {
            'id': self.id,
            'invoice_number': self.invoice_number,
            'supplier_invoice_number': self.supplier_invoice_number,
            'purchase_order_id': self.purchase_order_id,
            'supplier_id': self.supplier_id,
            'supplier_name': self.supplier_name,
            'invoice_type': self.invoice_type.value,
            'status': self.status.value,
            'payment_status': self.payment_status.value,
            'invoice_date': self.invoice_date.isoformat(),
            'due_date': self.due_date.isoformat() if self.due_date else None,
            'currency': self.currency,
            'subtotal': float(self.subtotal),
            'total_discount': float(self.total_discount),
            'total_tax': float(self.total_tax),
            'total_amount': float(self.total_amount),
            'paid_amount': float(self.paid_amount),
            'remaining_amount': float(self.remaining_amount),
            'payment_terms': self.payment_terms,
            'approval_status': self.approval_status,
            'notes': self.notes,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'created_by': self.created_by,
            'updated_by': self.updated_by
        }
