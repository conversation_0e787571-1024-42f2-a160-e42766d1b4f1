"""
Translations package for Smart Accounting App
حزمة الترجمة لبرنامج المحاسبة الذكي
"""

import os
from pathlib import Path

# الحصول على مسار مجلد الترجمة
TRANSLATIONS_DIR = Path(__file__).parent

# اللغات المدعومة
SUPPORTED_LANGUAGES = {
    'ar': {
        'name': 'العربية',
        'native_name': 'العربية',
        'direction': 'rtl',
        'ts_file': 'ar.ts',
        'qm_file': 'ar.qm'
    },
    'en': {
        'name': 'English',
        'native_name': 'English',
        'direction': 'ltr',
        'ts_file': 'en.ts',
        'qm_file': 'en.qm'
    },
    'fr': {
        'name': 'Français',
        'native_name': 'Français',
        'direction': 'ltr',
        'ts_file': 'fr.ts',
        'qm_file': 'fr.qm'
    }
}

def get_translation_path(lang_code: str, file_type: str = 'qm') -> str:
    """الحصول على مسار ملف الترجمة"""
    if lang_code not in SUPPORTED_LANGUAGES:
        raise ValueError(f"Unsupported language: {lang_code}")
    
    if file_type == 'qm':
        filename = SUPPORTED_LANGUAGES[lang_code]['qm_file']
    elif file_type == 'ts':
        filename = SUPPORTED_LANGUAGES[lang_code]['ts_file']
    else:
        raise ValueError(f"Unsupported file type: {file_type}")
    
    return str(TRANSLATIONS_DIR / filename)

def list_translations() -> dict:
    """قائمة بجميع ملفات الترجمة المتاحة"""
    translations = {}
    for lang_code, lang_info in SUPPORTED_LANGUAGES.items():
        translations[lang_code] = {
            'name': lang_info['name'],
            'native_name': lang_info['native_name'],
            'direction': lang_info['direction'],
            'ts_exists': (TRANSLATIONS_DIR / lang_info['ts_file']).exists(),
            'qm_exists': (TRANSLATIONS_DIR / lang_info['qm_file']).exists()
        }
    return translations

def translation_exists(lang_code: str, file_type: str = 'qm') -> bool:
    """التحقق من وجود ملف ترجمة"""
    try:
        path = get_translation_path(lang_code, file_type)
        return os.path.exists(path)
    except ValueError:
        return False

def get_language_info(lang_code: str) -> dict:
    """الحصول على معلومات اللغة"""
    if lang_code not in SUPPORTED_LANGUAGES:
        raise ValueError(f"Unsupported language: {lang_code}")
    
    return SUPPORTED_LANGUAGES[lang_code].copy() 