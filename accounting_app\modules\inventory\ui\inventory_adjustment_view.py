from PySide6.QtWidgets import QWidget, QVBoxLayout, QLineEdit, QTableWidget, QTableWidgetItem, QHeaderView, QPushButton, QDialog, QFormLayout, QDialogButtonBox

class InventoryAdjustmentDialog(QDialog):
    def __init__(self, parent=None, adjustment=None):
        super().__init__(parent)
        self.setWindowTitle(self.tr("إضافة / تعديل جرد"))
        self.adjustment = adjustment
        self.init_ui()

    def init_ui(self):
        layout = QFormLayout(self)
        self.product_edit = QLineEdit(self)
        self.counted_qty_edit = QLineEdit(self)
        self.system_qty_edit = QLineEdit(self)
        self.reason_edit = QLineEdit(self)
        self.warehouse_edit = QLineEdit(self)
        layout.addRow(self.tr("المنتج:"), self.product_edit)
        layout.addRow(self.tr("الكمية الفعلية:"), self.counted_qty_edit)
        layout.addRow(self.tr("كمية النظام:"), self.system_qty_edit)
        layout.addRow(self.tr("السبب:"), self.reason_edit)
        layout.addRow(self.tr("المخزن:"), self.warehouse_edit)
        if self.adjustment:
            self.product_edit.setText(self.adjustment["product"])
            self.counted_qty_edit.setText(str(self.adjustment["counted_qty"]))
            self.system_qty_edit.setText(str(self.adjustment["system_qty"]))
            self.reason_edit.setText(self.adjustment["reason"])
            self.warehouse_edit.setText(self.adjustment["warehouse"])
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel, self)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addRow(buttons)

    def get_data(self):
        return {
            "product": self.product_edit.text(),
            "counted_qty": self.counted_qty_edit.text(),
            "system_qty": self.system_qty_edit.text(),
            "reason": self.reason_edit.text(),
            "warehouse": self.warehouse_edit.text(),
        }

class InventoryAdjustmentTab(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("InventoryAdjustmentTab")
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout(self)
        self.search_box = QLineEdit(self)
        self.search_box.setPlaceholderText(self.tr("بحث في الجرد..."))
        self.search_box.textChanged.connect(self.filter_table)
        layout.addWidget(self.search_box)
        self.table = QTableWidget(self)
        self.table.setColumnCount(7)
        self.table.setHorizontalHeaderLabels([
            self.tr("المنتج"), self.tr("الكمية الفعلية"), self.tr("كمية النظام"), self.tr("الفرق"), self.tr("السبب"), self.tr("المخزن"), self.tr("إجراءات")
        ])
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        layout.addWidget(self.table)
        self.add_btn = QPushButton(self.tr("إضافة جرد جديد"), self)
        self.add_btn.clicked.connect(self.add_adjustment)
        layout.addWidget(self.add_btn)
        self.setLayout(layout)
        self.adjustments = [
            {"product": "منتج 1", "counted_qty": 95, "system_qty": 100, "reason": "نقص عد", "warehouse": "المخزن الرئيسي"},
            {"product": "منتج 2", "counted_qty": 50, "system_qty": 50, "reason": "مطابقة", "warehouse": "مخزن فرعي"},
        ]
        self.refresh_table()

    def refresh_table(self):
        self.table.setRowCount(0)
        for adj in self.adjustments:
            row = self.table.rowCount()
            self.table.insertRow(row)
            self.table.setItem(row, 0, QTableWidgetItem(adj["product"]))
            self.table.setItem(row, 1, QTableWidgetItem(str(adj["counted_qty"])))
            self.table.setItem(row, 2, QTableWidgetItem(str(adj["system_qty"])))
            diff = float(adj["counted_qty"]) - float(adj["system_qty"])
            self.table.setItem(row, 3, QTableWidgetItem(str(diff)))
            self.table.setItem(row, 4, QTableWidgetItem(adj["reason"]))
            self.table.setItem(row, 5, QTableWidgetItem(adj["warehouse"]))
            edit_btn = QPushButton(self.tr("تعديل"))
            edit_btn.clicked.connect(lambda _, r=row: self.edit_adjustment(r))
            self.table.setCellWidget(row, 6, edit_btn)

    def filter_table(self, text):
        for row in range(self.table.rowCount()):
            match = False
            for col in range(6):
                item = self.table.item(row, col)
                if item and text in item.text():
                    match = True
            self.table.setRowHidden(row, not match)

    def add_adjustment(self):
        dialog = InventoryAdjustmentDialog(self)
        if dialog.exec() == QDialog.Accepted:
            data = dialog.get_data()
            self.adjustments.append({
                "product": data["product"],
                "counted_qty": float(data["counted_qty"]),
                "system_qty": float(data["system_qty"]),
                "reason": data["reason"],
                "warehouse": data["warehouse"],
            })
            self.refresh_table()

    def edit_adjustment(self, row):
        adj = self.adjustments[row]
        dialog = InventoryAdjustmentDialog(self, adj)
        if dialog.exec() == QDialog.Accepted:
            data = dialog.get_data()
            self.adjustments[row] = {
                "product": data["product"],
                "counted_qty": float(data["counted_qty"]),
                "system_qty": float(data["system_qty"]),
                "reason": data["reason"],
                "warehouse": data["warehouse"],
            }
            self.refresh_table() 