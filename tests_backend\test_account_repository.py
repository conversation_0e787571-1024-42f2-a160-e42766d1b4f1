import sys
import os
import pytest
import uuid
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from database.models import Base, Account
from database.repositories.account_repository import AccountRepository

@pytest.fixture(scope="function")
def db_session():
    engine = create_engine('sqlite:///:memory:')
    Base.metadata.create_all(engine)
    Session = sessionmaker(bind=engine)
    session = Session()
    yield session
    session.close()

def test_create_and_get_account(db_session):
    company_id = uuid.uuid4()
    repo = AccountRepository(db_session)
    account = Account(company_id=company_id, name="Cash", type="Asset", balance=1000)
    repo.create(account)
    fetched = repo.get_by_id(account.account_id)
    assert fetched is not None
    assert fetched.name == "Cash"
    assert fetched.type == "Asset"
    assert float(fetched.balance) == 1000 