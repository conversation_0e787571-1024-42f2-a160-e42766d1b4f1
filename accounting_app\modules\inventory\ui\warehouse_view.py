from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QLineEdit, QTableWidget, QTableWidgetItem, QHeaderView, QPushButton, QDialog, QFormLayout, QDialogButtonBox, QHBoxLayout, QComboBox, QMessageBox, QLabel, QCheckBox, QTextEdit, QListWidget, QListWidgetItem, QFileDialog, QGroupBox, QScrollArea, QToolButton, QSizePolicy, QFrame
)
from PySide6.QtCore import Qt, QSize
from datetime import datetime
from PySide6.QtGui import QPixmap, QIcon
from functools import partial

class CollapsibleGroupBox(QWidget):
    def __init__(self, title, parent=None, expanded=False):
        super().__init__(parent)
        self.toggle_button = QToolButton(text=title, checkable=True, checked=expanded)
        self.toggle_button.setStyleSheet("QToolButton { border: none; font-weight: bold; font-size: 15px; text-align: right; }")
        self.toggle_button.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)
        self.toggle_button.setArrowType(Qt.DownArrow if expanded else Qt.RightArrow)
        self.toggle_button.clicked.connect(self.on_toggle)
        self.content_area = QWidget()
        self.content_area.setMaximumHeight(0 if not expanded else 16777215)
        self.content_area.setSizePolicy(self.sizePolicy())
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.addWidget(self.toggle_button)
        layout.addWidget(self.content_area)
        self.setLayout(layout)
    def setContentLayout(self, content_layout):
        self.content_area.setLayout(content_layout)
        self.content_area.setMaximumHeight(content_layout.sizeHint().height() if self.toggle_button.isChecked() else 0)
    def on_toggle(self):
        checked = self.toggle_button.isChecked()
        self.toggle_button.setArrowType(Qt.DownArrow if checked else Qt.RightArrow)
        content_height = self.content_area.layout().sizeHint().height()
        start_value = self.content_area.maximumHeight()
        end_value = content_height if checked else 0
        self.content_area.setMaximumHeight(end_value)

class WarehouseDialog(QDialog):
    def __init__(self, parent=None, warehouse=None, managers=None):
        super().__init__(parent)
        self.setWindowTitle(self.tr("إضافة / تعديل مستودع"))
        self.setMinimumWidth(480)
        self.setSizePolicy(QWidget.sizePolicy(self))
        self.warehouse = warehouse
        self.managers = managers or ["أحمد", "سارة"]
        self.init_ui()
        self.setFixedSize(520, 700)
        if self.warehouse:
            self.set_warehouse_data(self.warehouse)

    def _create_arrow_icon(self, direction="right", color="#2563eb"):
        from PySide6.QtGui import QPixmap, QPainter, QColor, QPolygon
        from PySide6.QtCore import QPoint, Qt
        pixmap = QPixmap(24, 24)
        pixmap.fill(Qt.transparent)
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        painter.setPen(Qt.NoPen)
        painter.setBrush(QColor(color))
        points = {
            "right": [QPoint(8, 6), QPoint(16, 12), QPoint(8, 18)],
            "down": [QPoint(6, 8), QPoint(12, 16), QPoint(18, 8)]
        }
        painter.drawPolygon(QPolygon(points[direction]))
        painter.end()
        from PySide6.QtGui import QIcon
        return QIcon(pixmap)

    def init_ui(self):
        from PySide6.QtWidgets import QSizePolicy
        self.setLayoutDirection(Qt.RightToLeft)
        scroll = QScrollArea(self)
        scroll.setWidgetResizable(True)
        container = QWidget()
        main_layout = QVBoxLayout(container)
        main_layout.setAlignment(Qt.AlignTop)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(12)
        arrow_color = "#2563eb"
        # --- الحقول الأساسية (اسم المستودع + زر توسيع) ---
        basic_row = QHBoxLayout()
        self.name_edit = QLineEdit(self)
        self.basic_expand_btn = QPushButton()
        self.basic_expand_btn.setFixedWidth(28)
        self.basic_expand_btn.setCheckable(True)
        self.basic_expand_btn.setChecked(False)
        self.basic_expand_btn.setIcon(self._create_arrow_icon("right", arrow_color))
        basic_row.addWidget(QLabel(self.tr("اسم المستودع:")))
        basic_row.addWidget(self.name_edit)
        basic_row.addWidget(self.basic_expand_btn)
        main_layout.addLayout(basic_row)
        # --- زر تفعيل تكوين موقع التخزين أسفل اسم المستودع ---
        self.storage_enable_chk = QCheckBox(self.tr("تفعيل تكوين موقع التخزين"), self)
        main_layout.addWidget(self.storage_enable_chk)
        # --- الحقول الثانوية للحقول الأساسية ---
        self.basic_details_widget = QWidget()
        basic_details_layout = QFormLayout(self.basic_details_widget)
        self.manager_combo = QComboBox(self)
        self.manager_combo.addItems(self.managers)
        self.manager_combo.setEditable(True)
        basic_details_layout.addRow(self.tr("مسؤول المستودع:"), self.manager_combo)
        self.contact_edit = QLineEdit(self)
        basic_details_layout.addRow(self.tr("جهة الاتصال:"), self.contact_edit)
        img_layout = QHBoxLayout()
        self.img_btn = QPushButton(self.tr("🖼️ إضافة صورة"), self)
        self.img_btn.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        self.img_btn.clicked.connect(self.select_main_image)
        self.img_thumb = QLabel(self)
        self.img_thumb.setFixedSize(48, 48)
        self.img_thumb.setStyleSheet("border: 1px solid #ccc; background: #fafbfc;")
        img_layout.addWidget(self.img_btn)
        img_layout.addWidget(self.img_thumb)
        img_layout.addStretch()
        basic_details_layout.addRow(self.tr("صورة المستودع:"), img_layout)
        self.desc_edit = QTextEdit(self)
        self.desc_edit.setFixedHeight(50)
        basic_details_layout.addRow(self.tr("العنوان / الوصف العام:"), self.desc_edit)
        self.basic_details_widget.setVisible(False)
        main_layout.addWidget(self.basic_details_widget)
        self.basic_expand_btn.toggled.connect(lambda checked: self.basic_details_widget.setVisible(checked))
        self.basic_expand_btn.toggled.connect(lambda checked: self.basic_expand_btn.setIcon(self._create_arrow_icon("down", arrow_color) if checked else self._create_arrow_icon("right", arrow_color)))
        # --- تكوين موقع التخزين (الممر + زر توسيع) ---
        storage_row = QHBoxLayout()
        self.aisle_edit = QLineEdit(self)
        self.storage_expand_btn = QPushButton()
        self.storage_expand_btn.setFixedWidth(28)
        self.storage_expand_btn.setCheckable(True)
        self.storage_expand_btn.setChecked(False)
        self.storage_expand_btn.setIcon(self._create_arrow_icon("right", arrow_color))
        storage_row.addWidget(QLabel(self.tr("الممر:")))
        storage_row.addWidget(self.aisle_edit)
        storage_row.addWidget(self.storage_expand_btn)
        main_layout.addLayout(storage_row)
        # --- زر تفعيل السعة والتنبيهات تحت الممر مباشرة ---
        self.capacity_enable_chk = QCheckBox(self.tr("تفعيل السعة والتنبيهات"), self)
        main_layout.addWidget(self.capacity_enable_chk)
        # --- الحقول الثانوية لموقع التخزين ---
        self.storage_details_widget = QWidget()
        storage_details_layout = QFormLayout(self.storage_details_widget)
        self.racks_per_aisle_edit = QLineEdit(self)
        storage_details_layout.addRow(self.tr("عدد الرفوف في الممر:"), self.racks_per_aisle_edit)
        self.bins_per_rack_edit = QLineEdit(self)
        storage_details_layout.addRow(self.tr("عدد الخانات في الرف:"), self.bins_per_rack_edit)
        rack_img_layout = QHBoxLayout()
        self.rack_img_btn = QPushButton(self.tr("🖼️ إضافة صورة رفوف"), self)
        self.rack_img_btn.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        self.rack_img_btn.clicked.connect(self.select_rack_image)
        self.rack_img_thumb = QLabel(self)
        self.rack_img_thumb.setFixedSize(40, 40)
        self.rack_img_thumb.setStyleSheet("border: 1px solid #ccc; background: #fafbfc;")
        rack_img_layout.addWidget(self.rack_img_btn)
        rack_img_layout.addWidget(self.rack_img_thumb)
        rack_img_layout.addStretch()
        storage_details_layout.addRow(self.tr("صورة توضيحية للرفوف:"), rack_img_layout)
        map_img_layout = QHBoxLayout()
        self.map_img_btn = QPushButton(self.tr("🗺️ إضافة خريطة"), self)
        self.map_img_btn.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        self.map_img_btn.clicked.connect(self.select_map_image)
        self.map_img_thumb = QLabel(self)
        self.map_img_thumb.setFixedSize(40, 40)
        self.map_img_thumb.setStyleSheet("border: 1px solid #ccc; background: #fafbfc;")
        map_img_layout.addWidget(self.map_img_btn)
        map_img_layout.addWidget(self.map_img_thumb)
        map_img_layout.addStretch()
        storage_details_layout.addRow(self.tr("خريطة الموقع:"), map_img_layout)
        self.storage_details_widget.setVisible(False)
        main_layout.addWidget(self.storage_details_widget)
        self.storage_expand_btn.toggled.connect(lambda checked: self.storage_details_widget.setVisible(checked))
        self.storage_expand_btn.toggled.connect(lambda checked: self.storage_expand_btn.setIcon(self._create_arrow_icon("down", arrow_color) if checked else self._create_arrow_icon("right", arrow_color)))
        self.storage_enable_chk.stateChanged.connect(lambda state: self.storage_details_widget.setEnabled(state == Qt.Checked))
        # --- السعة والتنبيهات (المساحة الإجمالية + زر توسيع) ---
        capacity_row = QHBoxLayout()
        self.area_edit = QLineEdit(self)
        self.capacity_expand_btn = QPushButton()
        self.capacity_expand_btn.setFixedWidth(28)
        self.capacity_expand_btn.setCheckable(True)
        self.capacity_expand_btn.setChecked(False)
        self.capacity_expand_btn.setIcon(self._create_arrow_icon("right", arrow_color))
        capacity_row.addWidget(QLabel(self.tr("المساحة الإجمالية:")))
        capacity_row.addWidget(self.area_edit)
        capacity_row.addWidget(self.capacity_expand_btn)
        main_layout.addLayout(capacity_row)
        self.capacity_details_widget = QWidget()
        capacity_details_layout = QFormLayout(self.capacity_details_widget)
        self.capacity_edit = QLineEdit(self)
        capacity_details_layout.addRow(self.tr("سعة التخزين المقدّرة:"), self.capacity_edit)
        self.capacity_alert_edit = QLineEdit(self)
        capacity_details_layout.addRow(self.tr("تنبيه انخفاض السعة (%):"), self.capacity_alert_edit)
        self.capacity_details_widget.setVisible(False)
        main_layout.addWidget(self.capacity_details_widget)
        self.capacity_expand_btn.toggled.connect(lambda checked: self.capacity_details_widget.setVisible(checked))
        self.capacity_expand_btn.toggled.connect(lambda checked: self.capacity_expand_btn.setIcon(self._create_arrow_icon("down", arrow_color) if checked else self._create_arrow_icon("right", arrow_color)))
        self.capacity_enable_chk.stateChanged.connect(lambda state: self.capacity_details_widget.setEnabled(state == Qt.Checked))
        # --- الإعدادات المتقدمة (زر تفعيل دائم) ---
        advanced_row = QHBoxLayout()
        self.advanced_enable_chk = QCheckBox(self.tr("تفعيل الإعدادات المتقدمة"), self)
        advanced_row.addWidget(self.advanced_enable_chk)
        main_layout.addLayout(advanced_row)
        access_box = QFrame()
        access_layout = QFormLayout(access_box)
        self.roles_list = QListWidget(self)
        access_layout.addRow(self.tr("صلاحيات الوصول:"), self.roles_list)
        self.active_check = QCheckBox(self.tr("نشط"), self)
        access_layout.addRow("", self.active_check)
        main_layout.addWidget(access_box)
        self.advanced_enable_chk.stateChanged.connect(lambda state: access_box.setEnabled(state == Qt.Checked))
        # --- أزرار الإجراء ---
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel, self)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        main_layout.addWidget(buttons)
        scroll.setWidget(container)
        layout = QVBoxLayout(self)
        layout.addWidget(scroll)
        self.setLayout(layout)

    def toggle_section(self, widget, btn):
        visible = not widget.isVisible()
        widget.setVisible(visible)
        btn.setText("–" if visible else "+")

    def select_main_image(self, event):
        file, _ = QFileDialog.getOpenFileName(self, self.tr("اختر صورة المستودع"), "", "Images (*.png *.jpg *.jpeg *.bmp)")
        if file:
            pixmap = QPixmap(file)
            self.img_thumb.setPixmap(pixmap.scaled(self.img_thumb.size(), Qt.KeepAspectRatio, Qt.SmoothTransformation))
            self.img_thumb.setText("")
            self.main_image_path = file
        else:
            if not self.main_image_path:
                self.img_thumb.setPixmap(QPixmap())
                self.img_thumb.setText(self.tr("اضغط لإضافة صورة المستودع"))
    def select_rack_image(self, event):
        file, _ = QFileDialog.getOpenFileName(self, self.tr("اختر صورة الرفوف"), "", "Images (*.png *.jpg *.jpeg *.bmp)")
        if file:
            pixmap = QPixmap(file)
            self.rack_img_thumb.setPixmap(pixmap.scaled(self.rack_img_thumb.size(), Qt.KeepAspectRatio, Qt.SmoothTransformation))
            self.rack_img_thumb.setText("")
            self.rack_image_path = file
        else:
            if not self.rack_image_path:
                self.rack_img_thumb.setPixmap(QPixmap())
                self.rack_img_thumb.setText(self.tr("اضغط لإضافة صورة الرفوف"))
    def select_map_image(self, event):
        file, _ = QFileDialog.getOpenFileName(self, self.tr("اختر خريطة الموقع"), "", "Images (*.png *.jpg *.jpeg *.bmp)")
        if file:
            pixmap = QPixmap(file)
            self.map_img_thumb.setPixmap(pixmap.scaled(self.map_img_thumb.size(), Qt.KeepAspectRatio, Qt.SmoothTransformation))
            self.map_img_thumb.setText("")
            self.map_image_path = file
        else:
            if not self.map_image_path:
                self.map_img_thumb.setPixmap(QPixmap())
                self.map_img_thumb.setText(self.tr("اضغط لإضافة خريطة الموقع"))

    def get_data(self):
        return {
            "name": self.name_edit.text(),
            "location": self.location_edit.text(),
            "manager": self.manager_combo.currentText(),
            "is_active": self.active_check.isChecked(),
        }

    def set_warehouse_data(self, warehouse):
        self.name_edit.setText(warehouse.get("name", ""))
        if hasattr(self, "location_edit"):
            self.location_edit.setText(warehouse.get("location", ""))
        self.manager_combo.setCurrentText(warehouse.get("manager", ""))
        if hasattr(self, "active_check"):
            self.active_check.setChecked(warehouse.get("is_active", True))
        # تعبئة الصور إذا كانت موجودة
        self.main_image_path = warehouse.get("main_image_path", "")
        if self.main_image_path and hasattr(self, "img_thumb"):
            pixmap = QPixmap(self.main_image_path)
            self.img_thumb.setPixmap(pixmap.scaled(self.img_thumb.size(), Qt.KeepAspectRatio, Qt.SmoothTransformation))
        self.rack_image_path = warehouse.get("rack_image_path", "")
        if self.rack_image_path and hasattr(self, "rack_img_thumb"):
            pixmap = QPixmap(self.rack_image_path)
            self.rack_img_thumb.setPixmap(pixmap.scaled(self.rack_img_thumb.size(), Qt.KeepAspectRatio, Qt.SmoothTransformation))
        self.map_image_path = warehouse.get("map_image_path", "")
        if self.map_image_path and hasattr(self, "map_img_thumb"):
            pixmap = QPixmap(self.map_image_path)
            self.map_img_thumb.setPixmap(pixmap.scaled(self.map_img_thumb.size(), Qt.KeepAspectRatio, Qt.SmoothTransformation))

class WarehouseListTab(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("WarehouseListTab")
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout(self)
        # شريط البحث والفلترة
        search_layout = QHBoxLayout()
        self.search_box = QLineEdit(self)
        self.search_box.setPlaceholderText(self.tr("بحث عن مستودع بالاسم أو الموقع..."))
        self.status_filter = QComboBox(self)
        self.status_filter.addItems([self.tr("الكل"), self.tr("نشط"), self.tr("غير نشط")])
        search_layout.addWidget(QLabel(self.tr("بحث:")))
        search_layout.addWidget(self.search_box)
        search_layout.addWidget(QLabel(self.tr("الحالة:")))
        search_layout.addWidget(self.status_filter)
        layout.addLayout(search_layout)
        # أزرار أعلى الجدول
        btn_layout = QHBoxLayout()
        self.add_btn = QPushButton(self.tr("إضافة مستودع جديد"), self)
        self.add_btn.clicked.connect(self.add_warehouse)
        self.export_btn = QPushButton(self.tr("تصدير"), self)
        btn_layout.addWidget(self.add_btn)
        btn_layout.addWidget(self.export_btn)
        btn_layout.addStretch()
        layout.addLayout(btn_layout)
        # جدول المخازن
        self.table = QTableWidget(self)
        self.table.setColumnCount(7)
        self.table.setHorizontalHeaderLabels([
            self.tr("اسم المستودع"), self.tr("الموقع"), self.tr("المدير"), self.tr("الحالة"), self.tr("تاريخ الإنشاء"), self.tr("تاريخ التحديث"), self.tr("إجراءات")
        ])
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setSelectionMode(QTableWidget.MultiSelection)
        layout.addWidget(self.table)
        self.setLayout(layout)
        # بيانات تجريبية
        self.warehouses = [
            {"name": "المخزن الرئيسي", "location": "الرياض", "manager": "أحمد", "is_active": True, "created_at": "2024-06-01", "updated_at": "2024-06-10",
             "main_image_path": "accounting_app/resources/icons/material/git-repository-svgrepo-com.svg", "rack_image_path": "accounting_app/resources/icons/material/gallery-svgrepo-com.svg", "map_image_path": "accounting_app/resources/icons/material/sitemap-icon.svg"},
            {"name": "مخزن فرعي", "location": "جدة", "manager": "سارة", "is_active": False, "created_at": "2024-06-02", "updated_at": "2024-06-11",
             "main_image_path": "accounting_app/resources/icons/material/git-repository-svgrepo-com.svg", "rack_image_path": "accounting_app/resources/icons/material/gallery-svgrepo-com.svg", "map_image_path": "accounting_app/resources/icons/material/sitemap-icon.svg"}
        ]
        self.refresh_table()
        self.search_box.textChanged.connect(self.filter_table)
        self.status_filter.currentTextChanged.connect(self.filter_table)
        # self.delete_selected_btn.clicked.connect(self.delete_selected)

    def refresh_table(self):
        self.table.setRowCount(0)
        for warehouse in self.warehouses:
            row = self.table.rowCount()
            self.table.insertRow(row)
            self.table.setItem(row, 0, QTableWidgetItem(warehouse["name"]))
            self.table.setItem(row, 1, QTableWidgetItem(warehouse["location"]))
            self.table.setItem(row, 2, QTableWidgetItem(warehouse["manager"]))
            status = self.tr("نشط") if warehouse["is_active"] else self.tr("غير نشط")
            status_item = QTableWidgetItem(status)
            status_item.setForeground(Qt.green if warehouse["is_active"] else Qt.gray)
            self.table.setItem(row, 3, status_item)
            self.table.setItem(row, 4, QTableWidgetItem(warehouse["created_at"]))
            self.table.setItem(row, 5, QTableWidgetItem(warehouse["updated_at"]))
            # --- أزرار الإجراءات مع أيقونات Material Design SVG ---
            # الأيقونات يجب أن تكون في resources/icons/edit.svg, delete.svg, info.svg
            # مصدر الأيقونات: https://fonts.google.com/icons (Material Icons)
            from PySide6.QtGui import QIcon
            from PySide6.QtCore import QSize
            actions_widget = QWidget()
            actions_layout = QHBoxLayout(actions_widget)
            actions_layout.setContentsMargins(0, 0, 0, 0)
            # زر التعديل (أزرق)
            edit_btn = QPushButton()
            edit_btn.setIcon(QIcon('accounting_app/resources/icons/material/edit-173.svg'))
            edit_btn.setIconSize(QSize(22, 22))
            edit_btn.setStyleSheet("QPushButton { background: #e3f0ff; color: #1565c0; border-radius: 6px; padding: 4px; } QPushButton:focus { outline: none; }")
            edit_btn.clicked.connect(partial(self.edit_warehouse, row))
            # زر الحذف (أحمر)
            del_btn = QPushButton()
            del_btn.setIcon(QIcon('accounting_app/resources/icons/material/red-trash-can-icon.svg'))
            del_btn.setIconSize(QSize(22, 22))
            del_btn.setStyleSheet("QPushButton { background: #ffebee; color: #c62828; border-radius: 6px; padding: 4px; } QPushButton:focus { outline: none; }")
            del_btn.clicked.connect(partial(self.delete_warehouse, row))
            # زر التفاصيل (تقرير)
            details_btn = QPushButton()
            details_btn.setIcon(QIcon('accounting_app/resources/icons/material/report-document-file-svgrepo-com.svg'))
            details_btn.setIconSize(QSize(22, 22))
            details_btn.setStyleSheet("QPushButton { background: #f5f5f5; color: #333; border-radius: 6px; padding: 4px; } QPushButton:focus { outline: none; }")
            details_btn.clicked.connect(partial(self.show_details, row))
            # زر عرض الصور (أيقونة)
            view_images_btn = QPushButton()
            # استخدم الأيقونة الجديدة لمعرض الصور
            view_images_btn.setIcon(QIcon('accounting_app/resources/icons/material/picture-gallery-photo-image-svgrepo-com.svg'))
            view_images_btn.setIconSize(QSize(22, 22))
            view_images_btn.setStyleSheet("QPushButton { background: #f0f7fa; color: #1976d2; border-radius: 6px; padding: 4px; } QPushButton:focus { outline: none; }")
            view_images_btn.clicked.connect(partial(self.show_images_dialog, row))
            actions_layout.addWidget(edit_btn)
            actions_layout.addWidget(del_btn)
            actions_layout.addWidget(details_btn)
            actions_layout.addWidget(view_images_btn)
            actions_widget.setLayout(actions_layout)
            self.table.setCellWidget(row, 6, actions_widget)

    def filter_table(self):
        text = self.search_box.text().strip()
        status = self.status_filter.currentText()
        for row in range(self.table.rowCount()):
            match = True
            if text:
                match &= any(text in (self.table.item(row, col).text() if self.table.item(row, col) else "") for col in [0, 1])
            if status != self.tr("الكل"):
                is_active = self.table.item(row, 3) and self.table.item(row, 3).text() == self.tr("نشط")
                match &= (is_active if status == self.tr("نشط") else not is_active)
            self.table.setRowHidden(row, not match)

    def add_warehouse(self):
        dialog = WarehouseDialog(self, managers=["أحمد", "سارة"])
        if dialog.exec() == QDialog.Accepted:
            data = dialog.get_data()
            self.warehouses.append({
                "name": data["name"],
                "location": data["location"],
                "manager": data["manager"],
                "is_active": data["is_active"],
                "created_at": datetime.now().strftime("%Y-%m-%d"),
                "updated_at": datetime.now().strftime("%Y-%m-%d"),
            })
            self.refresh_table()

    def edit_warehouse(self, row):
        warehouse = self.warehouses[row]
        dialog = WarehouseDialog(self, warehouse, managers=["أحمد", "سارة"])
        if dialog.exec() == QDialog.Accepted:
            data = dialog.get_data()
            self.warehouses[row] = {
                "name": data["name"],
                "location": data["location"],
                "manager": data["manager"],
                "is_active": data["is_active"],
                "created_at": warehouse["created_at"],
                "updated_at": datetime.now().strftime("%Y-%m-%d"),
            }
            self.refresh_table()

    def delete_warehouse(self, row):
        reply = QMessageBox.question(self, self.tr("تأكيد الحذف"), self.tr("هل أنت متأكد من حذف المستودع؟"), QMessageBox.Yes | QMessageBox.No)
        if reply == QMessageBox.Yes:
            self.warehouses.pop(row)
            self.refresh_table()

    def delete_selected(self):
        rows = sorted(set(idx.row() for idx in self.table.selectedIndexes()), reverse=True)
        if not rows:
            QMessageBox.information(self, self.tr("تنبيه"), self.tr("لم يتم تحديد أي مستودع للحذف."))
            return
        reply = QMessageBox.question(self, self.tr("تأكيد الحذف الجماعي"), self.tr("هل أنت متأكد من حذف المستودعات المحددة؟"), QMessageBox.Yes | QMessageBox.No)
        if reply == QMessageBox.Yes:
            for row in rows:
                self.warehouses.pop(row)
            self.refresh_table()

    def show_details(self, row):
        warehouse = self.warehouses[row]
        details = f"""
{self.tr('اسم المستودع')}: {warehouse['name']}
{self.tr('الموقع')}: {warehouse['location']}
{self.tr('المدير')}: {warehouse['manager']}
{self.tr('الحالة')}: {self.tr('نشط') if warehouse['is_active'] else self.tr('غير نشط')}
{self.tr('تاريخ الإنشاء')}: {warehouse['created_at']}
{self.tr('تاريخ التحديث')}: {warehouse['updated_at']}
"""
        QMessageBox.information(self, self.tr("تفاصيل المستودع"), details)

    def show_images_dialog(self, row):
        import os
        dialog = QDialog(self)
        dialog.setWindowTitle(self.tr("صور المستودع"))
        layout = QHBoxLayout(dialog)
        warehouse = self.warehouses[row]
        images = [
            (self.tr("صورة المستودع"), warehouse.get("main_image_path")),
            (self.tr("صورة الرفوف"), warehouse.get("rack_image_path")),
            (self.tr("خريطة الموقع"), warehouse.get("map_image_path")),
        ]
        for label_text, img_path in images:
            vbox = QVBoxLayout()
            img_label = QLabel()
            img_label.setFixedSize(100, 100)
            img_label.setAlignment(Qt.AlignCenter)
            # تحسين جمالي: إطار دائري خفيف
            img_label.setStyleSheet("border: 2px solid #e0e0e0; border-radius: 12px; background: #fafbfc; transition: border-color 0.2s;")
            if img_path and os.path.exists(img_path):
                pixmap = QPixmap(img_path)
                img_label.setPixmap(pixmap.scaled(img_label.size(), Qt.KeepAspectRatio, Qt.SmoothTransformation))
            else:
                img_label.setText(self.tr("لا توجد صورة"))
            # تفاعل عند المرور بالفأرة (hover): تغيير لون الإطار
            def enterEvent(event, lbl=img_label):
                lbl.setStyleSheet("border: 2px solid #1976d2; border-radius: 12px; background: #fafbfc;")
            def leaveEvent(event, lbl=img_label):
                lbl.setStyleSheet("border: 2px solid #e0e0e0; border-radius: 12px; background: #fafbfc;")
            img_label.enterEvent = enterEvent
            img_label.leaveEvent = leaveEvent
            # عند الضغط: تكبير الصورة
            img_label.mousePressEvent = lambda e, p=img_path: self.show_full_image(p)
            vbox.addWidget(img_label)
            caption = QLabel(label_text)
            caption.setAlignment(Qt.AlignCenter)
            caption.setStyleSheet("font-size: 12px; color: #444;")
            vbox.addWidget(caption)
            layout.addLayout(vbox)
        dialog.setLayout(layout)
        dialog.exec()

    def show_full_image(self, img_path):
        import os
        if not img_path or not os.path.exists(img_path):
            QMessageBox.warning(self, self.tr("خطأ"), self.tr("الصورة غير موجودة"))
            return
        dialog = QDialog(self)
        dialog.setWindowTitle(self.tr("عرض الصورة"))
        layout = QVBoxLayout(dialog)
        img_label = QLabel()
        pixmap = QPixmap(img_path)
        img_label.setPixmap(pixmap)
        img_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(img_label)
        dialog.setLayout(layout)
        dialog.exec() 