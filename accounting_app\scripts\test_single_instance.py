#!/usr/bin/env python3
"""
سكريبت اختبار آلية منع تشغيل مثيلات متعددة للتطبيق
"""

import sys
import os
import time
import threading
import subprocess

# إضافة مسار الحزمة
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from accounting_app.utils import SingleInstanceApp, ensure_single_instance


def test_single_instance_basic():
    """اختبار أساسي لآلية النسخة الواحدة"""
    print("=== اختبار أساسي لآلية النسخة الواحدة ===")
    
    try:
        # محاولة الحصول على قفل
        instance1 = ensure_single_instance("test_app", 12346)
        print("✓ تم الحصول على القفل الأول بنجاح")
        
        # محاولة الحصول على قفل آخر (يجب أن تفشل)
        try:
            instance2 = ensure_single_instance("test_app", 12346)
            print("❌ فشل الاختبار: تم الحصول على قفل ثاني!")
        except RuntimeError as e:
            print(f"✓ تم رفض القفل الثاني كما هو متوقع: {e}")
        
        # إطلاق القفل الأول
        instance1.release_lock()
        print("✓ تم إطلاق القفل الأول")
        
        # محاولة الحصول على قفل جديد (يجب أن ينجح)
        instance3 = ensure_single_instance("test_app", 12346)
        print("✓ تم الحصول على قفل جديد بعد الإطلاق")
        instance3.release_lock()
        
        print("✅ الاختبار الأساسي نجح!\n")
        return True
        
    except Exception as e:
        print(f"❌ فشل الاختبار الأساسي: {e}\n")
        return False


def test_socket_port():
    """اختبار حجز المنفذ"""
    print("=== اختبار حجز المنفذ ===")
    
    try:
        # إنشاء كائن النسخة الواحدة
        instance = SingleInstanceApp("test_socket", 12347)
        
        # فحص ما إذا كان المنفذ متاح
        is_running, message = instance.check_if_running()
        print(f"حالة التطبيق قبل الحصول على القفل: {is_running} - {message}")
        
        # الحصول على القفل
        if instance.acquire_lock():
            print("✓ تم الحصول على قفل المنفذ")
            
            # فحص ما إذا كان المنفذ محجوز
            is_running, message = instance.check_if_running()
            print(f"حالة التطبيق بعد الحصول على القفل: {is_running} - {message}")
            
            # إطلاق القفل
            instance.release_lock()
            print("✓ تم إطلاق قفل المنفذ")
            
            print("✅ اختبار المنفذ نجح!\n")
            return True
        else:
            print("❌ فشل في الحصول على قفل المنفذ")
            return False
            
    except Exception as e:
        print(f"❌ فشل اختبار المنفذ: {e}\n")
        return False


def test_lock_file():
    """اختبار ملف القفل"""
    print("=== اختبار ملف القفل ===")
    
    try:
        # إنشاء كائن النسخة الواحدة
        instance = SingleInstanceApp("test_lock_file", 12348)
        
        # فحص ملف القفل
        lock_file_path = instance._get_lock_file_path()
        print(f"مسار ملف القفل: {lock_file_path}")
        
        # الحصول على القفل
        if instance.acquire_lock():
            print("✓ تم الحصول على قفل الملف")
            
            # فحص وجود ملف القفل
            if os.path.exists(lock_file_path):
                print("✓ ملف القفل موجود")
                
                # قراءة محتوى الملف
                with open(lock_file_path, 'r') as f:
                    pid = f.read().strip()
                    print(f"معرف العملية في الملف: {pid}")
            else:
                print("❌ ملف القفل غير موجود")
                return False
            
            # إطلاق القفل
            instance.release_lock()
            print("✓ تم إطلاق قفل الملف")
            
            # فحص حذف ملف القفل
            if not os.path.exists(lock_file_path):
                print("✓ تم حذف ملف القفل")
            else:
                print("❌ لم يتم حذف ملف القفل")
                return False
            
            print("✅ اختبار ملف القفل نجح!\n")
            return True
        else:
            print("❌ فشل في الحصول على قفل الملف")
            return False
            
    except Exception as e:
        print(f"❌ فشل اختبار ملف القفل: {e}\n")
        return False


def test_concurrent_instances():
    """اختبار محاولات متزامنة للحصول على القفل"""
    print("=== اختبار المحاولات المتزامنة ===")
    
    results = []
    
    def try_acquire_lock(thread_id):
        """محاولة الحصول على قفل في خيط منفصل"""
        try:
            instance = ensure_single_instance("test_concurrent", 12349)
            results.append((thread_id, True, "نجح"))
            time.sleep(2)  # الاحتفاظ بالقفل لمدة ثانيتين
            instance.release_lock()
        except RuntimeError as e:
            results.append((thread_id, False, str(e)))
        except Exception as e:
            results.append((thread_id, False, f"خطأ: {e}"))
    
    # بدء عدة خيوط في نفس الوقت
    threads = []
    for i in range(5):
        thread = threading.Thread(target=try_acquire_lock, args=(i,))
        threads.append(thread)
        thread.start()
    
    # انتظار انتهاء جميع الخيوط
    for thread in threads:
        thread.join()
    
    # تحليل النتائج
    successful = [r for r in results if r[1]]
    failed = [r for r in results if not r[1]]
    
    print(f"عدد المحاولات الناجحة: {len(successful)}")
    print(f"عدد المحاولات الفاشلة: {len(failed)}")
    
    if len(successful) == 1 and len(failed) == 4:
        print("✅ اختبار المحاولات المتزامنة نجح!")
        print(f"الخيط الناجح: {successful[0][0]}")
        print(f"الخيوط الفاشلة: {[f[0] for f in failed]}")
        return True
    else:
        print("❌ اختبار المحاولات المتزامنة فشل!")
        print(f"النتائج: {results}")
        return False


def test_cleanup_on_exit():
    """اختبار التنظيف عند الإغلاق"""
    print("=== اختبار التنظيف عند الإغلاق ===")
    
    try:
        # الحصول على قفل
        instance = ensure_single_instance("test_cleanup", 12350)
        print("✓ تم الحصول على قفل للاختبار")
        
        # فحص وجود ملف القفل
        lock_file_path = instance._get_lock_file_path()
        if os.path.exists(lock_file_path):
            print("✓ ملف القفل موجود قبل الإغلاق")
        
        # محاكاة الإغلاق الطبيعي
        instance.release_lock()
        print("✓ تم إطلاق القفل")
        
        # فحص حذف ملف القفل
        if not os.path.exists(lock_file_path):
            print("✓ تم حذف ملف القفل بعد الإغلاق")
            print("✅ اختبار التنظيف نجح!\n")
            return True
        else:
            print("❌ لم يتم حذف ملف القفل بعد الإغلاق")
            return False
            
    except Exception as e:
        print(f"❌ فشل اختبار التنظيف: {e}\n")
        return False


def main():
    """الدالة الرئيسية للاختبارات"""
    print("بدء اختبارات آلية منع تشغيل مثيلات متعددة\n")
    
    tests = [
        test_single_instance_basic,
        test_socket_port,
        test_lock_file,
        test_concurrent_instances,
        test_cleanup_on_exit
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("=" * 50)
    print(f"نتائج الاختبارات: {passed}/{total} نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت!")
        return 0
    else:
        print("❌ بعض الاختبارات فشلت!")
        return 1


if __name__ == "__main__":
    sys.exit(main()) 