"""
UI Utilities for Smart Accounting App
أدوات واجهة المستخدم لبرنامج المحاسبة الذكي
"""

import logging
from PySide6.QtWidgets import QListView

logger = logging.getLogger(__name__)

# الحل الجذري: إعادة تعريف دالة update لـ QListView
class SafeQListView(QListView):
    """نسخة آمنة من QListView مع دالة update محسنة"""
    def update(self):
        """دالة update آمنة بدون وسائط إضافية"""
        try:
            super().update()
        except Exception as e:
            logging.debug(f"SafeQListView update failed: {e}")

def apply_safe_listview_fix():
    """تطبيق الحل الجذري على جميع كائنات QListView"""
    try:
        from PySide6.QtWidgets import QApplication
        app = QApplication.instance()
        if app:
            for widget in app.allWidgets():
                if isinstance(widget, QListView) and not isinstance(widget, SafeQListView):
                    # إعادة تعريف دالة update للكائن الموجود
                    def safe_update_method():
                        try:
                            widget.__class__.update(widget)
                        except Exception as e:
                            logging.debug(f"Safe update for existing QListView: {e}")
                    widget.update = safe_update_method
    except Exception as e:
        logging.warning(f"Failed to apply safe listview fix: {e}")

def safe_update(obj):
    """تحديث آمن للعناصر مع معالجة الأخطاء"""
    try:
        if hasattr(obj, "update") and callable(obj.update):
            # استخدام الحل الجذري لـ QListView
            if isinstance(obj, QListView):
                obj.__class__.update(obj)  # استدعاء الدالة الأصلية
            else:
                obj.update()
    except Exception as e:
        logging.warning(f"safe_update failed for {type(obj)}: {e}")

def center_widget_on_screen(widget):
    """توسيط العنصر على الشاشة"""
    try:
        from PySide6.QtWidgets import QApplication
        screen = QApplication.primaryScreen().geometry()
        widget_geometry = widget.geometry()
        x = (screen.width() - widget_geometry.width()) // 2
        y = (screen.height() - widget_geometry.height()) // 2
        widget.move(x, y)
    except Exception as e:
        logger.error(f"Failed to center widget: {e}")

def set_widget_icon(widget, icon_path):
    """تعيين أيقونة للعنصر"""
    try:
        from PySide6.QtGui import QIcon
        if icon_path and os.path.exists(icon_path):
            widget.setIcon(QIcon(icon_path))
    except Exception as e:
        logger.error(f"Failed to set widget icon: {e}")

def enable_widget_animation(widget, duration=300):
    """تفعيل الرسوم المتحركة للعنصر"""
    try:
        from PySide6.QtCore import QPropertyAnimation, QEasingCurve
        animation = QPropertyAnimation(widget, b"geometry")
        animation.setDuration(duration)
        animation.setEasingCurve(QEasingCurve.InOutQuad)
        return animation
    except Exception as e:
        logger.error(f"Failed to enable widget animation: {e}")
        return None 