from PySide6.QtWidgets import QWidget, QVBoxLayout, QTabWidget
from PySide6.QtCore import Qt

class PurchasesMainTab(QWidget):
    """التبويب الرئيسي لقسم المشتريات"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("PurchasesMainTab")
        self.init_ui()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)
        self.tabs = QTabWidget(self)
        
        # إضافة التبويبات الفرعية
        from accounting_app.modules.purchases.ui.purchase_orders_view import PurchaseOrdersTab
        self.tabs.addTab(PurchaseOrdersTab(self), self.tr("أوامر الشراء"))
        
        # سيتم إضافة هذه التبويبات لاحقاً
        # self.tabs.addTab(PurchaseInvoicesTab(self), self.tr("فواتير الشراء"))
        # self.tabs.addTab(PurchaseReturnsTab(self), self.tr("مرتجعات الشراء"))
        # self.tabs.addTab(SuppliersTab(self), self.tr("إدارة الموردين"))
        # self.tabs.addTab(PurchaseReportsTab(self), self.tr("تقارير المشتريات"))
        # self.tabs.addTab(ReceivingTab(self), self.tr("استلام البضائع"))
        # self.tabs.addTab(QuotesTab(self), self.tr("عروض الأسعار"))
        
        layout.addWidget(self.tabs)
        self.setLayout(layout) 