from database.repositories.account_repository import AccountRepository
from database.models import Account
from sqlalchemy.orm import Session

class AccountService:
    def __init__(self, db: Session):
        self.account_repo = AccountRepository(db)

    def create_account(self, company_id: str, name: str, type: str, balance: float = 0):
        account = Account(company_id=company_id, name=name, type=type, balance=balance)
        return self.account_repo.create(account)

    def get_accounts_by_company(self, company_id: str):
        return self.account_repo.get_by_company(company_id)

    def get_account(self, account_id: str):
        return self.account_repo.get_by_id(account_id) 