"""
واجهة فواتير الشراء - واجهة شاملة لإدارة جميع فواتير المشتريات
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QPushButton, QLineEdit, QComboBox, QLabel, QGroupBox, QFormLayout,
    QDialog, QDialogButtonBox, QTabWidget, QTextEdit, QSpinBox,
    QDoubleSpinBox, QDateEdit, QCheckBox, QMessageBox, QHeaderView,
    QFrame, QScrollArea, QGridLayout, QSplitter, QProgressBar
)
from PySide6.QtCore import Qt, QDate, Signal
from PySide6.QtGui import QIcon, QFont
from datetime import datetime, date
import logging

# استيراد النماذج
from ..models.purchase_invoice import (
    PurchaseInvoice, PurchaseInvoiceItem, PaymentRecord,
    InvoiceStatus, InvoiceType, PaymentStatus
)


class PurchaseInvoicesTab(QWidget):
    """التبويب الرئيسي لفواتير الشراء"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("PurchaseInvoicesTab")
        self.invoices_data = []
        self.init_ui()
        self.load_sample_data()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)
        
        # شريط الأدوات العلوي
        toolbar = self.create_toolbar()
        layout.addWidget(toolbar)
        
        # منطقة البحث والفلترة
        search_area = self.create_search_area()
        layout.addWidget(search_area)
        
        # المحتوى الرئيسي
        splitter = QSplitter(Qt.Horizontal)
        
        # قائمة الفواتير (الجانب الأيسر)
        invoices_list = self.create_invoices_list()
        splitter.addWidget(invoices_list)
        
        # تفاصيل الفاتورة (الجانب الأيمن)
        invoice_details = self.create_invoice_details()
        splitter.addWidget(invoice_details)
        
        splitter.setSizes([500, 700])
        layout.addWidget(splitter)
        
        # شريط الحالة
        status_bar = self.create_status_bar()
        layout.addWidget(status_bar)
    
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar = QFrame()
        toolbar.setFrameStyle(QFrame.StyledPanel)
        layout = QHBoxLayout(toolbar)
        
        # أزرار العمليات الرئيسية
        self.add_invoice_btn = QPushButton(self.tr("إنشاء فاتورة جديدة"))
        self.add_invoice_btn.setIcon(QIcon("accounting_app/resources/icons/add.svg"))
        self.add_invoice_btn.clicked.connect(self.add_new_invoice)
        
        self.edit_invoice_btn = QPushButton(self.tr("تعديل"))
        self.edit_invoice_btn.setIcon(QIcon("accounting_app/resources/icons/edit.svg"))
        self.edit_invoice_btn.clicked.connect(self.edit_invoice)
        self.edit_invoice_btn.setEnabled(False)
        
        self.approve_invoice_btn = QPushButton(self.tr("موافقة"))
        self.approve_invoice_btn.setIcon(QIcon("accounting_app/resources/icons/approve.svg"))
        self.approve_invoice_btn.clicked.connect(self.approve_invoice)
        self.approve_invoice_btn.setEnabled(False)
        
        self.pay_invoice_btn = QPushButton(self.tr("تسجيل دفعة"))
        self.pay_invoice_btn.setIcon(QIcon("accounting_app/resources/icons/payment.svg"))
        self.pay_invoice_btn.clicked.connect(self.add_payment)
        self.pay_invoice_btn.setEnabled(False)
        
        self.print_invoice_btn = QPushButton(self.tr("طباعة"))
        self.print_invoice_btn.setIcon(QIcon("accounting_app/resources/icons/print.svg"))
        self.print_invoice_btn.clicked.connect(self.print_invoice)
        self.print_invoice_btn.setEnabled(False)
        
        # إضافة الأزرار
        layout.addWidget(self.add_invoice_btn)
        layout.addWidget(self.edit_invoice_btn)
        layout.addWidget(self.approve_invoice_btn)
        layout.addWidget(self.pay_invoice_btn)
        layout.addWidget(self.print_invoice_btn)
        layout.addStretch()
        
        return toolbar
    
    def create_search_area(self):
        """إنشاء منطقة البحث والفلترة"""
        search_group = QGroupBox(self.tr("البحث والفلترة"))
        layout = QHBoxLayout(search_group)
        
        # حقل البحث
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText(self.tr("البحث في الفواتير..."))
        self.search_input.textChanged.connect(self.filter_invoices)
        
        # فلتر الحالة
        self.status_filter = QComboBox()
        self.status_filter.addItem(self.tr("جميع الحالات"), "")
        for status in InvoiceStatus:
            self.status_filter.addItem(status.value, status.name)
        self.status_filter.currentTextChanged.connect(self.filter_invoices)
        
        # فلتر حالة الدفع
        self.payment_status_filter = QComboBox()
        self.payment_status_filter.addItem(self.tr("جميع حالات الدفع"), "")
        for status in PaymentStatus:
            self.payment_status_filter.addItem(status.value, status.name)
        self.payment_status_filter.currentTextChanged.connect(self.filter_invoices)
        
        # فلتر التاريخ
        self.date_from = QDateEdit()
        self.date_from.setDate(QDate.currentDate().addMonths(-1))
        self.date_to = QDateEdit()
        self.date_to.setDate(QDate.currentDate())
        
        layout.addWidget(QLabel(self.tr("البحث:")))
        layout.addWidget(self.search_input)
        layout.addWidget(QLabel(self.tr("الحالة:")))
        layout.addWidget(self.status_filter)
        layout.addWidget(QLabel(self.tr("حالة الدفع:")))
        layout.addWidget(self.payment_status_filter)
        layout.addWidget(QLabel(self.tr("من:")))
        layout.addWidget(self.date_from)
        layout.addWidget(QLabel(self.tr("إلى:")))
        layout.addWidget(self.date_to)
        layout.addStretch()
        
        return search_group
    
    def create_invoices_list(self):
        """إنشاء قائمة الفواتير"""
        list_group = QGroupBox(self.tr("قائمة الفواتير"))
        layout = QVBoxLayout(list_group)
        
        # جدول الفواتير
        self.invoices_table = QTableWidget()
        self.invoices_table.setColumnCount(7)
        self.invoices_table.setHorizontalHeaderLabels([
            self.tr("رقم الفاتورة"),
            self.tr("المورد"),
            self.tr("التاريخ"),
            self.tr("المبلغ"),
            self.tr("المدفوع"),
            self.tr("الحالة"),
            self.tr("حالة الدفع")
        ])
        
        # تخصيص الجدول
        header = self.invoices_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.Interactive)
        
        self.invoices_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.invoices_table.setSelectionMode(QTableWidget.SingleSelection)
        self.invoices_table.setAlternatingRowColors(True)
        self.invoices_table.itemSelectionChanged.connect(self.on_invoice_selected)
        
        layout.addWidget(self.invoices_table)
        
        # معلومات إحصائية
        stats_layout = QHBoxLayout()
        self.total_invoices_label = QLabel(self.tr("إجمالي الفواتير: 0"))
        self.total_amount_label = QLabel(self.tr("إجمالي المبلغ: 0.00"))
        self.pending_amount_label = QLabel(self.tr("المبلغ المعلق: 0.00"))
        
        stats_layout.addWidget(self.total_invoices_label)
        stats_layout.addWidget(self.total_amount_label)
        stats_layout.addWidget(self.pending_amount_label)
        stats_layout.addStretch()
        
        layout.addLayout(stats_layout)
        
        return list_group
    
    def create_invoice_details(self):
        """إنشاء منطقة تفاصيل الفاتورة"""
        details_group = QGroupBox(self.tr("تفاصيل الفاتورة"))
        layout = QVBoxLayout(details_group)
        
        # تبويبات التفاصيل
        self.details_tabs = QTabWidget()
        
        # تبويب المعلومات الأساسية
        basic_tab = self.create_basic_info_tab()
        self.details_tabs.addTab(basic_tab, self.tr("المعلومات الأساسية"))
        
        # تبويب العناصر
        items_tab = self.create_items_tab()
        self.details_tabs.addTab(items_tab, self.tr("عناصر الفاتورة"))
        
        # تبويب المدفوعات
        payments_tab = self.create_payments_tab()
        self.details_tabs.addTab(payments_tab, self.tr("المدفوعات"))
        
        layout.addWidget(self.details_tabs)
        
        return details_group
    
    def create_basic_info_tab(self):
        """إنشاء تبويب المعلومات الأساسية"""
        tab = QWidget()
        scroll = QScrollArea(tab)
        scroll.setWidgetResizable(True)
        
        container = QWidget()
        layout = QFormLayout(container)
        
        # المعلومات الأساسية
        self.invoice_number_label = QLabel()
        self.supplier_invoice_number_label = QLabel()
        self.supplier_name_label = QLabel()
        self.invoice_date_label = QLabel()
        self.due_date_label = QLabel()
        self.status_label = QLabel()
        self.payment_status_label = QLabel()
        
        # المعلومات المالية
        self.subtotal_label = QLabel()
        self.tax_amount_label = QLabel()
        self.total_amount_label = QLabel()
        self.paid_amount_label = QLabel()
        self.remaining_amount_label = QLabel()
        
        # إضافة الحقول
        layout.addRow(self.tr("رقم الفاتورة:"), self.invoice_number_label)
        layout.addRow(self.tr("رقم فاتورة المورد:"), self.supplier_invoice_number_label)
        layout.addRow(self.tr("المورد:"), self.supplier_name_label)
        layout.addRow(self.tr("تاريخ الفاتورة:"), self.invoice_date_label)
        layout.addRow(self.tr("تاريخ الاستحقاق:"), self.due_date_label)
        layout.addRow(self.tr("الحالة:"), self.status_label)
        layout.addRow(self.tr("حالة الدفع:"), self.payment_status_label)
        layout.addRow(self.tr("المجموع الفرعي:"), self.subtotal_label)
        layout.addRow(self.tr("الضريبة:"), self.tax_amount_label)
        layout.addRow(self.tr("المجموع الكلي:"), self.total_amount_label)
        layout.addRow(self.tr("المبلغ المدفوع:"), self.paid_amount_label)
        layout.addRow(self.tr("المبلغ المتبقي:"), self.remaining_amount_label)
        
        scroll.setWidget(container)
        
        tab_layout = QVBoxLayout(tab)
        tab_layout.addWidget(scroll)
        
        return tab
    
    def create_items_tab(self):
        """إنشاء تبويب عناصر الفاتورة"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # جدول العناصر
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(6)
        self.items_table.setHorizontalHeaderLabels([
            self.tr("المنتج"),
            self.tr("الكمية"),
            self.tr("سعر الوحدة"),
            self.tr("الخصم"),
            self.tr("الضريبة"),
            self.tr("المجموع")
        ])
        
        layout.addWidget(self.items_table)
        
        return tab
    
    def create_payments_tab(self):
        """إنشاء تبويب المدفوعات"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # جدول المدفوعات
        self.payments_table = QTableWidget()
        self.payments_table.setColumnCount(5)
        self.payments_table.setHorizontalHeaderLabels([
            self.tr("تاريخ الدفع"),
            self.tr("المبلغ"),
            self.tr("طريقة الدفع"),
            self.tr("رقم المرجع"),
            self.tr("ملاحظات")
        ])
        
        layout.addWidget(self.payments_table)
        
        return tab
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_bar = QFrame()
        status_bar.setFrameStyle(QFrame.StyledPanel)
        layout = QHBoxLayout(status_bar)
        
        self.status_label = QLabel(self.tr("جاهز"))
        layout.addWidget(self.status_label)
        layout.addStretch()
        
        return status_bar

    def load_sample_data(self):
        """تحميل بيانات وهمية للاختبار"""
        sample_invoices = [
            {
                'invoice_number': 'PI-202401-1001',
                'supplier_invoice_number': 'SUP-INV-001',
                'supplier_name': 'شركة المواد الغذائية المتقدمة',
                'invoice_date': '2024-01-15',
                'due_date': '2024-02-15',
                'status': InvoiceStatus.APPROVED,
                'payment_status': PaymentStatus.PARTIALLY_PAID,
                'total_amount': 15000.00,
                'paid_amount': 7500.00,
                'remaining_amount': 7500.00
            },
            {
                'invoice_number': 'PI-202401-1002',
                'supplier_invoice_number': 'SUP-INV-002',
                'supplier_name': 'مؤسسة التقنية الحديثة',
                'invoice_date': '2024-01-20',
                'due_date': '2024-02-20',
                'status': InvoiceStatus.PENDING,
                'payment_status': PaymentStatus.UNPAID,
                'total_amount': 25000.00,
                'paid_amount': 0.00,
                'remaining_amount': 25000.00
            }
        ]

        self.invoices_data = sample_invoices
        self.populate_invoices_table()
        self.update_statistics()

    def populate_invoices_table(self):
        """ملء جدول الفواتير"""
        self.invoices_table.setRowCount(len(self.invoices_data))

        for row, invoice in enumerate(self.invoices_data):
            # رقم الفاتورة
            self.invoices_table.setItem(row, 0, QTableWidgetItem(invoice['invoice_number']))

            # المورد
            self.invoices_table.setItem(row, 1, QTableWidgetItem(invoice['supplier_name']))

            # التاريخ
            self.invoices_table.setItem(row, 2, QTableWidgetItem(invoice['invoice_date']))

            # المبلغ
            self.invoices_table.setItem(row, 3, QTableWidgetItem(f"{invoice['total_amount']:,.2f}"))

            # المدفوع
            self.invoices_table.setItem(row, 4, QTableWidgetItem(f"{invoice['paid_amount']:,.2f}"))

            # الحالة
            status_item = QTableWidgetItem(invoice['status'].value)
            if invoice['status'] == InvoiceStatus.APPROVED:
                status_item.setBackground(Qt.green)
            elif invoice['status'] == InvoiceStatus.PENDING:
                status_item.setBackground(Qt.yellow)
            else:
                status_item.setBackground(Qt.red)
            self.invoices_table.setItem(row, 5, status_item)

            # حالة الدفع
            payment_status_item = QTableWidgetItem(invoice['payment_status'].value)
            if invoice['payment_status'] == PaymentStatus.FULLY_PAID:
                payment_status_item.setBackground(Qt.green)
            elif invoice['payment_status'] == PaymentStatus.PARTIALLY_PAID:
                payment_status_item.setBackground(Qt.yellow)
            else:
                payment_status_item.setBackground(Qt.red)
            self.invoices_table.setItem(row, 6, payment_status_item)

    def update_statistics(self):
        """تحديث الإحصائيات"""
        total_invoices = len(self.invoices_data)
        total_amount = sum(invoice['total_amount'] for invoice in self.invoices_data)
        pending_amount = sum(invoice['remaining_amount'] for invoice in self.invoices_data)

        self.total_invoices_label.setText(f"إجمالي الفواتير: {total_invoices}")
        self.total_amount_label.setText(f"إجمالي المبلغ: {total_amount:,.2f}")
        self.pending_amount_label.setText(f"المبلغ المعلق: {pending_amount:,.2f}")

    def filter_invoices(self):
        """فلترة الفواتير حسب معايير البحث"""
        search_text = self.search_input.text().lower()
        status_filter = self.status_filter.currentData()
        payment_status_filter = self.payment_status_filter.currentData()

        for row in range(self.invoices_table.rowCount()):
            show_row = True

            # فلترة النص
            if search_text:
                invoice_number = self.invoices_table.item(row, 0).text().lower()
                supplier_name = self.invoices_table.item(row, 1).text().lower()
                if search_text not in invoice_number and search_text not in supplier_name:
                    show_row = False

            # فلترة الحالة
            if status_filter and show_row:
                invoice_status = self.invoices_table.item(row, 5).text()
                if invoice_status != InvoiceStatus[status_filter].value:
                    show_row = False

            # فلترة حالة الدفع
            if payment_status_filter and show_row:
                payment_status = self.invoices_table.item(row, 6).text()
                if payment_status != PaymentStatus[payment_status_filter].value:
                    show_row = False

            self.invoices_table.setRowHidden(row, not show_row)

    def on_invoice_selected(self):
        """عند اختيار فاتورة من القائمة"""
        current_row = self.invoices_table.currentRow()
        if current_row >= 0:
            # تفعيل الأزرار
            self.edit_invoice_btn.setEnabled(True)
            self.approve_invoice_btn.setEnabled(True)
            self.pay_invoice_btn.setEnabled(True)
            self.print_invoice_btn.setEnabled(True)

            # عرض تفاصيل الفاتورة
            invoice_number = self.invoices_table.item(current_row, 0).text()
            self.display_invoice_details(invoice_number)
        else:
            self.edit_invoice_btn.setEnabled(False)
            self.approve_invoice_btn.setEnabled(False)
            self.pay_invoice_btn.setEnabled(False)
            self.print_invoice_btn.setEnabled(False)

    def display_invoice_details(self, invoice_number):
        """عرض تفاصيل الفاتورة المختارة"""
        invoice_data = next((inv for inv in self.invoices_data if inv['invoice_number'] == invoice_number), None)
        if not invoice_data:
            return

        # تحديث المعلومات الأساسية
        self.invoice_number_label.setText(invoice_data['invoice_number'])
        self.supplier_invoice_number_label.setText(invoice_data['supplier_invoice_number'])
        self.supplier_name_label.setText(invoice_data['supplier_name'])
        self.invoice_date_label.setText(invoice_data['invoice_date'])
        self.due_date_label.setText(invoice_data['due_date'])
        self.status_label.setText(invoice_data['status'].value)
        self.payment_status_label.setText(invoice_data['payment_status'].value)
        self.total_amount_label.setText(f"{invoice_data['total_amount']:,.2f}")
        self.paid_amount_label.setText(f"{invoice_data['paid_amount']:,.2f}")
        self.remaining_amount_label.setText(f"{invoice_data['remaining_amount']:,.2f}")

    def add_new_invoice(self):
        """إضافة فاتورة جديدة"""
        dialog = PurchaseInvoiceDialog(self)
        if dialog.exec() == QDialog.Accepted:
            invoice_data = dialog.get_invoice_data()
            self.invoices_data.append(invoice_data)
            self.populate_invoices_table()
            self.update_statistics()
            self.status_label.setText(self.tr("تم إضافة فاتورة جديدة بنجاح"))

    def edit_invoice(self):
        """تعديل الفاتورة المختارة"""
        current_row = self.invoices_table.currentRow()
        if current_row >= 0:
            invoice_number = self.invoices_table.item(current_row, 0).text()
            invoice_data = next((inv for inv in self.invoices_data if inv['invoice_number'] == invoice_number), None)

            if invoice_data:
                dialog = PurchaseInvoiceDialog(self, invoice_data)
                if dialog.exec() == QDialog.Accepted:
                    updated_data = dialog.get_invoice_data()
                    # تحديث البيانات
                    for i, invoice in enumerate(self.invoices_data):
                        if invoice['invoice_number'] == invoice_number:
                            self.invoices_data[i] = updated_data
                            break

                    self.populate_invoices_table()
                    self.display_invoice_details(updated_data['invoice_number'])
                    self.status_label.setText(self.tr("تم تحديث الفاتورة بنجاح"))

    def approve_invoice(self):
        """الموافقة على الفاتورة"""
        current_row = self.invoices_table.currentRow()
        if current_row >= 0:
            invoice_number = self.invoices_table.item(current_row, 0).text()

            reply = QMessageBox.question(
                self,
                self.tr("تأكيد الموافقة"),
                self.tr(f"هل أنت متأكد من الموافقة على الفاتورة '{invoice_number}'؟"),
                QMessageBox.Yes | QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # تحديث حالة الفاتورة
                for invoice in self.invoices_data:
                    if invoice['invoice_number'] == invoice_number:
                        invoice['status'] = InvoiceStatus.APPROVED
                        break

                self.populate_invoices_table()
                self.display_invoice_details(invoice_number)
                self.status_label.setText(self.tr("تم الموافقة على الفاتورة بنجاح"))

    def add_payment(self):
        """إضافة دفعة للفاتورة"""
        current_row = self.invoices_table.currentRow()
        if current_row >= 0:
            invoice_number = self.invoices_table.item(current_row, 0).text()
            dialog = PaymentDialog(self, invoice_number)
            if dialog.exec() == QDialog.Accepted:
                payment_data = dialog.get_payment_data()
                # تحديث بيانات الدفع
                self.status_label.setText(self.tr("تم تسجيل الدفعة بنجاح"))

    def print_invoice(self):
        """طباعة الفاتورة"""
        current_row = self.invoices_table.currentRow()
        if current_row >= 0:
            invoice_number = self.invoices_table.item(current_row, 0).text()
            QMessageBox.information(
                self,
                self.tr("طباعة"),
                self.tr(f"سيتم طباعة الفاتورة '{invoice_number}'")
            )


# نوافذ حوار مبسطة للاختبار

class PurchaseInvoiceDialog(QDialog):
    """نافذة حوار إضافة/تعديل فاتورة الشراء"""

    def __init__(self, parent=None, invoice_data=None):
        super().__init__(parent)
        self.invoice_data = invoice_data
        self.setWindowTitle(
            self.tr("تعديل الفاتورة") if invoice_data else self.tr("إنشاء فاتورة جديدة")
        )
        self.setMinimumSize(600, 400)
        self.init_ui()

    def init_ui(self):
        layout = QFormLayout(self)

        self.invoice_number_edit = QLineEdit()
        self.supplier_name_edit = QLineEdit()
        self.total_amount_edit = QDoubleSpinBox()
        self.total_amount_edit.setRange(0, 1000000)
        self.total_amount_edit.setDecimals(2)

        layout.addRow(self.tr("رقم الفاتورة:"), self.invoice_number_edit)
        layout.addRow(self.tr("المورد:"), self.supplier_name_edit)
        layout.addRow(self.tr("المبلغ الكلي:"), self.total_amount_edit)

        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addRow(buttons)

    def get_invoice_data(self):
        return {
            'invoice_number': self.invoice_number_edit.text(),
            'supplier_name': self.supplier_name_edit.text(),
            'total_amount': self.total_amount_edit.value(),
            'status': InvoiceStatus.DRAFT,
            'payment_status': PaymentStatus.UNPAID,
            'paid_amount': 0.00,
            'remaining_amount': self.total_amount_edit.value()
        }


class PaymentDialog(QDialog):
    """نافذة حوار إضافة دفعة"""

    def __init__(self, parent=None, invoice_number=""):
        super().__init__(parent)
        self.invoice_number = invoice_number
        self.setWindowTitle(self.tr("تسجيل دفعة"))
        self.init_ui()

    def init_ui(self):
        layout = QFormLayout(self)

        self.amount_edit = QDoubleSpinBox()
        self.amount_edit.setRange(0, 1000000)
        self.amount_edit.setDecimals(2)

        self.payment_method_combo = QComboBox()
        self.payment_method_combo.addItems([
            self.tr("تحويل بنكي"),
            self.tr("شيك"),
            self.tr("نقدي"),
            self.tr("بطاقة ائتمان")
        ])

        self.reference_edit = QLineEdit()
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(80)

        layout.addRow(self.tr("المبلغ:"), self.amount_edit)
        layout.addRow(self.tr("طريقة الدفع:"), self.payment_method_combo)
        layout.addRow(self.tr("رقم المرجع:"), self.reference_edit)
        layout.addRow(self.tr("ملاحظات:"), self.notes_edit)

        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addRow(buttons)

    def get_payment_data(self):
        return {
            'amount': self.amount_edit.value(),
            'payment_method': self.payment_method_combo.currentText(),
            'reference_number': self.reference_edit.text(),
            'notes': self.notes_edit.toPlainText()
        }
