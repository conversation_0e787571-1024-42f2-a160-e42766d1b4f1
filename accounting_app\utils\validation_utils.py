"""
Validation Utilities for Smart Accounting App
أدوات التحقق من صحة البيانات لبرنامج المحاسبة الذكي
"""

import re
import logging
from typing import Union, Optional

logger = logging.getLogger(__name__)

def validate_email(email: str) -> bool:
    """التحقق من صحة البريد الإلكتروني"""
    try:
        if not email or not isinstance(email, str):
            return False
        
        # نمط البريد الإلكتروني
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, email.strip()))
    except Exception as e:
        logger.error(f"Email validation error: {e}")
        return False

def validate_phone(phone: str) -> bool:
    """التحقق من صحة رقم الهاتف"""
    try:
        if not phone or not isinstance(phone, str):
            return False
        
        # إزالة المسافات والرموز
        clean_phone = re.sub(r'[\s\-\(\)\+]', '', phone)
        
        # التحقق من أن الرقم يحتوي على أرقام فقط
        if not clean_phone.isdigit():
            return False
        
        # التحقق من الطول (7-15 رقم)
        return 7 <= len(clean_phone) <= 15
    except Exception as e:
        logger.error(f"Phone validation error: {e}")
        return False

def validate_amount(amount: Union[str, float, int]) -> bool:
    """التحقق من صحة المبلغ"""
    try:
        if amount is None:
            return False
        
        # تحويل إلى رقم
        if isinstance(amount, str):
            amount = float(amount.replace(',', ''))
        
        # التحقق من أن المبلغ موجب
        return float(amount) >= 0
    except (ValueError, TypeError) as e:
        logger.error(f"Amount validation error: {e}")
        return False

def validate_date_format(date_str: str, format_str: str = '%Y-%m-%d') -> bool:
    """التحقق من صحة تنسيق التاريخ"""
    try:
        from datetime import datetime
        datetime.strptime(date_str, format_str)
        return True
    except (ValueError, TypeError) as e:
        logger.error(f"Date validation error: {e}")
        return False

def validate_username(username: str) -> bool:
    """التحقق من صحة اسم المستخدم"""
    try:
        if not username or not isinstance(username, str):
            return False
        
        # التحقق من الطول (3-20 حرف)
        if not (3 <= len(username) <= 20):
            return False
        
        # التحقق من الأحرف المسموحة (أحرف وأرقام وشرطة سفلية)
        pattern = r'^[a-zA-Z0-9_]+$'
        return bool(re.match(pattern, username))
    except Exception as e:
        logger.error(f"Username validation error: {e}")
        return False

def validate_password(password: str, min_length: int = 8) -> bool:
    """التحقق من قوة كلمة المرور"""
    try:
        if not password or not isinstance(password, str):
            return False
        
        # التحقق من الطول
        if len(password) < min_length:
            return False
        
        # التحقق من وجود حرف كبير
        if not re.search(r'[A-Z]', password):
            return False
        
        # التحقق من وجود حرف صغير
        if not re.search(r'[a-z]', password):
            return False
        
        # التحقق من وجود رقم
        if not re.search(r'\d', password):
            return False
        
        return True
    except Exception as e:
        logger.error(f"Password validation error: {e}")
        return False

def validate_file_path(file_path: str) -> bool:
    """التحقق من صحة مسار الملف"""
    try:
        import os
        if not file_path or not isinstance(file_path, str):
            return False
        
        # التحقق من أن المسار صحيح
        return os.path.exists(file_path) and os.path.isfile(file_path)
    except Exception as e:
        logger.error(f"File path validation error: {e}")
        return False

def validate_required_fields(data: dict, required_fields: list) -> tuple[bool, list]:
    """التحقق من وجود الحقول المطلوبة"""
    try:
        missing_fields = []
        
        for field in required_fields:
            if field not in data or data[field] is None or data[field] == "":
                missing_fields.append(field)
        
        return len(missing_fields) == 0, missing_fields
    except Exception as e:
        logger.error(f"Required fields validation error: {e}")
        return False, required_fields

def sanitize_input(text: str) -> str:
    """تنظيف النص المدخل من الأحرف الخطرة"""
    try:
        if not text or not isinstance(text, str):
            return ""
        
        # إزالة الأحرف الخطرة
        dangerous_chars = ['<', '>', '"', "'", '&', ';']
        for char in dangerous_chars:
            text = text.replace(char, '')
        
        # إزالة المسافات الزائدة
        text = ' '.join(text.split())
        
        return text.strip()
    except Exception as e:
        logger.error(f"Input sanitization error: {e}")
        return "" 