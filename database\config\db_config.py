import os
from dotenv import load_dotenv

load_dotenv()

DB_HOST = os.getenv('DB_HOST', 'localhost')
DB_PORT = os.getenv('DB_PORT', '5432')
DB_NAME = os.getenv('DB_NAME', 'accounting_db')
DB_USER = os.getenv('DB_USER', 'accounting_user')
DB_PASSWORD = os.getenv('DB_PASSWORD', 'yourpassword')

DB_URL = f"postgresql+psycopg2://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"

REDIS_URL = os.getenv('REDIS_URL', 'redis://localhost:6379/0')
JWT_SECRET = os.getenv('JWT_SECRET', 'your_jwt_secret')
