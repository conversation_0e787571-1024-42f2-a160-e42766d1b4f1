"""
Resource Checker for Smart Accounting App
فاحص الموارد لبرنامج المحاسبة الذكي
"""

import os
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class ResourceInfo:
    """معلومات المورد"""
    path: str
    exists: bool
    size: int = 0
    error: Optional[str] = None

class ResourceChecker:
    """فاحص موارد التطبيق"""
    
    def __init__(self, app_root: str = None):
        """تهيئة فاحص الموارد"""
        if app_root is None:
            # الحصول على مجلد التطبيق تلقائياً
            app_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        
        self.app_root = Path(app_root)
        self.resources_path = self.app_root / "resources"
        self.translations_path = self.app_root / "translations"
        
        # قائمة الموارد المطلوبة
        self.required_resources = {
            # الأيقونات SVG
            "icons": [
                "home.svg",
                "user.svg", 
                "settings.svg",
                "ai.svg",
                "bell.svg",
                "exit.svg",
                "lang.svg",
                "mic.svg",
                "loading.svg",
                "support.svg"
            ],
            # الأصوات
            "sounds": [
                "notification.wav"
            ],
            # ملفات الترجمة
            "translations": [
                "ar.qm",
                "en.qm", 
                "fr.qm"
            ]
        }
        
        # بدائل للموارد المفقودة
        self.fallback_resources = {
            "home.svg": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTMgMTJMMTIgM0wyMSAxMkwyMCAxM0gxOVYyMEg1VjEzSDQiIHN0cm9rZT0iY3VycmVudENvbG9yIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K",
            "user.svg": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTIiIGN5PSI4IiByPSI1IiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIi8+CjxwYXRoIGQ9Ik0yMCAyMS4xMkMxOS41NCAxOC4wMiAxNi41NiAxNiAxMyAxNkM5LjQ0IDE2IDYuNDYgMTguMDIgNiAyMS4xMiIgc3Ryb2tlPSJjdXJyZW50Q29sb3IiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=",
            "settings.svg": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTIiIGN5PSIxMiIgcj0iMyIgc3Ryb2tlPSJjdXJyZW50Q29sb3IiIHN0cm9rZS13aWR0aD0iMiIvPgo8cGF0aCBkPSJNMTkuNCAxNWEyLjY1IDIuNjUgMCAwIDAgLjQyIDEuODVsLjA2LjA2YTIgMiAwIDAgMSAwIDIuODMgMiAyIDAgMCAxLTIuODMgMGwtLjA2LS4wNmEyLjY1IDIuNjUgMCAwIDAtMS44NS0uNDIgMi42NSAyLjY1IDAgMCAwLTEuODUuNDJsLS4wNi4wNmEyIDIgMCAwIDEtMi44MyAwIDIgMiAwIDAgMSAwLTIuODNsLjA2LS4wNmEyLjY1IDIuNjUgMCAwIDAgLjQyLTEuODUgMi42NSAyLjY1IDAgMCAwLS40Mi0xLjg1bC0uMDYtLjA2YTIgMiAwIDAgMSAwLTIuODMgMiAyIDAgMCAxIDIuODMgMGwuMDYuMDZhMi42NSAyLjY1IDAgMCAwIDEuODUuNDJjLjY2IDAgMS4yLS4yIDEuODUtLjQybC4wNi0uMDZhMiAyIDAgMCAxIDIuODMgMCAyIDIgMCAwIDEgMCAyLjgzIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==",
            "ai.svg": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTkgMTJMMTUgMTJNMTIgOUwxMiAxNU0yMSAxMkMxMiAyMSAzIDEyIDIxIDEyWiIgc3Ryb2tlPSJjdXJyZW50Q29sb3IiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=",
            "bell.svg": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTE4IDhBNiA2IDAgMCAwIDYgOGMwIDctMyA5LTMgMTNoMTJjMC00LTMtNi0zLTEzeiIgc3Ryb2tlPSJjdXJyZW50Q29sb3IiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+CjxwYXRoIGQ9Ik0xMy43MyAyMWEyIDIgMCAwIDEtMy40NiAwIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==",
            "exit.svg": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTkgMjFIMTVBMiAyIDAgMCAwIDE3IDE5VjVBMiAyIDAgMCAwIDE1IDNIOVYyMU0xMCA5TDE0IDEzTDEwIDE3IiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==",
            "lang.svg": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTIgMTJIMjJNMTIgMlYyMk0xMiAyQzE2LjQxIDIgMjAgNS41OSAyMCAxME0xMiAyMkMxNi40MSAyMiAyMCAxOC40MSAyMCAxNCIgc3Ryb2tlPSJjdXJyZW50Q29sb3IiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=",
            "mic.svg": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDFhMyAzIDAgMCAwLTMgM3Y4YzAgMS42NiAxLjM0IDMgMyAzczMtMS4zNCAzLTMtMy04YzAtMS42Ni0xLjM0LTMtMy0zeiIgc3Ryb2tlPSJjdXJyZW50Q29sb3IiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+CjxwYXRoIGQ9Ik0xOSAxMHYyYzAgMy44Ny0zLjEzIDctNyA3cy03LTMuMTMtNy03di0yIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPHBhdGggZD0iTTEyIDE5djQiIHN0cm9rZT0iY3VycmVudENvbG9yIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K",
            "loading.svg": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJWMk0xMiAyMlYyMk0xMiAyQzE2LjQxIDIgMjAgNS41OSAyMCAxME0xMiAyMkMxNi40MSAyMiAyMCAxOC40MSAyMCAxNCIgc3Ryb2tlPSJjdXJyZW50Q29sb3IiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=",
            "support.svg": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTIxIDE1YTIgMiAwIDAgMS0yIDJoLTFhMiAyIDAgMCAxLTItMnYtMmEyIDIgMCAwIDEgMi0yaDFhMiAyIDAgMCAxIDIgMnYyeiIgc3Ryb2tlPSJjdXJyZW50Q29sb3IiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+CjxwYXRoIGQ9Ik03IDE1YTIgMiAwIDAgMS0yLTJ2LTJhMiAyIDAgMCAxIDItMmgxYTIgMiAwIDAgMSAyIDJ2MmEyIDIgMCAwIDEtMiAyaC0xeiIgc3Ryb2tlPSJjdXJyZW50Q29sb3IiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+CjxwYXRoIGQ9Ik0xNyAxMWMwLTEuMS0uOS0yLTIuMDItMkMxNC4xIDkgMTMgMTAuMSAxMyAxMS41YzAgMS40IDEuMSAyLjUgMi41IDIuNWgxYzEuNCAwIDIuNS0xLjEgMi41LTIuNXoiIHN0cm9rZT0iY3VycmVudENvbG9yIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K"
        }
    
    def check_all_resources(self) -> Dict[str, List[ResourceInfo]]:
        """فحص جميع موارد التطبيق"""
        logger.info("🔍 بدء فحص موارد التطبيق...")
        
        results = {}
        
        # فحص الأيقونات
        results["icons"] = self._check_icons()
        
        # فحص الأصوات
        results["sounds"] = self._check_sounds()
        
        # فحص ملفات الترجمة
        results["translations"] = self._check_translations()
        
        # طباعة التقرير
        self._print_report(results)
        
        return results
    
    def _check_icons(self) -> List[ResourceInfo]:
        """فحص الأيقونات"""
        icons = []
        icons_path = self.resources_path
        
        for icon_name in self.required_resources["icons"]:
            icon_path = icons_path / icon_name
            resource_info = self._check_single_resource(icon_path, "أيقونة")
            icons.append(resource_info)
        
        return icons
    
    def _check_sounds(self) -> List[ResourceInfo]:
        """فحص الأصوات"""
        sounds = []
        sounds_path = self.resources_path
        
        for sound_name in self.required_resources["sounds"]:
            sound_path = sounds_path / sound_name
            resource_info = self._check_single_resource(sound_path, "صوت")
            sounds.append(resource_info)
        
        return sounds
    
    def _check_translations(self) -> List[ResourceInfo]:
        """فحص ملفات الترجمة"""
        translations = []
        translations_path = self.translations_path
        
        for translation_name in self.required_resources["translations"]:
            translation_path = translations_path / translation_name
            resource_info = self._check_single_resource(translation_path, "ترجمة")
            translations.append(resource_info)
        
        return translations
    
    def _check_single_resource(self, file_path: Path, resource_type: str) -> ResourceInfo:
        """فحص مورد واحد"""
        try:
            if file_path.exists():
                size = file_path.stat().st_size
                if size == 0:
                    return ResourceInfo(
                        path=str(file_path),
                        exists=True,
                        size=size,
                        error=f"{resource_type} فارغ (0 بايت)"
                    )
                return ResourceInfo(
                    path=str(file_path),
                    exists=True,
                    size=size
                )
            else:
                return ResourceInfo(
                    path=str(file_path),
                    exists=False,
                    error=f"{resource_type} غير موجود"
                )
        except Exception as e:
            return ResourceInfo(
                path=str(file_path),
                exists=False,
                error=f"خطأ في فحص {resource_type}: {str(e)}"
            )
    
    def _print_report(self, results: Dict[str, List[ResourceInfo]]):
        """طباعة تقرير فحص الموارد"""
        logger.info("📊 تقرير فحص موارد التطبيق:")
        
        total_resources = 0
        missing_resources = 0
        error_resources = 0
        
        for category, resources in results.items():
            logger.info(f"\n📁 {category.upper()}:")
            
            for resource in resources:
                total_resources += 1
                
                if resource.exists and not resource.error:
                    logger.info(f"  ✅ {os.path.basename(resource.path)} ({resource.size} بايت)")
                elif resource.exists and resource.error:
                    logger.warning(f"  ⚠️  {os.path.basename(resource.path)}: {resource.error}")
                    error_resources += 1
                else:
                    logger.error(f"  ❌ {os.path.basename(resource.path)}: {resource.error}")
                    missing_resources += 1
        
        # ملخص
        logger.info(f"\n📈 ملخص الفحص:")
        logger.info(f"  إجمالي الموارد: {total_resources}")
        logger.info(f"  الموارد الموجودة: {total_resources - missing_resources - error_resources}")
        logger.info(f"  الموارد المفقودة: {missing_resources}")
        logger.info(f"  الموارد المعطوبة: {error_resources}")
        
        if missing_resources > 0 or error_resources > 0:
            logger.warning("⚠️  تم اكتشاف مشاكل في الموارد. قد تؤثر على عمل التطبيق.")
        else:
            logger.info("✅ جميع الموارد موجودة وصحيحة.")
    
    def get_missing_resources(self) -> List[str]:
        """الحصول على قائمة الموارد المفقودة"""
        results = self.check_all_resources()
        missing = []
        
        for category, resources in results.items():
            for resource in resources:
                if not resource.exists:
                    missing.append(resource.path)
        
        return missing
    
    def create_fallback_resource(self, resource_name: str) -> bool:
        """إنشاء مورد بديل"""
        try:
            if resource_name in self.fallback_resources:
                # إنشاء المورد من البيانات المشفرة
                fallback_data = self.fallback_resources[resource_name]
                
                if resource_name.endswith('.svg'):
                    # إنشاء ملف SVG
                    resource_path = self.resources_path / resource_name
                    with open(resource_path, 'w', encoding='utf-8') as f:
                        f.write(fallback_data)
                    logger.info(f"✅ تم إنشاء بديل لـ {resource_name}")
                    return True
                
            return False
        except Exception as e:
            logger.error(f"❌ فشل في إنشاء بديل لـ {resource_name}: {e}")
            return False
    
    def fix_missing_resources(self) -> int:
        """إصلاح الموارد المفقودة"""
        logger.info("🔧 بدء إصلاح الموارد المفقودة...")
        
        fixed_count = 0
        missing_resources = self.get_missing_resources()
        
        for resource_path in missing_resources:
            resource_name = os.path.basename(resource_path)
            if self.create_fallback_resource(resource_name):
                fixed_count += 1
        
        logger.info(f"✅ تم إصلاح {fixed_count} مورد من أصل {len(missing_resources)}")
        return fixed_count

def check_resources_on_startup() -> bool:
    """فحص الموارد عند بدء التشغيل"""
    print("🔍 بدء فحص موارد التطبيق...")
    logger.info("🔍 بدء فحص موارد التطبيق...")
    
    try:
        checker = ResourceChecker()
        results = checker.check_all_resources()
        
        # حساب الموارد المفقودة والمعطوبة
        missing_count = 0
        error_count = 0
        
        for category, resources in results.items():
            for resource in resources:
                if not resource.exists:
                    missing_count += 1
                elif resource.error:
                    error_count += 1
        
        print(f"📊 تم فحص الموارد: {missing_count} مفقود، {error_count} معطوب")
        logger.info(f"📊 تم فحص الموارد: {missing_count} مفقود، {error_count} معطوب")
        
        # إذا كان هناك مشاكل، محاولة الإصلاح
        if missing_count > 0 or error_count > 0:
            print(f"⚠️  تم اكتشاف {missing_count} مورد مفقود و {error_count} مورد معطوب")
            logger.warning(f"⚠️  تم اكتشاف {missing_count} مورد مفقود و {error_count} مورد معطوب")
            
            # محاولة إصلاح الموارد المفقودة
            fixed_count = checker.fix_missing_resources()
            
            if fixed_count > 0:
                print(f"✅ تم إصلاح {fixed_count} مورد")
                logger.info(f"✅ تم إصلاح {fixed_count} مورد")
            
            # فحص مرة أخرى بعد الإصلاح
            results_after_fix = checker.check_all_resources()
            
            # حساب المشاكل المتبقية
            remaining_issues = 0
            for category, resources in results_after_fix.items():
                for resource in resources:
                    if not resource.exists or resource.error:
                        remaining_issues += 1
            
            if remaining_issues == 0:
                print("✅ تم إصلاح جميع مشاكل الموارد بنجاح")
                logger.info("✅ تم إصلاح جميع مشاكل الموارد بنجاح")
                return True
            else:
                print(f"⚠️  لا تزال هناك {remaining_issues} مشكلة في الموارد")
                logger.warning(f"⚠️  لا تزال هناك {remaining_issues} مشكلة في الموارد")
                return False
        else:
            print("✅ جميع الموارد موجودة وصحيحة")
            logger.info("✅ جميع الموارد موجودة وصحيحة")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في فحص الموارد: {e}")
        logger.error(f"❌ خطأ في فحص الموارد: {e}")
        return False 