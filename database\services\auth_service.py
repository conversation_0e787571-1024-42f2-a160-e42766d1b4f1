from database.repositories.user_repository import UserRepository
from database.models import <PERSON>ppUser
from sqlalchemy.orm import Session
import bcrypt
from jose import jwt
from datetime import datetime, timedelta
from database.config.db_config import JWT_SECRET

class AuthService:
    def __init__(self, db: Session):
        self.user_repo = UserRepository(db)

    def hash_password(self, password: str) -> str:
        return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

    def verify_password(self, password: str, hashed: str) -> bool:
        return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))

    def register_user(self, username: str, password: str, role: str = 'accountant'):
        if self.user_repo.get_by_username(username):
            raise ValueError('Username already exists')
        hashed = self.hash_password(password)
        user = AppUser(username=username, password_hash=hashed, role=role)
        return self.user_repo.create(user)

    def authenticate_user(self, username: str, password: str):
        user = self.user_repo.get_by_username(username)
        if not user or not self.verify_password(password, user.password_hash):
            return None
        return user

    def create_access_token(self, username: str, expires_delta: timedelta = timedelta(hours=1)):
        to_encode = {"sub": username, "exp": datetime.utcnow() + expires_delta}
        return jwt.encode(to_encode, JWT_SECRET, algorithm="HS256") 