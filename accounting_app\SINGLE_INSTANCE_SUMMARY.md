# ملخص آلية منع تشغيل مثيلات متعددة

## نظرة عامة

تم تنفيذ آلية شاملة لمنع تشغيل مثيلات متعددة من التطبيق باستخدام مزيج من ملف قفل (Lock File) ومنفذ Socket. هذه الآلية تضمن تشغيل نسخة واحدة فقط من التطبيق في نفس الوقت.

## الملفات المضافة/المحدثة

### 1. `accounting_app/utils/single_instance.py` (جديد)
- فئة `SingleInstanceApp` لإدارة قفل التطبيق
- دالة `ensure_single_instance()` للاستخدام السهل
- آلية مزدوجة: ملف قفل + منفذ Socket
- تنظيف تلقائي عند الإغلاق

### 2. `accounting_app/utils/__init__.py` (محدث)
- إضافة استيراد `SingleInstanceApp` و `ensure_single_instance`
- تحديث قائمة `__all__`

### 3. `accounting_app/main.py` (محدث)
- إضافة فحص النسخة الواحدة في بداية `main()`
- عرض رسالة تحذير للمستخدم إذا كان التطبيق قيد التشغيل
- تنظيف تلقائي عند الإغلاق

### 4. `accounting_app/scripts/test_single_instance.py` (جديد)
- 5 اختبارات شاملة لآلية النسخة الواحدة
- اختبار المحاولات المتزامنة
- اختبار التنظيف عند الإغلاق

### 5. `accounting_app/docs/single_instance_guide.md` (جديد)
- دليل شامل لاستخدام الآلية
- أمثلة عملية
- استكشاف الأخطاء

## الميزات الرئيسية

### 1. آلية مزدوجة للحماية
- **ملف قفل**: يحتفظ بمعرف العملية في مجلد مؤقت
- **منفذ Socket**: يحجز منفذ محلي للتحقق من التشغيل

### 2. تنظيف تلقائي
- حذف ملف القفل عند الإغلاق الطبيعي
- إغلاق منفذ Socket
- معالجة الإشارات (SIGINT, SIGTERM)

### 3. فحص ذكي للعمليات
- التحقق من وجود العملية قبل الحصول على القفل
- حذف ملفات القفل القديمة تلقائياً

### 4. رسائل واضحة للمستخدم
- عرض رسالة تحذير عند محاولة تشغيل نسخة أخرى
- رسائل مفصلة في السجلات

## كيفية الاستخدام

### الاستخدام الأساسي
```python
from accounting_app.utils import ensure_single_instance

try:
    single_instance = ensure_single_instance("accounting_app", 12345)
    # تشغيل التطبيق
except RuntimeError as e:
    print(f"لا يمكن تشغيل نسخة أخرى: {e}")
    sys.exit(1)
```

### الاستخدام المتقدم
```python
from accounting_app.utils import SingleInstanceApp

instance = SingleInstanceApp("my_app", 12345)
if instance.acquire_lock():
    # تشغيل التطبيق
    instance.release_lock()
```

## الاختبارات

### تشغيل الاختبارات
```bash
cd accounting_app/scripts
python test_single_instance.py
```

### أنواع الاختبارات
1. **الاختبار الأساسي**: الحصول على قفل وإطلاقه
2. **اختبار المنفذ**: حجز المنفذ والتحقق منه
3. **اختبار ملف القفل**: إنشاء وحذف ملف القفل
4. **اختبار المحاولات المتزامنة**: منع الحصول على قفل متعدد
5. **اختبار التنظيف**: التنظيف عند الإغلاق

## الأمان والموثوقية

### 1. حماية من التعارض
- فحص وجود العملية قبل الحصول على القفل
- حذف ملفات القفل القديمة تلقائياً

### 2. تنظيف آمن
- استخدام `atexit` للتنظيف التلقائي
- معالجة الإشارات للتنظيف عند الإغلاق القسري

### 3. رسائل واضحة
- رسائل تحذير للمستخدم
- سجلات مفصلة للتشخيص

## التكامل مع التطبيق

### في `main.py`
- فحص النسخة الواحدة في بداية التشغيل
- عرض رسالة تحذير إذا كان التطبيق قيد التشغيل
- تنظيف تلقائي عند الإغلاق

### مع نظام الأحداث
- لا يتداخل مع نظام الأحداث الحالي
- يعمل بشكل مستقل عن باقي المكونات

## المزايا

### 1. موثوقية عالية
- آلية مزدوجة للحماية
- تنظيف تلقائي للموارد

### 2. سهولة الاستخدام
- دالة مساعدة بسيطة
- معالجة تلقائية للأخطاء

### 3. مرونة
- قابل للتخصيص (اسم التطبيق، المنفذ)
- دعم للاستخدام المتقدم

### 4. توثيق شامل
- دليل مفصل للاستخدام
- أمثلة عملية
- استكشاف الأخطاء

## القيود والحلول

### 1. ملف قفل قديم
**المشكلة**: قد يبقى ملف القفل إذا انتهت العملية بشكل غير طبيعي
**الحل**: فحص وجود العملية وحذف الملف القديم تلقائياً

### 2. منفذ محجوز
**المشكلة**: قد يكون المنفذ محجوزاً من تطبيق آخر
**الحل**: استخدام منفذ في نطاق آمن (12345)

### 3. صلاحيات النظام
**المشكلة**: قد تتطلب صلاحيات إضافية في بعض الأنظمة
**الحل**: استخدام منفذ محلي (localhost) فقط

## الخلاصة

تم تنفيذ آلية شاملة وموثوقة لمنع تشغيل مثيلات متعددة من التطبيق. الآلية تستخدم مزيجاً من ملف قفل ومنفذ Socket لتوفير حماية مزدوجة، مع تنظيف تلقائي للموارد ورسائل واضحة للمستخدم.

### النقاط الرئيسية:
- ✅ آلية مزدوجة للحماية (ملف قفل + منفذ Socket)
- ✅ تنظيف تلقائي عند الإغلاق
- ✅ فحص ذكي للعمليات
- ✅ رسائل واضحة للمستخدم
- ✅ اختبارات شاملة
- ✅ توثيق مفصل
- ✅ تكامل سلس مع التطبيق

### الخطوات التالية:
1. اختبار الآلية في بيئة الإنتاج
2. مراقبة الأداء والتأثير على بدء التشغيل
3. إضافة خيارات إضافية إذا لزم الأمر 