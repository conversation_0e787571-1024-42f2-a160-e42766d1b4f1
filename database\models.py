from sqlalchemy import Column, String, DateTime, ForeignKey, DECIMAL, Boolean, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import declarative_base, relationship
import uuid
from datetime import datetime
import enum
from sqlalchemy import Enum as SqlEnum

Base = declarative_base()

def utcnow():
    return datetime.utcnow()

class AppUser(Base):
    __tablename__ = 'app_user'
    user_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    username = Column(String(50), unique=True, nullable=False)
    password_hash = Column(Text, nullable=False)
    role = Column(String(20), nullable=False)
    created_at = Column(DateTime(timezone=True), default=utcnow)
    updated_at = Column(DateTime(timezone=True), default=utcnow, onupdate=utcnow)
    companies = relationship('Company', back_populates='owner')

class Company(Base):
    __tablename__ = 'company'
    company_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(100), nullable=False)
    owner_id = Column(UUID(as_uuid=True), ForeignKey('app_user.user_id', ondelete='CASCADE'), index=True, nullable=False)
    created_at = Column(DateTime(timezone=True), default=utcnow)
    updated_at = Column(DateTime(timezone=True), default=utcnow, onupdate=utcnow)
    owner = relationship('AppUser', back_populates='companies')
    accounts = relationship('Account', back_populates='company', cascade='all, delete-orphan')
    transactions = relationship('Transaction', back_populates='company', cascade='all, delete-orphan')

class AccountType(enum.Enum):
    ASSET = 'asset'
    LIABILITY = 'liability'
    EQUITY = 'equity'
    REVENUE = 'revenue'
    EXPENSE = 'expense'

class Account(Base):
    __tablename__ = 'account'
    account_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    company_id = Column(UUID(as_uuid=True), ForeignKey('company.company_id', ondelete='CASCADE'), index=True, nullable=False)
    name = Column(String(100), nullable=False)
    type = Column(SqlEnum(AccountType), nullable=False)
    balance = Column(DECIMAL(18,2), default=0)
    created_at = Column(DateTime(timezone=True), default=utcnow)
    updated_at = Column(DateTime(timezone=True), default=utcnow, onupdate=utcnow)
    company = relationship('Company', back_populates='accounts')
    entries = relationship('TransactionEntry', back_populates='account', cascade='all, delete-orphan')

class Transaction(Base):
    __tablename__ = 'transaction'
    transaction_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    company_id = Column(UUID(as_uuid=True), ForeignKey('company.company_id', ondelete='CASCADE'), index=True, nullable=False)
    description = Column(Text)
    date = Column(DateTime(timezone=True), default=utcnow)
    created_at = Column(DateTime(timezone=True), default=utcnow)
    updated_at = Column(DateTime(timezone=True), default=utcnow, onupdate=utcnow)
    company = relationship('Company', back_populates='transactions')
    entries = relationship('TransactionEntry', back_populates='transaction', cascade='all, delete-orphan')

class TransactionEntry(Base):
    __tablename__ = 'transaction_entries'
    entry_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    transaction_id = Column(UUID(as_uuid=True), ForeignKey('transaction.transaction_id', ondelete='CASCADE'), index=True, nullable=False)
    account_id = Column(UUID(as_uuid=True), ForeignKey('account.account_id', ondelete='CASCADE'), index=True, nullable=False)
    amount = Column(DECIMAL(18,2), nullable=False)
    is_debit = Column(Boolean, nullable=False)
    created_at = Column(DateTime(timezone=True), default=utcnow)
    updated_at = Column(DateTime(timezone=True), default=utcnow, onupdate=utcnow)
    transaction = relationship('Transaction', back_populates='entries')
    account = relationship('Account', back_populates='entries')
