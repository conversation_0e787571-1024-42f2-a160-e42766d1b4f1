# توثيق واجهة برمجة التطبيقات (API Reference)

هذا الملف يوضح نقاط النهاية (Endpoints) الأساسية في النظام، مع أمثلة الطلب والاستجابة.

---

## المصادقة (Authentication)

### تسجيل الدخول (Login)
- **POST** `/api/auth/login`
- **Body:**
```json
{
  "username": "string",
  "password": "string"
}
```
- **Response:**
```json
{
  "access_token": "jwt_token",
  "token_type": "bearer"
}
```

---

## المستخدمون (Users)

### إنشاء مستخدم جديد
- **POST** `/api/users/`
- **Headers:** `Authorization: Bearer <token>` (admin فقط)
- **Body:**
```json
{
  "username": "string",
  "password": "string",
  "role": "admin | accountant"
}
```
- **Response:**
```json
{
  "user_id": "uuid",
  "username": "string",
  "role": "string",
  "created_at": "datetime"
}
```

### جلب جميع المستخدمين
- **GET** `/api/users/`
- **Headers:** `Authorization: Bearer <token>` (admin فقط)
- **Response:** قائمة المستخدمين.

---

## الشركات (Companies)

### إنشاء شركة
- **POST** `/api/companies/`
- **Headers:** `Authorization: Bearer <token>`
- **Body:**
```json
{
  "name": "string"
}
```

### جلب جميع الشركات
- **GET** `/api/companies/`
- **Headers:** `Authorization: Bearer <token>`

---

## الحسابات (Accounts)

### إنشاء حساب
- **POST** `/api/accounts/`
- **Headers:** `Authorization: Bearer <token>`
- **Body:**
```json
{
  "company_id": "uuid",
  "name": "string",
  "type": "string",
  "balance": 0
}
```

### جلب حسابات شركة
- **GET** `/api/accounts/?company_id=uuid`
- **Headers:** `Authorization: Bearer <token>`

---

## العمليات المحاسبية (Transactions)

### إنشاء عملية
- **POST** `/api/transactions/`
- **Headers:** `Authorization: Bearer <token>`
- **Body:**
```json
{
  "company_id": "uuid",
  "description": "string"
}
```

### جلب عمليات شركة
- **GET** `/api/transactions/?company_id=uuid`
- **Headers:** `Authorization: Bearer <token>`

---

## الملاحظات
- جميع النقاط (عدا تسجيل الدخول) تتطلب JWT صالح في الهيدر.
- يجب استخدام صلاحيات مناسبة (admin لبعض العمليات).
- جميع الاستجابات بصيغة JSON.

---

**للمزيد من التفاصيل حول كل نقطة، راجع كود الراوترات في مجلد `api/routers/`.** 