"""
نموذج استلام البضائع - يحتوي على جميع المعلومات المتعلقة باستلام المشتريات
"""

from dataclasses import dataclass, field
from typing import Optional, List, Dict, Any
from datetime import datetime, date
from enum import Enum
from decimal import Decimal


class ReceivingStatus(Enum):
    """حالة الاستلام"""
    PENDING = "في الانتظار"
    PARTIALLY_RECEIVED = "مستلم جزئياً"
    FULLY_RECEIVED = "مستلم بالكامل"
    REJECTED = "مرفوض"
    RETURNED = "مرتجع"


class ItemCondition(Enum):
    """حالة الصنف"""
    GOOD = "جيد"
    DAMAGED = "تالف"
    DEFECTIVE = "معيب"
    EXPIRED = "منتهي الصلاحية"


@dataclass
class ReceivingItem:
    """عنصر في استلام البضائع"""
    id: Optional[int] = None
    receiving_id: Optional[int] = None
    purchase_order_item_id: Optional[int] = None
    product_id: Optional[int] = None
    product_code: str = ""
    product_name: str = ""
    
    # الكميات
    quantity_ordered: Decimal = Decimal('0')
    quantity_received: Decimal = Decimal('0')
    quantity_rejected: Decimal = Decimal('0')
    
    # معلومات الجودة
    condition: ItemCondition = ItemCondition.GOOD
    quality_notes: str = ""
    
    # معلومات إضافية
    batch_number: str = ""
    serial_number: str = ""
    expiry_date: Optional[date] = None
    location: str = ""  # موقع التخزين
    
    # ملاحظات
    notes: str = ""


@dataclass
class Receiving:
    """نموذج استلام البضائع الرئيسي"""
    
    # المعلومات الأساسية
    id: Optional[int] = None
    receiving_number: str = ""
    purchase_order_id: Optional[int] = None
    purchase_order_number: str = ""
    supplier_id: Optional[int] = None
    supplier_name: str = ""
    
    # التواريخ
    receiving_date: date = field(default_factory=date.today)
    delivery_date: Optional[date] = None
    
    # الحالة
    status: ReceivingStatus = ReceivingStatus.PENDING
    
    # معلومات التسليم
    delivery_note_number: str = ""
    carrier_name: str = ""
    tracking_number: str = ""
    received_by: str = ""
    
    # العناصر
    items: List[ReceivingItem] = field(default_factory=list)
    
    # ملاحظات
    notes: str = ""
    quality_control_notes: str = ""
    
    # معلومات النظام
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    created_by: str = ""
    updated_by: str = ""
    
    def __post_init__(self):
        """تهيئة إضافية"""
        if not self.created_at:
            self.created_at = datetime.now()
        self.updated_at = datetime.now()
        
        if not self.receiving_number:
            self.receiving_number = self.generate_receiving_number()
    
    def generate_receiving_number(self) -> str:
        """إنشاء رقم الاستلام"""
        today = date.today()
        year = today.year
        month = today.month
        import random
        sequence = random.randint(1000, 9999)
        return f"REC-{year}{month:02d}-{sequence}"
