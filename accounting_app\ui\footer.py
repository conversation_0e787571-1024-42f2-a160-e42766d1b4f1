from PySide6.QtWidgets import QWidget, QHBoxLayout, QLabel
from PySide6.QtCore import Qt
from .toast import show_error_toast
import logging

class Footer(QWidget):
    def __init__(self, event_bus=None, language_controller=None, settings=None, parent=None):
        super().__init__(parent)
        self.event_bus = event_bus
        self.language_controller = language_controller
        self.settings = settings
        self.setObjectName("Footer")
        self.init_ui()
        
        # تطبيق الاتجاه الصحيح عند الإنشاء
        self.update_layout_direction()
        # إذا احتجت event_bus أو language_controller اربطهم هنا

    def init_ui(self):
        layout = QHBoxLayout()
        layout.setContentsMargins(16, 4, 16, 4)
        layout.setSpacing(24)
        self.status_label = QLabel(self.tr("🟢 متصل بقاعدة البيانات"))
        self.user_label = QLabel(self.tr("المستخدم: admin (مدير)"))
        self.version_label = QLabel(self.tr("الإصدار: 1.0.0"))
        self.space_label = QLabel(self.tr("المساحة الحرة: 120GB"))
        self.update_label = QLabel("")
        layout.addWidget(self.status_label)
        layout.addWidget(self.user_label)
        layout.addStretch()
        layout.addWidget(self.version_label)
        layout.addWidget(self.space_label)
        layout.addWidget(self.update_label)
        self.setLayout(layout)
        
        # تحديث اتجاه التخطيط
        self.update_layout_direction()
    
    def update_layout_direction(self):
        """تحديث اتجاه التخطيط للشريط السفلي"""
        try:
            if self.language_controller:
                direction = self.language_controller.get_language_direction(self.language_controller.current_lang)
                self.setLayoutDirection(direction)
                
                # تحديث اتجاه جميع العناصر الفرعية
                for child in self.findChildren(QWidget):
                    child.setLayoutDirection(direction)
                    
                logging.info(f"Footer layout direction updated to: {'RTL' if direction == Qt.RightToLeft else 'LTR'}")
        except Exception as e:
            logging.error(f"Failed to update Footer layout direction: {e}")

    def set_user(self, username, role):
        self.user_label.setText(self.tr("المستخدم: {} ({})").format(username, role))

    def set_db_status(self, connected):
        if connected:
            self.status_label.setText(self.tr("🟢 متصل بقاعدة البيانات"))
        else:
            self.status_label.setText(self.tr("🔴 غير متصل بقاعدة البيانات"))
            show_error_toast(self, self.tr("تعذر الاتصال بقاعدة البيانات"))

    def set_free_space(self, space_gb):
        self.space_label.setText(self.tr("المساحة الحرة: {}GB").format(space_gb))
        if space_gb < 10:
            self.update_label.setText(self.tr("⚠️ المساحة منخفضة!"))
            show_error_toast(self, self.tr("⚠️ المساحة منخفضة جدًا!"))
        else:
            self.update_label.setText("")

    def set_update_available(self, available):
        if available:
            self.update_label.setText(self.tr("🔔 يوجد تحديث متاح!"))
        else:
            self.update_label.setText("")

    def reload_texts(self, lang_code=None):
        """إعادة تحميل النصوص عند تغيير اللغة"""
        try:
            self.status_label.setText(self.tr("🟢 متصل بقاعدة البيانات"))
            self.user_label.setText(self.tr("المستخدم: admin (مدير)"))
            self.version_label.setText(self.tr("الإصدار: 1.0.0"))
            self.space_label.setText(self.tr("المساحة الحرة: 120GB"))
            
            # تحديث اتجاه التخطيط
            self.update_layout_direction()
            
            logging.info(f"Footer texts reloaded for language: {lang_code or 'current'}")
        except Exception as e:
            logging.exception("فشل تحديث نصوص الشريط السفلي")
            show_error_toast(self, self.tr("تعذر تحديث نصوص الشريط السفلي")) 