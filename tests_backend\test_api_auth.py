import sys
import os
import pytest
import uuid

sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from fastapi.testclient import TestClient

# استيراد التطبيق الرئيسي للباكند (يفترض أن يكون اسمه app في ملف api/main.py)
from api.main import app

client = TestClient(app)


def test_login_success():
    response = client.post("/users/login", json={"username": "admin", "password": "admin123"})
    assert response.status_code == 200
    data = response.json()
    assert "access_token" in data
    assert data["token_type"] == "bearer"

def test_register_user():
    username = f"testuser_{uuid.uuid4().hex[:8]}"
    payload = {"username": username, "password": "testpass123", "role": "accountant"}
    response = client.post("/users/register", json=payload)
    assert response.status_code == 200
    data = response.json()
    assert data["username"] == username
    assert data["role"] == "accountant"
    assert "user_id" in data 