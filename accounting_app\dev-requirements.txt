# Development Dependencies - تبعيات التطوير
# قم بتثبيتها في بيئة التطوير فقط

# Production Dependencies - تبعيات الإنتاج
-r requirements.txt

# Testing & Quality Tools - أدوات الاختبار والجودة
pytest
pytest-qt
pytest-cov
coverage

# Code Quality & Formatting - جودة الكود والتنسيق
black==23.11.0
flake8==6.1.0
isort==5.12.0
mypy==1.7.1

# Documentation - التوثيق
sphinx==7.2.6
sphinx-rtd-theme==1.3.0

# Development Utilities - أدوات التطوير
pre-commit==3.5.0
jupyter==1.0.0
ipython==8.17.2

# Optional: UI Development - اختياري: تطوير الواجهة
# QFluentWidgets==1.3.7  # إلغ التعليق إذا كنت تريد استخدام Fluent UI 
jsonschema 