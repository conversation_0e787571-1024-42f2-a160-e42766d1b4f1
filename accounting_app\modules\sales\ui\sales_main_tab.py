from PySide6.QtWidgets import QWidget, QVBoxLayout, QTabWidget
from PySide6.QtCore import Qt

class SalesMainTab(QWidget):
    """التبويب الرئيسي لقسم المبيعات"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("SalesMainTab")
        self.init_ui()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)
        self.tabs = QTabWidget(self)
        
        # إضافة التبويبات الفرعية
        from accounting_app.modules.sales.ui.sales_orders_view import SalesOrdersTab
        self.tabs.addTab(SalesOrdersTab(self), self.tr("أوامر المبيعات"))
        
        # سيتم إضافة هذه التبويبات لاحقاً
        # self.tabs.addTab(SalesInvoicesTab(self), self.tr("فواتير المبيعات"))
        # self.tabs.addTab(SalesReturnsTab(self), self.tr("مرتجعات المبيعات"))
        # self.tabs.addTab(CustomersTab(self), self.tr("إدارة العملاء"))
        # self.tabs.addTab(SalesReportsTab(self), self.tr("تقارير المبيعات"))
        # self.tabs.addTab(DeliveryTab(self), self.tr("إدارة التسليم"))
        # self.tabs.addTab(PromotionsTab(self), self.tr("العروض والخصومات"))
        
        layout.addWidget(self.tabs)
        self.setLayout(layout) 