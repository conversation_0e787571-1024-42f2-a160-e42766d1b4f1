import sys
import os
import pytest

sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from fastapi.testclient import TestClient
from api.main import app
from database.models import Base, Company
from database.config.connection_pool import SessionLocal

client = TestClient(app)

def get_token():
    resp = client.post("/users/login", json={"username": "admin", "password": "admin123"})
    assert resp.status_code == 200
    return resp.json()["access_token"]

def get_demo_company_id():
    db = SessionLocal()
    company = db.query(Company).filter_by(name="Demo Company").first()
    db.close()
    assert company is not None
    return str(company.company_id)

def test_get_transactions():
    token = get_token()
    company_id = get_demo_company_id()
    headers = {"Authorization": f"Bearer {token}"}
    resp = client.get(f"/transactions/company/{company_id}", headers=headers)
    assert resp.status_code == 200
    data = resp.json()
    assert isinstance(data, list)
    assert any(trx["description"] == "Demo Transaction" for trx in data) 