#!/usr/bin/env python3
"""
سكريبت تحويل ملفات الترجمة من .ts إلى .qm
يستخدم Qt Linguist tools لتحويل ملفات الترجمة
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# إعداد المسار الجذر للمشروع
PROJECT_ROOT = Path(__file__).parent.parent
TRANSLATIONS_DIR = PROJECT_ROOT / "translations"

def setup_logging():
    """إعداد نظام التسجيل"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def check_lrelease():
    """التحقق من وجود أداة lrelease"""
    try:
        result = subprocess.run(['lrelease', '--version'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            logging.info("✅ lrelease متوفر")
            return True
        else:
            logging.error("❌ lrelease غير متوفر أو لا يعمل")
            return False
    except FileNotFoundError:
        logging.error("❌ lrelease غير مثبت")
        return False

def convert_ts_to_qm():
    """تحويل جميع ملفات .ts إلى .qm"""
    if not check_lrelease():
        logging.error("لا يمكن المتابعة بدون lrelease")
        return False
    
    ts_files = list(TRANSLATIONS_DIR.glob("*.ts"))
    if not ts_files:
        logging.warning("لا توجد ملفات .ts للتحويل")
        return True
    
    logging.info(f"العثور على {len(ts_files)} ملف .ts للتحويل")
    
    success_count = 0
    for ts_file in ts_files:
        qm_file = ts_file.with_suffix('.qm')
        
        logging.info(f"تحويل {ts_file.name} إلى {qm_file.name}...")
        
        try:
            # تشغيل lrelease
            result = subprocess.run([
                'lrelease',
                str(ts_file),
                '-qm', str(qm_file)
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                logging.info(f"✅ تم تحويل {ts_file.name} بنجاح")
                success_count += 1
            else:
                logging.error(f"❌ فشل في تحويل {ts_file.name}")
                logging.error(f"خطأ: {result.stderr}")
                
        except Exception as e:
            logging.error(f"❌ خطأ في تحويل {ts_file.name}: {e}")
    
    logging.info(f"تم تحويل {success_count}/{len(ts_files)} ملف بنجاح")
    return success_count == len(ts_files)

def verify_qm_files():
    """التحقق من وجود ملفات .qm"""
    qm_files = list(TRANSLATIONS_DIR.glob("*.qm"))
    required_files = ['ar.qm', 'en.qm', 'fr.qm']
    
    logging.info("التحقق من ملفات .qm...")
    
    for required_file in required_files:
        qm_path = TRANSLATIONS_DIR / required_file
        if qm_path.exists():
            file_size = qm_path.stat().st_size
            logging.info(f"✅ {required_file} موجود ({file_size} bytes)")
        else:
            logging.error(f"❌ {required_file} مفقود")
    
    return len(qm_files) >= len(required_files)

def main():
    """الدالة الرئيسية"""
    setup_logging()
    logging.info("🚀 بدء تحويل ملفات الترجمة من .ts إلى .qm")
    
    # التحقق من وجود مجلد الترجمة
    if not TRANSLATIONS_DIR.exists():
        logging.error(f"مجلد الترجمة غير موجود: {TRANSLATIONS_DIR}")
        return False
    
    # تحويل الملفات
    if not convert_ts_to_qm():
        logging.error("فشل في تحويل بعض الملفات")
        return False
    
    # التحقق من النتائج
    if not verify_qm_files():
        logging.error("بعض ملفات .qm مفقودة")
        return False
    
    logging.info("🎉 تم تحويل جميع ملفات الترجمة بنجاح!")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 