"""
Resources package for Smart Accounting App
حزمة الموارد لبرنامج المحاسبة الذكي
"""

import os
from pathlib import Path

# الحصول على مسار مجلد الموارد
RESOURCES_DIR = Path(__file__).parent

def get_resource_path(filename: str) -> str:
    """الحصول على المسار الكامل لملف مورد"""
    return str(RESOURCES_DIR / filename)

def list_resources() -> list:
    """قائمة بجميع الملفات في مجلد الموارد"""
    resources = []
    for file_path in RESOURCES_DIR.iterdir():
        if file_path.is_file() and file_path.name != "__init__.py":
            resources.append(file_path.name)
    return resources

def resource_exists(filename: str) -> bool:
    """التحقق من وجود ملف مورد"""
    return (RESOURCES_DIR / filename).exists() 