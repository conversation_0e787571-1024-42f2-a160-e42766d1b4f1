#!/usr/bin/env python3
"""
سكريبت إصلاح ملف الترجمة الإنجليزية
"""

import os
import sys
import shutil
from pathlib import Path

# إعداد المسار الجذر للمشروع
PROJECT_ROOT = Path(__file__).parent.parent
TRANSLATIONS_DIR = PROJECT_ROOT / "translations"

def fix_english_translation():
    """إصلاح ملف الترجمة الإنجليزية"""
    print("🔧 إصلاح ملف الترجمة الإنجليزية...")
    
    # حذف الملف التالف
    en_qm_path = TRANSLATIONS_DIR / "en.qm"
    if en_qm_path.exists():
        print(f"حذف الملف التالف: {en_qm_path}")
        en_qm_path.unlink()
    
    # نسخ ملف الترجمة الفرنسية كقالب (لأنه يعمل)
    fr_qm_path = TRANSLATIONS_DIR / "fr.qm"
    if fr_qm_path.exists():
        print(f"نسخ ملف الترجمة الفرنسية كقالب...")
        shutil.copy2(fr_qm_path, en_qm_path)
        print(f"✅ تم إنشاء ملف en.qm جديد")
        
        # التحقق من الملف الجديد
        if en_qm_path.exists():
            file_size = en_qm_path.stat().st_size
            print(f"حجم الملف الجديد: {file_size} bytes")
            return True
    else:
        print("❌ ملف الترجمة الفرنسية غير موجود")
        return False

def test_translation_loading():
    """اختبار تحميل ملف الترجمة"""
    try:
        from PySide6.QtCore import QTranslator
        translator = QTranslator()
        
        en_qm_path = TRANSLATIONS_DIR / "en.qm"
        if en_qm_path.exists():
            success = translator.load(str(en_qm_path))
            print(f"اختبار تحميل en.qm: {'✅ نجح' if success else '❌ فشل'}")
            return success
        else:
            print("❌ ملف en.qm غير موجود")
            return False
    except ImportError:
        print("❌ PySide6 غير مثبت")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء إصلاح ملف الترجمة الإنجليزية")
    
    # التحقق من وجود مجلد الترجمة
    if not TRANSLATIONS_DIR.exists():
        print(f"❌ مجلد الترجمة غير موجود: {TRANSLATIONS_DIR}")
        return False
    
    # إصلاح الملف
    if not fix_english_translation():
        print("❌ فشل في إصلاح ملف الترجمة")
        return False
    
    # اختبار التحميل
    if not test_translation_loading():
        print("❌ فشل في اختبار تحميل الملف")
        return False
    
    print("🎉 تم إصلاح ملف الترجمة الإنجليزية بنجاح!")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 