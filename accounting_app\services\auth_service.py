from database.repositories.user_repository import UserRepository
from database.models import <PERSON>pp<PERSON>ser
from sqlalchemy.orm import Session
from jose import JW<PERSON>rror, jwt
from datetime import datetime, timedelta
from accounting_app.core.settings import JWT_SECRET, JWT_ALGORITHM, JWT_EXP_MINUTES
from accounting_app.schemas.user import UserCreate, UserLogin, UserOut
from fastapi import HTTPException, status
import bcrypt

class AuthService:
    def __init__(self, user_repo: UserRepository):
        self.user_repo = user_repo

    def hash_password(self, password: str) -> str:
        return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

    def verify_password(self, password: str, hashed: str) -> bool:
        return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))

    def register_user(self, user_in: UserCreate):
        if self.user_repo.get_by_username(user_in.username):
            raise HTTPException(status_code=status.HTTP_409_CONFLICT, detail='Username already exists')
        hashed = self.hash_password(user_in.password)
        user = AppUser(username=user_in.username, password_hash=hashed, role=user_in.role)
        return self.user_repo.create(user)

    def authenticate_user(self, user_in: UserLogin):
        user = self.user_repo.get_by_username(user_in.username)
        if not user or not self.verify_password(user_in.password, user.password_hash):
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail='Invalid credentials')
        return user

    def create_access_token(self, user: AppUser, expires_delta: timedelta = None):
        now = datetime.utcnow()
        if expires_delta is None:
            expires_delta = timedelta(minutes=JWT_EXP_MINUTES)
        to_encode = {
            "sub": user.username,
            "user_id": user.id,
            "role": user.role,
            "iat": now,
            "nbf": now,
            "exp": now + expires_delta
        }
        return jwt.encode(to_encode, JWT_SECRET, algorithm=JWT_ALGORITHM)

    def decode_access_token(self, token: str):
        try:
            payload = jwt.decode(token, JWT_SECRET, algorithms=[JWT_ALGORITHM])
            return payload
        except JWTError:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail='Invalid token') 