from PySide6.QtWidgets import QWidget, QLabel, QVBoxLayout
from PySide6.QtCore import Qt, QTimer
from .effects import ShadowEffects

class ToastWidget(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.Tool)
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setStyleSheet("background: transparent;")
        self.label = QLabel()
        self.label.setStyleSheet("background: #232e3c; color: #FFF; border-radius: 8px; padding: 12px 24px; font-size: 15px;")
        layout = QVBoxLayout()
        layout.addWidget(self.label)
        layout.setContentsMargins(0, 0, 0, 0)
        self.setLayout(layout)
        self.hide()

    def show_toast(self, text, success=True, duration=2000):
        color = "#22c55e" if success else "#ef4444"
        self.label.setStyleSheet(f"background: {color}; color: #FFF; border-radius: 12px; padding: 16px 32px; font-size: 16px;")
        
        # تطبيق تأثير الظل باستخدام QGraphicsDropShadowEffect
        ShadowEffects.apply_toast_shadow(self.label)
        
        self.label.setText(text)
        self.adjustSize()
        parent_geom = self.parent().geometry() if self.parent() else self.geometry()
        x = parent_geom.x() + (parent_geom.width() - self.width()) // 2
        y = parent_geom.y() + parent_geom.height() - self.height() - 60
        self.move(x, y)
        self.show()
        QTimer.singleShot(duration, self.hide)

# دالة مساعدة لعرض Toast خطأ من أي مكان في الواجهة

def show_error_toast(parent, message: str, duration=4000):
    toast = ToastWidget(parent)
    toast.show_toast(message, success=False, duration=duration) 