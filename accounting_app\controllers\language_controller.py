import logging
import os
from PySide6.QtCore import QTranslator, QLocale, Qt
from PySide6.QtWidgets import QApplication, QWidget
from accounting_app.core.events import AppEvents
from accounting_app.utils import safe_update

class LanguageController:
    def __init__(self, app, event_bus, settings):
        self.app = app
        self.translator = QTranslator()
        self.settings = settings
        self.current_lang = self.settings.language
        self.event_bus = event_bus
        
        # الحصول على مسار التطبيق الحالي
        current_dir = os.path.dirname(os.path.abspath(__file__))
        app_dir = os.path.dirname(current_dir)  # مجلد accounting_app
        
        # خريطة اللغات المدعومة مع مسارات ملفات .qm
        self.lang_map = {
            "ar": (os.path.join(app_dir, "translations", "ar.qm"), Qt.RightToLeft),
            "en": (os.path.join(app_dir, "translations", "en.qm"), Qt.LeftToRight),
            "fr": (os.path.join(app_dir, "translations", "fr.qm"), Qt.LeftToRight)
        }
        
        logging.info(f"LanguageController initialized with default lang: {self.current_lang}")
        logging.info(f"Translation paths: {self.lang_map}")
        self.set_language(self.current_lang, emit_event=False)

    def set_language(self, lang_code, emit_event=True):
        """تغيير اللغة وتحميل ملف الترجمة المناسب"""
        if lang_code not in self.lang_map:
            lang_code = 'ar'  # اللغة الافتراضية العربية
        qm_path, direction = self.lang_map[lang_code]
        
        # إذا كانت اللغة الحالية هي نفسها المطلوبة، فقط عيّن الاتجاه
        if lang_code == self.current_lang:
            try:
                self.app.setLayoutDirection(direction)
                for widget in self.app.topLevelWidgets():
                    try:
                        widget.setLayoutDirection(direction)
                        for child in widget.findChildren(QWidget):
                            try:
                                child.setLayoutDirection(direction)
                            except Exception as e:
                                logging.warning(f"Failed to set child direction: {e}")
                    except Exception as e:
                        logging.warning(f"Failed to set widget direction: {e}")
                logging.info(f"Language {lang_code} already set, direction forced to {'RTL' if direction == Qt.RightToLeft else 'LTR'}.")
            except Exception as e:
                logging.error(f"Failed to set layout direction: {e}")
            return
        
        # إزالة المترجم الحالي
        self.app.removeTranslator(self.translator)
        
        # تحميل المترجم الجديد
        logging.info(f"Attempting to load translation file: {qm_path}")
        if os.path.exists(qm_path):
            logging.info(f"Translation file exists: {qm_path}")
            if self.translator.load(qm_path):
                self.app.installTranslator(self.translator)
                logging.info(f"Language set to {lang_code} using {qm_path}")
            else:
                logging.error(f"Failed to load translation file: {qm_path}")
                # إذا فشل التحميل، استخدم اللغة الافتراضية
                if lang_code != 'ar':
                    self.set_language('ar', emit_event=False)
                    return
        else:
            logging.error(f"Translation file does not exist: {qm_path}")
            # إنشاء ملف .qm فارغ إذا لم يكن موجوداً
            create_empty_qm_file(qm_path, lang_code)
            if self.translator.load(qm_path):
                self.app.installTranslator(self.translator)
                logging.info(f"Created and loaded translation file: {qm_path}")
            else:
                logging.error(f"Failed to create translation file: {qm_path}")
                # إذا فشل التحميل، استخدم اللغة الافتراضية
                if lang_code != 'ar':
                    self.set_language('ar', emit_event=False)
                    return
        
        # تحديث اللغة الحالية أولاً
        self.current_lang = lang_code
        
        # تعيين اتجاه التخطيط للتطبيق بالكامل
        try:
            self.app.setLayoutDirection(direction)
        except Exception as e:
            logging.error(f"Failed to set app layout direction: {e}")
        
        # تحديث اتجاه جميع النوافذ المفتوحة والعناصر
        self._update_all_widgets_direction(direction)
        
        logging.info(f"LanguageController: Language changed to {lang_code}, direction set to {'RTL' if direction == Qt.RightToLeft else 'LTR'}")
        
        # إرسال حدث تغيير اللغة فقط إذا كان مطلوباً
        if emit_event:
            self.event_bus.emit(AppEvents.LANGUAGE_CHANGED, lang_code)
        
        # إعادة ترجمة الواجهة فقط إذا كان emit_event=True
        if emit_event:
            self.retranslateUi()
        
        # إجبار تحديث الواجهة
        self.app.processEvents()
        
        # إعادة تطبيق الأنماط والتخطيط لضمان التحديث الكامل
        try:
            for widget in self.app.topLevelWidgets():
                try:
                    safe_update(widget)
                    # إعادة تطبيق التخطيط إذا كان متاحاً
                    if hasattr(widget, 'updateGeometry'):
                        widget.updateGeometry()
                    # إعادة تطبيق الأنماط إذا كان متاحاً
                    if hasattr(widget, 'style'):
                        widget.style().unpolish(widget)
                        widget.style().polish(widget)
                except Exception as e:
                    logging.warning(f"Failed to update widget {widget.__class__.__name__}: {e}")
                
                # إعادة تطبيق التخطيط للعناصر الفرعية أيضاً
                try:
                    for child in widget.findChildren(QWidget):
                        try:
                            safe_update(child)
                            if hasattr(child, 'updateGeometry'):
                                child.updateGeometry()
                            if hasattr(child, 'style'):
                                child.style().unpolish(child)
                                child.style().polish(child)
                        except Exception as e:
                            logging.warning(f"Failed to update child widget {child.__class__.__name__}: {e}")
                except Exception as e:
                    logging.warning(f"Failed to update child widgets: {e}")
        except Exception as e:
            logging.error(f"Failed to force UI update: {e}")
        
        # إجبار تحديث الشاشة
        try:
            # استخدام processEvents بدلاً من flush
            self.app.processEvents()
        except Exception as e:
            logging.error(f"Failed to process events: {e}")

    def _update_all_widgets_direction(self, direction):
        """تحديث اتجاه جميع العناصر في التطبيق"""
        try:
            # تحديث جميع النوافذ المفتوحة
            for widget in self.app.topLevelWidgets():
                try:
                    widget.setLayoutDirection(direction)
                    
                    # تحديث جميع العناصر الفرعية بشكل متكرر
                    self._update_widget_children_direction(widget, direction)
                except Exception as e:
                    logging.warning(f"Failed to update widget direction for {widget.__class__.__name__}: {e}")
                
            logging.info(f"Updated direction for all widgets to: {'RTL' if direction == Qt.RightToLeft else 'LTR'}")
        except Exception as e:
            logging.error(f"Failed to update all widgets direction: {e}")

    def _update_widget_children_direction(self, widget, direction):
        """تحديث اتجاه جميع العناصر الفرعية بشكل متكرر"""
        try:
            for child in widget.findChildren(QWidget):
                try:
                    child.setLayoutDirection(direction)
                    # تحديث العناصر الفرعية لهذا العنصر أيضاً
                    self._update_widget_children_direction(child, direction)
                except Exception as e:
                    logging.warning(f"Failed to update child direction for {child.__class__.__name__}: {e}")
        except Exception as e:
            logging.error(f"Failed to update widget children direction: {e}")

    def retranslateUi(self):
        """إعادة ترجمة جميع عناصر الواجهة"""
        try:
            # الحصول على النافذة الرئيسية
            main_window = None
            for widget in QApplication.instance().topLevelWidgets():
                if hasattr(widget, 'retranslateUi'):
                    main_window = widget
                    break
            
            if main_window:
                main_window.retranslateUi()
                logging.info("UI retranslated successfully")
            else:
                logging.warning("Main window not found for retranslation")
                
            # إعادة ترجمة جميع النوافذ المفتوحة
            for widget in QApplication.instance().topLevelWidgets():
                if hasattr(widget, 'reload_texts'):
                    try:
                        widget.reload_texts()
                        logging.info(f"Reloaded texts for widget: {widget.__class__.__name__}")
                    except Exception as e:
                        logging.error(f"Failed to reload texts for widget {widget.__class__.__name__}: {e}")
                
        except Exception as e:
            logging.error(f"Error during UI retranslation: {e}")

    def get_current_language(self):
        """الحصول على اللغة الحالية"""
        return self.current_lang

    def get_supported_languages(self):
        """الحصول على قائمة اللغات المدعومة"""
        return list(self.lang_map.keys())

    def get_language_direction(self, lang_code):
        """الحصول على اتجاه اللغة"""
        if lang_code in self.lang_map:
            return self.lang_map[lang_code][1]  # العنصر الثاني هو الاتجاه
        return Qt.LeftToRight
def create_empty_qm_file(qm_path, lang_code):
    """إنشاء ملف .qm فارغ صالح"""
    try:
        # محتوى ملف .qm فارغ صالح لـ Qt
        # هذا محتوى ثنائي بسيط لملف .qm فارغ
        qm_content = (
            b'QM\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
            b'\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
            b'\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
            b'\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
        )
        
        with open(qm_path, 'wb') as f:
            f.write(qm_content)
        
        logging.info(f"Created empty translation file: {qm_path}")
        return True
    except Exception as e:
        logging.error(f"Failed to create translation file {qm_path}: {e}")
        return False

