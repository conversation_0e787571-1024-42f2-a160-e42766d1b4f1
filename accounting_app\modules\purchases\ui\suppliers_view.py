"""
واجهة إدارة الموردين - واجهة شاملة لإدارة جميع معلومات الموردين
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QPushButton, QLineEdit, QComboBox, QLabel, QGroupBox, QFormLayout,
    QDialog, QDialogButtonBox, QTabWidget, QTextEdit, QSpinBox,
    QDoubleSpinBox, QDateEdit, QCheckBox, QListWidget, QListWidgetItem,
    QMessageBox, QHeaderView, QFrame, QScrollArea, QGridLayout,
    QSplitter, QTreeWidget, QTreeWidgetItem, QProgressBar, QSlider
)
from PySide6.QtCore import Qt, QDate, Signal
from PySide6.QtGui import QIcon, QPixmap, QFont
from datetime import datetime, date
import logging

# استيراد النماذج
from ..models.supplier import (
    Supplier, SupplierContact, SupplierAddress, SupplierBankAccount,
    SupplierDocument, SupplierRating, SupplierStatus, SupplierType, PaymentTerms
)

# استيراد نافذة حوار المورد
from .supplier_dialog import SupplierDialog


class SuppliersTab(QWidget):
    """التبويب الرئيسي لإدارة الموردين"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("SuppliersTab")
        self.suppliers_data = []  # بيانات وهمية للاختبار
        self.init_ui()
        self.load_sample_data()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)
        
        # شريط الأدوات العلوي
        toolbar = self.create_toolbar()
        layout.addWidget(toolbar)
        
        # منطقة البحث والفلترة
        search_area = self.create_search_area()
        layout.addWidget(search_area)
        
        # المحتوى الرئيسي - مقسم إلى قائمة الموردين وتفاصيل المورد
        splitter = QSplitter(Qt.Horizontal)
        
        # قائمة الموردين (الجانب الأيسر)
        suppliers_list = self.create_suppliers_list()
        splitter.addWidget(suppliers_list)
        
        # تفاصيل المورد (الجانب الأيمن)
        supplier_details = self.create_supplier_details()
        splitter.addWidget(supplier_details)
        
        # تعيين النسب
        splitter.setSizes([400, 600])
        layout.addWidget(splitter)
        
        # شريط الحالة
        status_bar = self.create_status_bar()
        layout.addWidget(status_bar)
    
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar = QFrame()
        toolbar.setFrameStyle(QFrame.StyledPanel)
        layout = QHBoxLayout(toolbar)
        
        # أزرار العمليات الرئيسية
        self.add_supplier_btn = QPushButton(self.tr("إضافة مورد جديد"))
        self.add_supplier_btn.setIcon(QIcon("accounting_app/resources/icons/add.svg"))
        self.add_supplier_btn.clicked.connect(self.add_new_supplier)
        
        self.edit_supplier_btn = QPushButton(self.tr("تعديل"))
        self.edit_supplier_btn.setIcon(QIcon("accounting_app/resources/icons/edit.svg"))
        self.edit_supplier_btn.clicked.connect(self.edit_supplier)
        self.edit_supplier_btn.setEnabled(False)
        
        self.delete_supplier_btn = QPushButton(self.tr("حذف"))
        self.delete_supplier_btn.setIcon(QIcon("accounting_app/resources/icons/delete.svg"))
        self.delete_supplier_btn.clicked.connect(self.delete_supplier)
        self.delete_supplier_btn.setEnabled(False)
        
        # أزرار إضافية
        self.import_btn = QPushButton(self.tr("استيراد"))
        self.export_btn = QPushButton(self.tr("تصدير"))
        self.print_btn = QPushButton(self.tr("طباعة"))
        
        # إضافة الأزرار
        layout.addWidget(self.add_supplier_btn)
        layout.addWidget(self.edit_supplier_btn)
        layout.addWidget(self.delete_supplier_btn)
        layout.addWidget(QFrame())  # فاصل
        layout.addWidget(self.import_btn)
        layout.addWidget(self.export_btn)
        layout.addWidget(self.print_btn)
        layout.addStretch()
        
        return toolbar
    
    def create_search_area(self):
        """إنشاء منطقة البحث والفلترة"""
        search_group = QGroupBox(self.tr("البحث والفلترة"))
        layout = QHBoxLayout(search_group)
        
        # حقل البحث
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText(self.tr("البحث في الموردين..."))
        self.search_input.textChanged.connect(self.filter_suppliers)
        
        # فلتر الحالة
        self.status_filter = QComboBox()
        self.status_filter.addItem(self.tr("جميع الحالات"), "")
        for status in SupplierStatus:
            self.status_filter.addItem(status.value, status.name)
        self.status_filter.currentTextChanged.connect(self.filter_suppliers)
        
        # فلتر النوع
        self.type_filter = QComboBox()
        self.type_filter.addItem(self.tr("جميع الأنواع"), "")
        for supplier_type in SupplierType:
            self.type_filter.addItem(supplier_type.value, supplier_type.name)
        self.type_filter.currentTextChanged.connect(self.filter_suppliers)
        
        # فلتر المدينة
        self.city_filter = QComboBox()
        self.city_filter.addItem(self.tr("جميع المدن"), "")
        self.city_filter.currentTextChanged.connect(self.filter_suppliers)
        
        layout.addWidget(QLabel(self.tr("البحث:")))
        layout.addWidget(self.search_input)
        layout.addWidget(QLabel(self.tr("الحالة:")))
        layout.addWidget(self.status_filter)
        layout.addWidget(QLabel(self.tr("النوع:")))
        layout.addWidget(self.type_filter)
        layout.addWidget(QLabel(self.tr("المدينة:")))
        layout.addWidget(self.city_filter)
        layout.addStretch()
        
        return search_group
    
    def create_suppliers_list(self):
        """إنشاء قائمة الموردين"""
        list_group = QGroupBox(self.tr("قائمة الموردين"))
        layout = QVBoxLayout(list_group)
        
        # جدول الموردين
        self.suppliers_table = QTableWidget()
        self.suppliers_table.setColumnCount(6)
        self.suppliers_table.setHorizontalHeaderLabels([
            self.tr("رمز المورد"),
            self.tr("اسم المورد"),
            self.tr("النوع"),
            self.tr("الحالة"),
            self.tr("الهاتف"),
            self.tr("التقييم")
        ])
        
        # تخصيص الجدول
        header = self.suppliers_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.Interactive)
        
        self.suppliers_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.suppliers_table.setSelectionMode(QTableWidget.SingleSelection)
        self.suppliers_table.setAlternatingRowColors(True)
        self.suppliers_table.itemSelectionChanged.connect(self.on_supplier_selected)
        
        layout.addWidget(self.suppliers_table)
        
        # معلومات إحصائية سريعة
        stats_layout = QHBoxLayout()
        self.total_suppliers_label = QLabel(self.tr("إجمالي الموردين: 0"))
        self.active_suppliers_label = QLabel(self.tr("الموردين النشطين: 0"))
        
        stats_layout.addWidget(self.total_suppliers_label)
        stats_layout.addWidget(self.active_suppliers_label)
        stats_layout.addStretch()
        
        layout.addLayout(stats_layout)
        
        return list_group
    
    def create_supplier_details(self):
        """إنشاء منطقة تفاصيل المورد"""
        details_group = QGroupBox(self.tr("تفاصيل المورد"))
        layout = QVBoxLayout(details_group)
        
        # تبويبات التفاصيل
        self.details_tabs = QTabWidget()
        
        # تبويب المعلومات الأساسية
        basic_tab = self.create_basic_info_tab()
        self.details_tabs.addTab(basic_tab, self.tr("المعلومات الأساسية"))
        
        # تبويب جهات الاتصال
        contacts_tab = self.create_contacts_tab()
        self.details_tabs.addTab(contacts_tab, self.tr("جهات الاتصال"))
        
        # تبويب العناوين
        addresses_tab = self.create_addresses_tab()
        self.details_tabs.addTab(addresses_tab, self.tr("العناوين"))
        
        # تبويب المعلومات المالية
        financial_tab = self.create_financial_tab()
        self.details_tabs.addTab(financial_tab, self.tr("المعلومات المالية"))
        
        # تبويب التقييم والأداء
        performance_tab = self.create_performance_tab()
        self.details_tabs.addTab(performance_tab, self.tr("التقييم والأداء"))
        
        # تبويب الوثائق
        documents_tab = self.create_documents_tab()
        self.details_tabs.addTab(documents_tab, self.tr("الوثائق"))
        
        layout.addWidget(self.details_tabs)
        
        return details_group
    
    def create_basic_info_tab(self):
        """إنشاء تبويب المعلومات الأساسية"""
        tab = QWidget()
        scroll = QScrollArea(tab)
        scroll.setWidgetResizable(True)
        
        container = QWidget()
        layout = QFormLayout(container)
        
        # المعلومات الأساسية
        self.supplier_code_label = QLabel()
        self.supplier_name_label = QLabel()
        self.supplier_name_english_label = QLabel()
        self.supplier_type_label = QLabel()
        self.supplier_status_label = QLabel()
        
        # المعلومات الضريبية
        self.tax_number_label = QLabel()
        self.commercial_registration_label = QLabel()
        self.is_tax_exempt_label = QLabel()
        
        # معلومات إضافية
        self.categories_label = QLabel()
        self.lead_time_label = QLabel()
        self.minimum_order_label = QLabel()
        self.discount_percentage_label = QLabel()
        
        # إضافة الحقول
        layout.addRow(self.tr("رمز المورد:"), self.supplier_code_label)
        layout.addRow(self.tr("اسم المورد:"), self.supplier_name_label)
        layout.addRow(self.tr("الاسم بالإنجليزية:"), self.supplier_name_english_label)
        layout.addRow(self.tr("نوع المورد:"), self.supplier_type_label)
        layout.addRow(self.tr("الحالة:"), self.supplier_status_label)
        layout.addRow(self.tr("الرقم الضريبي:"), self.tax_number_label)
        layout.addRow(self.tr("السجل التجاري:"), self.commercial_registration_label)
        layout.addRow(self.tr("معفي من الضريبة:"), self.is_tax_exempt_label)
        layout.addRow(self.tr("الفئات:"), self.categories_label)
        layout.addRow(self.tr("مدة التسليم (أيام):"), self.lead_time_label)
        layout.addRow(self.tr("الحد الأدنى للطلب:"), self.minimum_order_label)
        layout.addRow(self.tr("نسبة الخصم:"), self.discount_percentage_label)
        
        scroll.setWidget(container)
        
        tab_layout = QVBoxLayout(tab)
        tab_layout.addWidget(scroll)
        
        return tab
    
    def create_contacts_tab(self):
        """إنشاء تبويب جهات الاتصال"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # جدول جهات الاتصال
        self.contacts_table = QTableWidget()
        self.contacts_table.setColumnCount(5)
        self.contacts_table.setHorizontalHeaderLabels([
            self.tr("الاسم"),
            self.tr("المنصب"),
            self.tr("الهاتف"),
            self.tr("الجوال"),
            self.tr("البريد الإلكتروني")
        ])
        
        layout.addWidget(self.contacts_table)
        
        return tab
    
    def create_addresses_tab(self):
        """إنشاء تبويب العناوين"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # جدول العناوين
        self.addresses_table = QTableWidget()
        self.addresses_table.setColumnCount(6)
        self.addresses_table.setHorizontalHeaderLabels([
            self.tr("النوع"),
            self.tr("الشارع"),
            self.tr("المدينة"),
            self.tr("المنطقة"),
            self.tr("الرمز البريدي"),
            self.tr("الدولة")
        ])
        
        layout.addWidget(self.addresses_table)
        
        return tab
    
    def create_financial_tab(self):
        """إنشاء تبويب المعلومات المالية"""
        tab = QWidget()
        layout = QFormLayout(tab)
        
        # المعلومات المالية
        self.payment_terms_label = QLabel()
        self.credit_limit_label = QLabel()
        self.current_balance_label = QLabel()
        self.currency_label = QLabel()
        self.total_orders_label = QLabel()
        self.total_purchases_label = QLabel()
        
        layout.addRow(self.tr("شروط الدفع:"), self.payment_terms_label)
        layout.addRow(self.tr("حد الائتمان:"), self.credit_limit_label)
        layout.addRow(self.tr("الرصيد الحالي:"), self.current_balance_label)
        layout.addRow(self.tr("العملة:"), self.currency_label)
        layout.addRow(self.tr("إجمالي الطلبات:"), self.total_orders_label)
        layout.addRow(self.tr("إجمالي المشتريات:"), self.total_purchases_label)
        
        return tab
    
    def create_performance_tab(self):
        """إنشاء تبويب التقييم والأداء"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # مؤشرات الأداء
        performance_group = QGroupBox(self.tr("مؤشرات الأداء"))
        performance_layout = QGridLayout(performance_group)
        
        # تقييم الجودة
        quality_label = QLabel(self.tr("تقييم الجودة:"))
        self.quality_rating_bar = QProgressBar()
        self.quality_rating_bar.setMaximum(5)
        self.quality_rating_label = QLabel("0/5")
        
        # تقييم التسليم
        delivery_label = QLabel(self.tr("تقييم التسليم:"))
        self.delivery_rating_bar = QProgressBar()
        self.delivery_rating_bar.setMaximum(5)
        self.delivery_rating_label = QLabel("0/5")
        
        # تقييم الخدمة
        service_label = QLabel(self.tr("تقييم الخدمة:"))
        self.service_rating_bar = QProgressBar()
        self.service_rating_bar.setMaximum(5)
        self.service_rating_label = QLabel("0/5")
        
        # التقييم الإجمالي
        overall_label = QLabel(self.tr("التقييم الإجمالي:"))
        self.overall_rating_bar = QProgressBar()
        self.overall_rating_bar.setMaximum(5)
        self.overall_rating_label = QLabel("0/5")
        
        # إضافة العناصر
        performance_layout.addWidget(quality_label, 0, 0)
        performance_layout.addWidget(self.quality_rating_bar, 0, 1)
        performance_layout.addWidget(self.quality_rating_label, 0, 2)
        
        performance_layout.addWidget(delivery_label, 1, 0)
        performance_layout.addWidget(self.delivery_rating_bar, 1, 1)
        performance_layout.addWidget(self.delivery_rating_label, 1, 2)
        
        performance_layout.addWidget(service_label, 2, 0)
        performance_layout.addWidget(self.service_rating_bar, 2, 1)
        performance_layout.addWidget(self.service_rating_label, 2, 2)
        
        performance_layout.addWidget(overall_label, 3, 0)
        performance_layout.addWidget(self.overall_rating_bar, 3, 1)
        performance_layout.addWidget(self.overall_rating_label, 3, 2)
        
        layout.addWidget(performance_group)
        layout.addStretch()
        
        return tab
    
    def create_documents_tab(self):
        """إنشاء تبويب الوثائق"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # جدول الوثائق
        self.documents_table = QTableWidget()
        self.documents_table.setColumnCount(5)
        self.documents_table.setHorizontalHeaderLabels([
            self.tr("نوع الوثيقة"),
            self.tr("رقم الوثيقة"),
            self.tr("تاريخ الإصدار"),
            self.tr("تاريخ الانتهاء"),
            self.tr("ملاحظات")
        ])
        
        layout.addWidget(self.documents_table)
        
        return tab
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_bar = QFrame()
        status_bar.setFrameStyle(QFrame.StyledPanel)
        layout = QHBoxLayout(status_bar)
        
        self.status_label = QLabel(self.tr("جاهز"))
        layout.addWidget(self.status_label)
        layout.addStretch()
        
        return status_bar

    def load_sample_data(self):
        """تحميل بيانات وهمية للاختبار"""
        # إنشاء بيانات وهمية للموردين
        sample_suppliers = [
            {
                'supplier_code': 'SUP-ABC-1001',
                'name': 'شركة المواد الغذائية المتقدمة',
                'name_english': 'Advanced Food Materials Co.',
                'supplier_type': SupplierType.COMPANY,
                'status': SupplierStatus.ACTIVE,
                'phone': '0112345678',
                'rating': 4.5,
                'city': 'الرياض'
            },
            {
                'supplier_code': 'SUP-XYZ-1002',
                'name': 'مؤسسة التقنية الحديثة',
                'name_english': 'Modern Technology Est.',
                'supplier_type': SupplierType.COMPANY,
                'status': SupplierStatus.ACTIVE,
                'phone': '0123456789',
                'rating': 4.2,
                'city': 'جدة'
            },
            {
                'supplier_code': 'SUP-DEF-1003',
                'name': 'أحمد محمد للتجارة',
                'name_english': 'Ahmed Mohammed Trading',
                'supplier_type': SupplierType.INDIVIDUAL,
                'status': SupplierStatus.INACTIVE,
                'phone': '0134567890',
                'rating': 3.8,
                'city': 'الدمام'
            }
        ]

        self.suppliers_data = sample_suppliers
        self.populate_suppliers_table()
        self.update_statistics()
        self.populate_city_filter()

    def populate_suppliers_table(self):
        """ملء جدول الموردين"""
        self.suppliers_table.setRowCount(len(self.suppliers_data))

        for row, supplier in enumerate(self.suppliers_data):
            # رمز المورد
            self.suppliers_table.setItem(row, 0, QTableWidgetItem(supplier['supplier_code']))

            # اسم المورد
            self.suppliers_table.setItem(row, 1, QTableWidgetItem(supplier['name']))

            # النوع
            self.suppliers_table.setItem(row, 2, QTableWidgetItem(supplier['supplier_type'].value))

            # الحالة
            status_item = QTableWidgetItem(supplier['status'].value)
            if supplier['status'] == SupplierStatus.ACTIVE:
                status_item.setBackground(Qt.green)
            elif supplier['status'] == SupplierStatus.INACTIVE:
                status_item.setBackground(Qt.yellow)
            else:
                status_item.setBackground(Qt.red)
            self.suppliers_table.setItem(row, 3, status_item)

            # الهاتف
            self.suppliers_table.setItem(row, 4, QTableWidgetItem(supplier['phone']))

            # التقييم
            rating_item = QTableWidgetItem(f"{supplier['rating']}/5")
            self.suppliers_table.setItem(row, 5, rating_item)

    def populate_city_filter(self):
        """ملء فلتر المدن"""
        cities = set(supplier['city'] for supplier in self.suppliers_data)
        for city in sorted(cities):
            self.city_filter.addItem(city, city)

    def update_statistics(self):
        """تحديث الإحصائيات"""
        total = len(self.suppliers_data)
        active = sum(1 for s in self.suppliers_data if s['status'] == SupplierStatus.ACTIVE)

        self.total_suppliers_label.setText(self.tr(f"إجمالي الموردين: {total}"))
        self.active_suppliers_label.setText(self.tr(f"الموردين النشطين: {active}"))

    def filter_suppliers(self):
        """فلترة الموردين حسب معايير البحث"""
        search_text = self.search_input.text().lower()
        status_filter = self.status_filter.currentData()
        type_filter = self.type_filter.currentData()
        city_filter = self.city_filter.currentData()

        for row in range(self.suppliers_table.rowCount()):
            show_row = True

            # فلترة النص
            if search_text:
                supplier_name = self.suppliers_table.item(row, 1).text().lower()
                supplier_code = self.suppliers_table.item(row, 0).text().lower()
                if search_text not in supplier_name and search_text not in supplier_code:
                    show_row = False

            # فلترة الحالة
            if status_filter and show_row:
                supplier_status = self.suppliers_table.item(row, 3).text()
                if supplier_status != SupplierStatus[status_filter].value:
                    show_row = False

            # فلترة النوع
            if type_filter and show_row:
                supplier_type = self.suppliers_table.item(row, 2).text()
                if supplier_type != SupplierType[type_filter].value:
                    show_row = False

            # فلترة المدينة
            if city_filter and show_row:
                # هنا نحتاج للبحث في البيانات الأصلية
                supplier_code = self.suppliers_table.item(row, 0).text()
                supplier_data = next((s for s in self.suppliers_data if s['supplier_code'] == supplier_code), None)
                if supplier_data and supplier_data['city'] != city_filter:
                    show_row = False

            self.suppliers_table.setRowHidden(row, not show_row)

    def on_supplier_selected(self):
        """عند اختيار مورد من القائمة"""
        current_row = self.suppliers_table.currentRow()
        if current_row >= 0:
            # تفعيل أزرار التعديل والحذف
            self.edit_supplier_btn.setEnabled(True)
            self.delete_supplier_btn.setEnabled(True)

            # عرض تفاصيل المورد
            supplier_code = self.suppliers_table.item(current_row, 0).text()
            self.display_supplier_details(supplier_code)
        else:
            self.edit_supplier_btn.setEnabled(False)
            self.delete_supplier_btn.setEnabled(False)

    def display_supplier_details(self, supplier_code):
        """عرض تفاصيل المورد المختار"""
        supplier_data = next((s for s in self.suppliers_data if s['supplier_code'] == supplier_code), None)
        if not supplier_data:
            return

        # تحديث المعلومات الأساسية
        self.supplier_code_label.setText(supplier_data['supplier_code'])
        self.supplier_name_label.setText(supplier_data['name'])
        self.supplier_name_english_label.setText(supplier_data.get('name_english', ''))
        self.supplier_type_label.setText(supplier_data['supplier_type'].value)
        self.supplier_status_label.setText(supplier_data['status'].value)

        # تحديث مؤشرات الأداء
        rating = supplier_data.get('rating', 0)
        self.quality_rating_bar.setValue(int(rating))
        self.quality_rating_label.setText(f"{rating}/5")
        self.delivery_rating_bar.setValue(int(rating))
        self.delivery_rating_label.setText(f"{rating}/5")
        self.service_rating_bar.setValue(int(rating))
        self.service_rating_label.setText(f"{rating}/5")
        self.overall_rating_bar.setValue(int(rating))
        self.overall_rating_label.setText(f"{rating}/5")

    def add_new_supplier(self):
        """إضافة مورد جديد"""
        dialog = SupplierDialog(self)
        if dialog.exec() == QDialog.Accepted:
            supplier_data = dialog.get_supplier_data()
            self.suppliers_data.append(supplier_data)
            self.populate_suppliers_table()
            self.update_statistics()
            self.status_label.setText(self.tr("تم إضافة مورد جديد بنجاح"))

    def edit_supplier(self):
        """تعديل المورد المختار"""
        current_row = self.suppliers_table.currentRow()
        if current_row >= 0:
            supplier_code = self.suppliers_table.item(current_row, 0).text()
            supplier_data = next((s for s in self.suppliers_data if s['supplier_code'] == supplier_code), None)

            if supplier_data:
                dialog = SupplierDialog(self, supplier_data)
                if dialog.exec() == QDialog.Accepted:
                    updated_data = dialog.get_supplier_data()
                    # تحديث البيانات
                    for i, supplier in enumerate(self.suppliers_data):
                        if supplier['supplier_code'] == supplier_code:
                            self.suppliers_data[i] = updated_data
                            break

                    self.populate_suppliers_table()
                    self.display_supplier_details(updated_data['supplier_code'])
                    self.status_label.setText(self.tr("تم تحديث بيانات المورد بنجاح"))

    def delete_supplier(self):
        """حذف المورد المختار"""
        current_row = self.suppliers_table.currentRow()
        if current_row >= 0:
            supplier_name = self.suppliers_table.item(current_row, 1).text()

            reply = QMessageBox.question(
                self,
                self.tr("تأكيد الحذف"),
                self.tr(f"هل أنت متأكد من حذف المورد '{supplier_name}'؟"),
                QMessageBox.Yes | QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                supplier_code = self.suppliers_table.item(current_row, 0).text()
                self.suppliers_data = [s for s in self.suppliers_data if s['supplier_code'] != supplier_code]
                self.populate_suppliers_table()
                self.update_statistics()
                self.status_label.setText(self.tr("تم حذف المورد بنجاح"))

                # إخفاء التفاصيل
                self.edit_supplier_btn.setEnabled(False)
                self.delete_supplier_btn.setEnabled(False)
