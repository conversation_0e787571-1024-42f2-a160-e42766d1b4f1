from sqlalchemy.orm import Session
from database.models import AppUser
from sqlalchemy.exc import NoResultFound

class UserRepository:
    def __init__(self, db: Session):
        self.db = db

    def get_by_id(self, user_id):
        # يمكن استخدام scalar_one_or_none() لمزيد من الصرامة
        return self.db.query(AppUser).filter(AppUser.user_id == user_id).first()

    def get_by_username(self, username):
        # يمكن استخدام scalar_one_or_none() لمزيد من الصرامة
        return self.db.query(AppUser).filter(AppUser.username == username).first()

    def create(self, user: AppUser):
        self.db.add(user)
        self.db.commit()
        self.db.refresh(user)
        return user

    def update(self, user: AppUser, updates: dict = None):
        """
        إذا تم تمرير updates (dict)، سيتم تحديث الحقول تلقائيًا.
        إذا لم يتم تمرير updates، يجب أن يكون user معدّل مسبقًا.
        """
        if updates:
            for field, value in updates.items():
                setattr(user, field, value)
        self.db.commit()
        self.db.refresh(user)
        return user

    def delete(self, user: AppUser) -> bool:
        """
        يحذف المستخدم إذا كان موجودًا. يرجع True إذا تم الحذف، False إذا كان الكائن None.
        """
        if not user:
            return False
        self.db.delete(user)
        self.db.commit()
        return True 