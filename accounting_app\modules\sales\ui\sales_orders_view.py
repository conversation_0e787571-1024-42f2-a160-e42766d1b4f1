from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLineEdit, QTableWidget, QTableWidgetItem, 
    QHeaderView, QPushButton, QDialog, QFormLayout, QDialogButtonBox, QComboBox, 
    QMessageBox, QLabel, QCheckBox, QTextEdit, QGroupBox, QFrame, QScrollArea, 
    QToolButton, QSizePolicy, QDateEdit, QTabWidget, QToolTip, QGridLayout, 
    QSpacerItem, QSpinBox, QDoubleSpinBox
)
from PySide6.QtCore import Qt, QDate, QParallelAnimationGroup, QAbstractAnimation, QEasingCurve, QPropertyAnimation, QPoint, QSize
from PySide6.QtGui import QPixmap, QIntValidator, QDoubleValidator, QIcon, QPainter, QColor, QPolygon
from datetime import datetime, timedelta
import logging

class CollapsibleGroupBox(QWidget):
    """مجموعة قابلة للطي مع عنوان وزر توسيع/طي."""
    def __init__(self, title, parent=None, expanded=False):
        super().__init__(parent)
        self.toggle_button = QToolButton(text=title, checkable=True, checked=expanded)
        self.toggle_button.setStyleSheet("QToolButton { border: none; font-weight: bold; font-size: 15px; text-align: right; }")
        self.toggle_button.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)
        self.toggle_button.setArrowType(Qt.DownArrow if expanded else Qt.RightArrow)
        self.toggle_button.clicked.connect(self.on_toggle)

        self.content_area = QWidget()
        self.content_area.setMaximumHeight(0 if not expanded else 16777215)
        self.content_area.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)

        self.anim = QPropertyAnimation(self.content_area, b"maximumHeight")
        self.anim.setDuration(200)
        self.anim.setEasingCurve(QEasingCurve.InOutCubic)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.addWidget(self.toggle_button)
        layout.addWidget(self.content_area)
        self.setLayout(layout)

    def setContentLayout(self, content_layout):
        self.content_area.setLayout(content_layout)
        self.content_area.setMaximumHeight(content_layout.sizeHint().height() if self.toggle_button.isChecked() else 0)

    def on_toggle(self):
        checked = self.toggle_button.isChecked()
        self.toggle_button.setArrowType(Qt.DownArrow if checked else Qt.RightArrow)
        content_height = self.content_area.layout().sizeHint().height()
        start_value = self.content_area.maximumHeight()
        end_value = content_height if checked else 0
        self.anim.stop()
        self.anim.setStartValue(start_value)
        self.anim.setEndValue(end_value)
        self.anim.start()

class SalesOrderDialog(QDialog):
    """نافذة إنشاء/تعديل أمر مبيعات"""
    
    def __init__(self, parent=None, order=None, customers=None, products=None):
        super().__init__(parent)
        self.setWindowTitle(self.tr("إنشاء أمر مبيعات جديد"))
        self.setMinimumWidth(900)
        self.setMinimumHeight(700)
        self.order = order
        self.customers = customers or [
            {"id": 1, "name": "أحمد محمد", "phone": "0501234567", "email": "<EMAIL>"},
            {"id": 2, "name": "سارة أحمد", "phone": "0507654321", "email": "<EMAIL>"},
            {"id": 3, "name": "محمد علي", "phone": "0509876543", "email": "<EMAIL>"}
        ]
        self.products = products or [
            {"id": 1, "name": "منتج 1", "code": "P001", "price": 100.0, "stock": 50},
            {"id": 2, "name": "منتج 2", "code": "P002", "price": 150.0, "stock": 30},
            {"id": 3, "name": "منتج 3", "code": "P003", "price": 200.0, "stock": 25}
        ]
        self.order_items = []
        self.init_ui()
        
        if self.order:
            self.set_order_data(self.order)

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setLayoutDirection(Qt.RightToLeft)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        
        # استخدام QTabWidget لتنظيم المحتوى
        self.tab_widget = QTabWidget(self)
        
        # تبويب المعلومات الأساسية
        basic_tab = self.create_basic_info_tab()
        self.tab_widget.addTab(basic_tab, self.tr("المعلومات الأساسية"))
        
        # تبويب المنتجات
        products_tab = self.create_products_tab()
        self.tab_widget.addTab(products_tab, self.tr("المنتجات"))
        
        # تبويب التسليم
        delivery_tab = self.create_delivery_tab()
        self.tab_widget.addTab(delivery_tab, self.tr("التسليم"))
        
        # تبويب الدفع
        payment_tab = self.create_payment_tab()
        self.tab_widget.addTab(payment_tab, self.tr("الدفع"))
        
        main_layout.addWidget(self.tab_widget)
        
        # ملخص الطلب
        summary_group = QGroupBox(self.tr("ملخص الطلب"))
        summary_layout = QHBoxLayout(summary_group)
        
        self.subtotal_label = QLabel(self.tr("المجموع الفرعي: 0.00"))
        self.discount_label = QLabel(self.tr("الخصم: 0.00"))
        self.tax_label = QLabel(self.tr("الضريبة: 0.00"))
        self.shipping_label = QLabel(self.tr("الشحن: 0.00"))
        self.total_label = QLabel(self.tr("المجموع الكلي: 0.00"))
        self.total_label.setStyleSheet("font-weight: bold; font-size: 14px; color: #2563eb;")
        
        summary_layout.addWidget(self.subtotal_label)
        summary_layout.addWidget(self.discount_label)
        summary_layout.addWidget(self.tax_label)
        summary_layout.addWidget(self.shipping_label)
        summary_layout.addWidget(self.total_label)
        
        main_layout.addWidget(summary_group)
        
        # أزرار الحفظ والإلغاء
        button_box = QDialogButtonBox(
            QDialogButtonBox.Save | QDialogButtonBox.Cancel
        )
        button_box.accepted.connect(self.save_order)
        button_box.rejected.connect(self.reject)
        main_layout.addWidget(button_box)

    def create_basic_info_tab(self):
        """إنشاء تبويب المعلومات الأساسية"""
        tab = QWidget()
        scroll = QScrollArea(tab)
        scroll.setWidgetResizable(True)
        container = QWidget()
        layout = QVBoxLayout(container)
        
        # معلومات العميل
        customer_group = CollapsibleGroupBox(self.tr("معلومات العميل"), container, expanded=True)
        customer_layout = QFormLayout()
        
        self.customer_combo = QComboBox(self)
        for customer in self.customers:
            self.customer_combo.addItem(f"{customer['name']} - {customer['phone']}", customer['id'])
        self.customer_combo.currentIndexChanged.connect(self.on_customer_changed)
        customer_layout.addRow(self.tr("العميل:"), self.customer_combo)
        
        self.customer_phone_edit = QLineEdit(self)
        self.customer_phone_edit.setReadOnly(True)
        customer_layout.addRow(self.tr("الهاتف:"), self.customer_phone_edit)
        
        self.customer_email_edit = QLineEdit(self)
        self.customer_email_edit.setReadOnly(True)
        customer_layout.addRow(self.tr("البريد الإلكتروني:"), self.customer_email_edit)
        
        customer_group.setContentLayout(customer_layout)
        layout.addWidget(customer_group)
        
        # معلومات الطلب
        order_group = CollapsibleGroupBox(self.tr("معلومات الطلب"), container, expanded=True)
        order_layout = QFormLayout()
        
        self.order_date_edit = QDateEdit(self)
        self.order_date_edit.setDate(QDate.currentDate())
        order_layout.addRow(self.tr("تاريخ الطلب:"), self.order_date_edit)
        
        self.order_status_combo = QComboBox(self)
        self.order_status_combo.addItems([
            self.tr("مسودة"),
            self.tr("مؤكد"),
            self.tr("قيد التحضير"),
            self.tr("جاهز للتسليم"),
            self.tr("مسلم"),
            self.tr("ملغي")
        ])
        order_layout.addRow(self.tr("حالة الطلب:"), self.order_status_combo)
        
        self.notes_edit = QTextEdit(self)
        self.notes_edit.setMaximumHeight(100)
        order_layout.addRow(self.tr("ملاحظات:"), self.notes_edit)
        
        order_group.setContentLayout(order_layout)
        layout.addWidget(order_group)
        
        layout.addStretch()
        scroll.setWidget(container)
        
        tab_layout = QVBoxLayout(tab)
        tab_layout.addWidget(scroll)
        return tab

    def create_products_tab(self):
        """إنشاء تبويب المنتجات"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # أزرار إضافة المنتجات
        btn_layout = QHBoxLayout()
        self.add_product_btn = QPushButton(self.tr("إضافة منتج"), self)
        self.add_product_btn.clicked.connect(self.add_product_item)
        btn_layout.addWidget(self.add_product_btn)
        btn_layout.addStretch()
        layout.addLayout(btn_layout)
        
        # جدول المنتجات
        self.products_table = QTableWidget(self)
        self.products_table.setColumnCount(8)
        self.products_table.setHorizontalHeaderLabels([
            self.tr("المنتج"),
            self.tr("الكمية"),
            self.tr("سعر الوحدة"),
            self.tr("الخصم %"),
            self.tr("الضريبة %"),
            self.tr("المجموع"),
            self.tr("المخزون"),
            self.tr("إجراءات")
        ])
        
        # تخصيص عرض الأعمدة
        self.products_table.setColumnWidth(0, 200)  # المنتج
        self.products_table.setColumnWidth(1, 80)   # الكمية
        self.products_table.setColumnWidth(2, 100)  # سعر الوحدة
        self.products_table.setColumnWidth(3, 80)   # الخصم %
        self.products_table.setColumnWidth(4, 80)   # الضريبة %
        self.products_table.setColumnWidth(5, 100)  # المجموع
        self.products_table.setColumnWidth(6, 80)   # المخزون
        self.products_table.setColumnWidth(7, 100)  # إجراءات
        
        layout.addWidget(self.products_table)
        return tab

    def create_delivery_tab(self):
        """إنشاء تبويب التسليم"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # معلومات التسليم
        delivery_group = QGroupBox(self.tr("معلومات التسليم"))
        delivery_layout = QFormLayout(delivery_group)
        
        self.delivery_method_combo = QComboBox(self)
        self.delivery_method_combo.addItems([
            self.tr("توصيل منزلي"),
            self.tr("استلام من المحل"),
            self.tr("توصيل سريع")
        ])
        delivery_layout.addRow(self.tr("طريقة التسليم:"), self.delivery_method_combo)
        
        self.delivery_date_edit = QDateEdit(self)
        self.delivery_date_edit.setDate(QDate.currentDate().addDays(1))
        delivery_layout.addRow(self.tr("تاريخ التسليم المطلوب:"), self.delivery_date_edit)
        
        self.delivery_address_edit = QTextEdit(self)
        self.delivery_address_edit.setMaximumHeight(80)
        delivery_layout.addRow(self.tr("عنوان التسليم:"), self.delivery_address_edit)
        
        self.shipping_cost_spin = QDoubleSpinBox(self)
        self.shipping_cost_spin.setMaximum(1000.0)
        self.shipping_cost_spin.setValue(0.0)
        self.shipping_cost_spin.valueChanged.connect(self.calculate_totals)
        delivery_layout.addRow(self.tr("تكلفة الشحن:"), self.shipping_cost_spin)
        
        layout.addWidget(delivery_group)
        layout.addStretch()
        return tab

    def create_payment_tab(self):
        """إنشاء تبويب الدفع"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # معلومات الدفع
        payment_group = QGroupBox(self.tr("معلومات الدفع"))
        payment_layout = QFormLayout(payment_group)
        
        self.payment_method_combo = QComboBox(self)
        self.payment_method_combo.addItems([
            self.tr("نقداً"),
            self.tr("بطاقة ائتمان"),
            self.tr("تحويل بنكي"),
            self.tr("شيك"),
            self.tr("آجل")
        ])
        payment_layout.addRow(self.tr("طريقة الدفع:"), self.payment_method_combo)
        
        self.payment_status_combo = QComboBox(self)
        self.payment_status_combo.addItems([
            self.tr("غير مدفوع"),
            self.tr("مدفوع جزئياً"),
            self.tr("مدفوع بالكامل")
        ])
        payment_layout.addRow(self.tr("حالة الدفع:"), self.payment_status_combo)
        
        self.paid_amount_spin = QDoubleSpinBox(self)
        self.paid_amount_spin.setMaximum(100000.0)
        self.paid_amount_spin.setValue(0.0)
        self.paid_amount_spin.valueChanged.connect(self.calculate_remaining)
        payment_layout.addRow(self.tr("المبلغ المدفوع:"), self.paid_amount_spin)
        
        self.remaining_amount_label = QLabel("0.00")
        payment_layout.addRow(self.tr("المبلغ المتبقي:"), self.remaining_amount_label)
        
        layout.addWidget(payment_group)
        layout.addStretch()
        return tab

    def on_customer_changed(self):
        """عند تغيير العميل"""
        customer_id = self.customer_combo.currentData()
        if customer_id:
            customer = next((c for c in self.customers if c['id'] == customer_id), None)
            if customer:
                self.customer_phone_edit.setText(customer['phone'])
                self.customer_email_edit.setText(customer['email'])

    def add_product_item(self):
        """إضافة منتج إلى الجدول"""
        # إنشاء نافذة حوار لاختيار المنتج
        dialog = ProductSelectionDialog(self.products, self)
        if dialog.exec() == QDialog.Accepted:
            product_data = dialog.get_selected_product()
            if product_data:
                self.add_product_to_table(product_data)

    def add_product_to_table(self, product_data):
        """إضافة منتج إلى الجدول"""
        row = self.products_table.rowCount()
        self.products_table.insertRow(row)
        
        # اختيار المنتج
        product_combo = QComboBox()
        for product in self.products:
            product_combo.addItem(f"{product['name']} ({product['code']})", product['id'])
        product_combo.setCurrentText(f"{product_data['name']} ({product_data['code']})")
        product_combo.currentIndexChanged.connect(lambda: self.calculate_row_total(row))
        self.products_table.setCellWidget(row, 0, product_combo)
        
        # الكمية
        qty_spin = QSpinBox()
        qty_spin.setMaximum(1000)
        qty_spin.setValue(1)
        qty_spin.valueChanged.connect(lambda: self.calculate_row_total(row))
        self.products_table.setCellWidget(row, 1, qty_spin)
        
        # سعر الوحدة
        price_spin = QDoubleSpinBox()
        price_spin.setMaximum(10000.0)
        price_spin.setValue(product_data['price'])
        price_spin.valueChanged.connect(lambda: self.calculate_row_total(row))
        self.products_table.setCellWidget(row, 2, price_spin)
        
        # الخصم %
        discount_spin = QDoubleSpinBox()
        discount_spin.setMaximum(100.0)
        discount_spin.setValue(0.0)
        discount_spin.valueChanged.connect(lambda: self.calculate_row_total(row))
        self.products_table.setCellWidget(row, 3, discount_spin)
        
        # الضريبة %
        tax_spin = QDoubleSpinBox()
        tax_spin.setMaximum(100.0)
        tax_spin.setValue(15.0)  # ضريبة القيمة المضافة
        tax_spin.valueChanged.connect(lambda: self.calculate_row_total(row))
        self.products_table.setCellWidget(row, 4, tax_spin)
        
        # المجموع
        total_item = QTableWidgetItem("0.00")
        self.products_table.setItem(row, 5, total_item)
        
        # المخزون
        stock_item = QTableWidgetItem(str(product_data['stock']))
        self.products_table.setItem(row, 6, stock_item)
        
        # إجراءات
        actions_widget = QWidget()
        actions_layout = QHBoxLayout(actions_widget)
        actions_layout.setContentsMargins(0, 0, 0, 0)
        
        delete_btn = QPushButton()
        delete_btn.setIcon(QIcon('accounting_app/resources/icons/material/red-trash-can-icon.svg'))
        delete_btn.setIconSize(QSize(16, 16))
        delete_btn.setStyleSheet("QPushButton { background: #ffebee; color: #c62828; border-radius: 4px; padding: 2px; }")
        delete_btn.clicked.connect(lambda: self.delete_product_row(row))
        
        actions_layout.addWidget(delete_btn)
        self.products_table.setCellWidget(row, 7, actions_widget)
        
        # حساب المجموع الأولي
        self.calculate_row_total(row)

    def calculate_row_total(self, row):
        """حساب مجموع الصف"""
        try:
            qty = self.products_table.cellWidget(row, 1).value()
            price = self.products_table.cellWidget(row, 2).value()
            discount_percent = self.products_table.cellWidget(row, 3).value()
            tax_percent = self.products_table.cellWidget(row, 4).value()
            
            subtotal = qty * price
            discount_amount = subtotal * (discount_percent / 100)
            after_discount = subtotal - discount_amount
            tax_amount = after_discount * (tax_percent / 100)
            total = after_discount + tax_amount
            
            self.products_table.item(row, 5).setText(f"{total:.2f}")
            self.calculate_totals()
        except Exception as e:
            logging.error(f"Error calculating row total: {e}")

    def delete_product_row(self, row):
        """حذف صف المنتج"""
        self.products_table.removeRow(row)
        self.calculate_totals()

    def calculate_totals(self):
        """حساب المجاميع"""
        try:
            subtotal = 0.0
            total_discount = 0.0
            total_tax = 0.0
            
            for row in range(self.products_table.rowCount()):
                qty = self.products_table.cellWidget(row, 1).value()
                price = self.products_table.cellWidget(row, 2).value()
                discount_percent = self.products_table.cellWidget(row, 3).value()
                tax_percent = self.products_table.cellWidget(row, 4).value()
                
                row_subtotal = qty * price
                row_discount = row_subtotal * (discount_percent / 100)
                row_after_discount = row_subtotal - row_discount
                row_tax = row_after_discount * (tax_percent / 100)
                
                subtotal += row_subtotal
                total_discount += row_discount
                total_tax += row_tax
            
            shipping_cost = self.shipping_cost_spin.value()
            grand_total = subtotal - total_discount + total_tax + shipping_cost
            
            self.subtotal_label.setText(self.tr(f"المجموع الفرعي: {subtotal:.2f}"))
            self.discount_label.setText(self.tr(f"الخصم: {total_discount:.2f}"))
            self.tax_label.setText(self.tr(f"الضريبة: {total_tax:.2f}"))
            self.shipping_label.setText(self.tr(f"الشحن: {shipping_cost:.2f}"))
            self.total_label.setText(self.tr(f"المجموع الكلي: {grand_total:.2f}"))
            
            # تحديث المبلغ المتبقي
            self.calculate_remaining()
            
        except Exception as e:
            logging.error(f"Error calculating totals: {e}")

    def calculate_remaining(self):
        """حساب المبلغ المتبقي"""
        try:
            total_text = self.total_label.text()
            total = float(total_text.split(":")[1].strip())
            paid = self.paid_amount_spin.value()
            remaining = total - paid
            self.remaining_amount_label.setText(f"{remaining:.2f}")
        except Exception as e:
            logging.error(f"Error calculating remaining: {e}")

    def save_order(self):
        """حفظ الطلب"""
        try:
            # التحقق من صحة البيانات
            if self.products_table.rowCount() == 0:
                QMessageBox.warning(self, self.tr("تنبيه"), self.tr("يجب إضافة منتج واحد على الأقل"))
                return
            
            # جمع بيانات الطلب
            order_data = self.get_order_data()
            
            # هنا سيتم حفظ البيانات في قاعدة البيانات
            QMessageBox.information(self, self.tr("نجح"), self.tr("تم حفظ الطلب بنجاح"))
            self.accept()
            
        except Exception as e:
            logging.error(f"Error saving order: {e}")
            QMessageBox.critical(self, self.tr("خطأ"), self.tr(f"حدث خطأ أثناء حفظ الطلب: {e}"))

    def get_order_data(self):
        """الحصول على بيانات الطلب"""
        order_data = {
            "customer_id": self.customer_combo.currentData(),
            "order_date": self.order_date_edit.date().toString("yyyy-MM-dd"),
            "order_status": self.order_status_combo.currentText(),
            "delivery_method": self.delivery_method_combo.currentText(),
            "delivery_date": self.delivery_date_edit.date().toString("yyyy-MM-dd"),
            "delivery_address": self.delivery_address_edit.toPlainText(),
            "payment_method": self.payment_method_combo.currentText(),
            "payment_status": self.payment_status_combo.currentText(),
            "shipping_cost": self.shipping_cost_spin.value(),
            "paid_amount": self.paid_amount_spin.value(),
            "notes": self.notes_edit.toPlainText(),
            "items": []
        }
        
        # جمع المنتجات
        for row in range(self.products_table.rowCount()):
            product_combo = self.products_table.cellWidget(row, 0)
            product_id = product_combo.currentData()
            qty = self.products_table.cellWidget(row, 1).value()
            price = self.products_table.cellWidget(row, 2).value()
            discount = self.products_table.cellWidget(row, 3).value()
            tax = self.products_table.cellWidget(row, 4).value()
            
            order_data["items"].append({
                "product_id": product_id,
                "quantity": qty,
                "unit_price": price,
                "discount_percent": discount,
                "tax_percent": tax
            })
        
        return order_data

    def set_order_data(self, order):
        """تعيين بيانات الطلب للتعديل"""
        # سيتم تنفيذ هذا لاحقاً
        pass

class ProductSelectionDialog(QDialog):
    """نافذة اختيار المنتج"""
    
    def __init__(self, products, parent=None):
        super().__init__(parent)
        self.setWindowTitle(self.tr("اختيار المنتج"))
        self.setMinimumWidth(400)
        self.products = products
        self.selected_product = None
        self.init_ui()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)
        
        # جدول المنتجات
        self.products_table = QTableWidget(self)
        self.products_table.setColumnCount(4)
        self.products_table.setHorizontalHeaderLabels([
            self.tr("المنتج"),
            self.tr("الرمز"),
            self.tr("السعر"),
            self.tr("المخزون")
        ])
        
        # ملء الجدول
        for product in self.products:
            row = self.products_table.rowCount()
            self.products_table.insertRow(row)
            self.products_table.setItem(row, 0, QTableWidgetItem(product['name']))
            self.products_table.setItem(row, 1, QTableWidgetItem(product['code']))
            self.products_table.setItem(row, 2, QTableWidgetItem(str(product['price'])))
            self.products_table.setItem(row, 3, QTableWidgetItem(str(product['stock'])))
        
        self.products_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.products_table.setSelectionMode(QTableWidget.SingleSelection)
        self.products_table.itemSelectionChanged.connect(self.on_selection_changed)
        
        layout.addWidget(self.products_table)
        
        # أزرار
        button_box = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

    def on_selection_changed(self):
        """عند تغيير الاختيار"""
        current_row = self.products_table.currentRow()
        if current_row >= 0:
            self.selected_product = self.products[current_row]

    def get_selected_product(self):
        """الحصول على المنتج المختار"""
        return self.selected_product

class SalesOrdersTab(QWidget):
    """تبويب أوامر المبيعات"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("SalesOrdersTab")
        self.init_ui()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)
        
        # شريط البحث والفلترة
        search_layout = QHBoxLayout()
        
        self.search_box = QLineEdit(self)
        self.search_box.setPlaceholderText(self.tr("بحث في أوامر المبيعات..."))
        search_layout.addWidget(QLabel(self.tr("بحث:")))
        search_layout.addWidget(self.search_box)
        
        self.status_filter = QComboBox(self)
        self.status_filter.addItem(self.tr("جميع الحالات"))
        self.status_filter.addItems([
            self.tr("مسودة"),
            self.tr("مؤكد"),
            self.tr("قيد التحضير"),
            self.tr("جاهز للتسليم"),
            self.tr("مسلم"),
            self.tr("ملغي")
        ])
        search_layout.addWidget(QLabel(self.tr("الحالة:")))
        search_layout.addWidget(self.status_filter)
        
        self.date_from = QDateEdit(self)
        self.date_from.setDate(QDate.currentDate().addDays(-30))
        search_layout.addWidget(QLabel(self.tr("من:")))
        search_layout.addWidget(self.date_from)
        
        self.date_to = QDateEdit(self)
        self.date_to.setDate(QDate.currentDate())
        search_layout.addWidget(QLabel(self.tr("إلى:")))
        search_layout.addWidget(self.date_to)
        
        layout.addLayout(search_layout)
        
        # أزرار الإجراءات
        btn_layout = QHBoxLayout()
        self.add_order_btn = QPushButton(self.tr("أمر مبيعات جديد"), self)
        self.add_order_btn.clicked.connect(self.add_order)
        btn_layout.addWidget(self.add_order_btn)
        
        self.export_btn = QPushButton(self.tr("تصدير"), self)
        btn_layout.addWidget(self.export_btn)
        
        self.delete_selected_btn = QPushButton(self.tr("حذف المحدد"), self)
        self.delete_selected_btn.clicked.connect(self.delete_selected)
        btn_layout.addWidget(self.delete_selected_btn)
        
        btn_layout.addStretch()
        layout.addLayout(btn_layout)
        
        # جدول أوامر المبيعات
        self.setup_orders_table()
        layout.addWidget(self.orders_table)
        
        self.setLayout(layout)
        
        # ربط الأحداث
        self.search_box.textChanged.connect(self.filter_table)
        self.status_filter.currentTextChanged.connect(self.filter_table)
        self.date_from.dateChanged.connect(self.filter_table)
        self.date_to.dateChanged.connect(self.filter_table)
        
        # تحميل البيانات
        self.load_orders_data()
        self.refresh_table()

    def setup_orders_table(self):
        """إعداد جدول أوامر المبيعات"""
        self.orders_table = QTableWidget(self)
        self.orders_table.setColumnCount(12)
        self.orders_table.setHorizontalHeaderLabels([
            self.tr("رقم الأمر"),
            self.tr("التاريخ"),
            self.tr("العميل"),
            self.tr("المجموع"),
            self.tr("حالة الأمر"),
            self.tr("حالة الدفع"),
            self.tr("تاريخ التسليم"),
            self.tr("طريقة الدفع"),
            self.tr("المبيعات"),
            self.tr("الملاحظات"),
            self.tr("تاريخ الإنشاء"),
            self.tr("إجراءات")
        ])
        
        # تخصيص عرض الأعمدة
        self.orders_table.setColumnWidth(0, 100)  # رقم الأمر
        self.orders_table.setColumnWidth(1, 100)  # التاريخ
        self.orders_table.setColumnWidth(2, 150)  # العميل
        self.orders_table.setColumnWidth(3, 100)  # المجموع
        self.orders_table.setColumnWidth(4, 100)  # حالة الأمر
        self.orders_table.setColumnWidth(5, 100)  # حالة الدفع
        self.orders_table.setColumnWidth(6, 100)  # تاريخ التسليم
        self.orders_table.setColumnWidth(7, 100)  # طريقة الدفع
        self.orders_table.setColumnWidth(8, 100)  # المبيعات
        self.orders_table.setColumnWidth(9, 150)  # الملاحظات
        self.orders_table.setColumnWidth(10, 100) # تاريخ الإنشاء
        self.orders_table.setColumnWidth(11, 120) # إجراءات
        
        self.orders_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.orders_table.setSelectionMode(QTableWidget.MultiSelection)

    def load_orders_data(self):
        """تحميل بيانات أوامر المبيعات"""
        # بيانات تجريبية
        self.orders = [
            {
                "id": "SO-001",
                "date": "2024-06-01",
                "customer": "أحمد محمد",
                "total": 1500.0,
                "status": self.tr("مؤكد"),
                "payment_status": self.tr("مدفوع جزئياً"),
                "delivery_date": "2024-06-03",
                "payment_method": self.tr("بطاقة ائتمان"),
                "salesperson": "سارة أحمد",
                "notes": "طلب عاجل",
                "created_at": "2024-06-01 10:30"
            },
            {
                "id": "SO-002",
                "date": "2024-06-02",
                "customer": "سارة أحمد",
                "total": 2300.0,
                "status": self.tr("قيد التحضير"),
                "payment_status": self.tr("غير مدفوع"),
                "delivery_date": "2024-06-05",
                "payment_method": self.tr("نقداً"),
                "salesperson": "محمد علي",
                "notes": "",
                "created_at": "2024-06-02 14:15"
            }
        ]

    def refresh_table(self):
        """تحديث الجدول"""
        self.orders_table.setRowCount(0)
        for order in self.orders:
            row = self.orders_table.rowCount()
            self.orders_table.insertRow(row)
            
            self.orders_table.setItem(row, 0, QTableWidgetItem(order["id"]))
            self.orders_table.setItem(row, 1, QTableWidgetItem(order["date"]))
            self.orders_table.setItem(row, 2, QTableWidgetItem(order["customer"]))
            self.orders_table.setItem(row, 3, QTableWidgetItem(f"{order['total']:.2f}"))
            
            status_item = QTableWidgetItem(order["status"])
            if order["status"] == self.tr("مؤكد"):
                status_item.setBackground(Qt.green)
            elif order["status"] == self.tr("ملغي"):
                status_item.setBackground(Qt.red)
            self.orders_table.setItem(row, 4, status_item)
            
            payment_status_item = QTableWidgetItem(order["payment_status"])
            if order["payment_status"] == self.tr("مدفوع بالكامل"):
                payment_status_item.setBackground(Qt.green)
            elif order["payment_status"] == self.tr("غير مدفوع"):
                payment_status_item.setBackground(Qt.red)
            self.orders_table.setItem(row, 5, payment_status_item)
            
            self.orders_table.setItem(row, 6, QTableWidgetItem(order["delivery_date"]))
            self.orders_table.setItem(row, 7, QTableWidgetItem(order["payment_method"]))
            self.orders_table.setItem(row, 8, QTableWidgetItem(order["salesperson"]))
            self.orders_table.setItem(row, 9, QTableWidgetItem(order["notes"]))
            self.orders_table.setItem(row, 10, QTableWidgetItem(order["created_at"]))
            
            # أزرار الإجراءات
            actions_widget = QWidget()
            actions_layout = QHBoxLayout(actions_widget)
            actions_layout.setContentsMargins(0, 0, 0, 0)
            
            edit_btn = QPushButton()
            edit_btn.setIcon(QIcon('accounting_app/resources/icons/material/edit-173.svg'))
            edit_btn.setIconSize(QSize(16, 16))
            edit_btn.setStyleSheet("QPushButton { background: #e3f0ff; color: #1565c0; border-radius: 4px; padding: 2px; }")
            edit_btn.clicked.connect(lambda _, r=row: self.edit_order(r))
            
            delete_btn = QPushButton()
            delete_btn.setIcon(QIcon('accounting_app/resources/icons/material/red-trash-can-icon.svg'))
            delete_btn.setIconSize(QSize(16, 16))
            delete_btn.setStyleSheet("QPushButton { background: #ffebee; color: #c62828; border-radius: 4px; padding: 2px; }")
            delete_btn.clicked.connect(lambda _, r=row: self.delete_order(r))
            
            actions_layout.addWidget(edit_btn)
            actions_layout.addWidget(delete_btn)
            self.orders_table.setCellWidget(row, 11, actions_widget)

    def filter_table(self):
        """فلترة الجدول"""
        # سيتم تنفيذ الفلترة لاحقاً
        pass

    def add_order(self):
        """إضافة أمر جديد"""
        dialog = SalesOrderDialog(self)
        if dialog.exec() == QDialog.Accepted:
            # إعادة تحميل البيانات
            self.load_orders_data()
            self.refresh_table()

    def edit_order(self, row):
        """تعديل الأمر"""
        order_id = self.orders_table.item(row, 0).text()
        # هنا سيتم تحميل بيانات الأمر وفتح نافذة التعديل
        QMessageBox.information(self, self.tr("تعديل"), self.tr(f"تعديل الأمر: {order_id}"))

    def delete_order(self, row):
        """حذف الأمر"""
        order_id = self.orders_table.item(row, 0).text()
        reply = QMessageBox.question(
            self, 
            self.tr("تأكيد الحذف"), 
            self.tr(f"هل أنت متأكد من حذف الأمر {order_id}؟"),
            QMessageBox.Yes | QMessageBox.No
        )
        if reply == QMessageBox.Yes:
            self.orders_table.removeRow(row)

    def delete_selected(self):
        """حذف العناصر المحددة"""
        selected_rows = set()
        for item in self.orders_table.selectedItems():
            selected_rows.add(item.row())
        
        if not selected_rows:
            QMessageBox.warning(self, self.tr("تنبيه"), self.tr("لم يتم تحديد أي عنصر"))
            return
        
        reply = QMessageBox.question(
            self, 
            self.tr("تأكيد الحذف"), 
            self.tr(f"هل أنت متأكد من حذف {len(selected_rows)} عنصر؟"),
            QMessageBox.Yes | QMessageBox.No
        )
        if reply == QMessageBox.Yes:
            for row in sorted(selected_rows, reverse=True):
                self.orders_table.removeRow(row) 