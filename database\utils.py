import bcrypt
from math import ceil
import redis
from database.config.db_config import REDIS_URL

def hash_password(password: str) -> str:
    return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

def verify_password(password: str, hashed: str) -> bool:
    return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))

def paginate(query, page: int = 1, per_page: int = 10):
    total = query.count()
    items = query.offset((page - 1) * per_page).limit(per_page).all()
    return {
        'items': items,
        'total': total,
        'page': page,
        'per_page': per_page,
        'pages': ceil(total / per_page) if per_page else 1
    }

def get_redis_connection():
    return redis.Redis.from_url(REDIS_URL) 