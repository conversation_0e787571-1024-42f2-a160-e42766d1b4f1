"""
Documentation package for Smart Accounting App
حزمة التوثيق لبرنامج المحاسبة الذكي
"""

import os
from pathlib import Path

# الحصول على مسار مجلد التوثيق
DOCS_DIR = Path(__file__).parent

def get_doc_file_path(filename: str) -> str:
    """الحصول على المسار الكامل لملف التوثيق"""
    return str(DOCS_DIR / filename)

def list_doc_files() -> list:
    """قائمة بجميع ملفات التوثيق"""
    doc_files = []
    for file_path in DOCS_DIR.iterdir():
        if file_path.is_file() and file_path.name != "__init__.py":
            doc_files.append(file_path.name)
    return doc_files

def doc_file_exists(filename: str) -> bool:
    """التحقق من وجود ملف توثيق"""
    return (DOCS_DIR / filename).exists()

# قائمة ملفات التوثيق المتاحة
AVAILABLE_DOCS = {
    "dependencies_management.md": "إدارة الاعتمادات والتبعيات",
    "translation_files_fix.md": "إصلاح ملفات الترجمة",
    "eventbus_singleton_fix.md": "إصلاح EventBus كـ Singleton",
    "lambda_closure_fix.md": "إصلاح مشاكل Lambda Closures",
    "effects_guide.md": "دليل التأثيرات البصرية",
    "translation_guide.md": "دليل الترجمة",
    "events.md": "دليل نظام الأحداث"
}

def get_doc_info(filename: str) -> dict:
    """الحصول على معلومات ملف التوثيق"""
    if filename in AVAILABLE_DOCS:
        return {
            "filename": filename,
            "title": AVAILABLE_DOCS[filename],
            "exists": doc_file_exists(filename),
            "path": get_doc_file_path(filename) if doc_file_exists(filename) else None
        }
    return None 