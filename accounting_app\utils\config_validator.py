"""
Configuration Validator for Smart Accounting App
فاحص إعدادات برنامج المحاسبة الذكي
"""

import json
import os
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass

try:
    from jsonschema import validate, ValidationError, Draft7Validator
    from jsonschema.exceptions import SchemaError
    JSONSCHEMA_AVAILABLE = True
except ImportError:
    JSONSCHEMA_AVAILABLE = False
    logging.warning("jsonschema library not available. Schema validation will be disabled.")

logger = logging.getLogger(__name__)

@dataclass
class ValidationResult:
    """نتيجة التحقق من صحة الإعدادات"""
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    fixed_values: Dict[str, Any]

class ConfigValidator:
    """فاحص إعدادات التطبيق"""
    
    def __init__(self, schema_path: str = None):
        """تهيئة فاحص الإعدادات"""
        if schema_path is None:
            # الحصول على مسار المخطط تلقائياً
            app_root = Path(__file__).parent.parent
            schema_path = app_root / "config" / "schema.json"
        
        self.schema_path = Path(schema_path)
        self.schema = None
        self.validator = None
        
        if JSONSCHEMA_AVAILABLE:
            self._load_schema()
        else:
            logger.warning("Schema validation disabled due to missing jsonschema library")
    
    def _load_schema(self):
        """تحميل مخطط JSON Schema"""
        try:
            if self.schema_path.exists():
                with open(self.schema_path, 'r', encoding='utf-8') as f:
                    self.schema = json.load(f)
                self.validator = Draft7Validator(self.schema)
                logger.info(f"Schema loaded successfully from {self.schema_path}")
            else:
                logger.error(f"Schema file not found: {self.schema_path}")
        except Exception as e:
            logger.error(f"Failed to load schema: {e}")
    
    def validate_config(self, config_data: Dict[str, Any]) -> ValidationResult:
        """التحقق من صحة بيانات الإعدادات"""
        errors = []
        warnings = []
        fixed_values = {}
        
        if not JSONSCHEMA_AVAILABLE:
            warnings.append("Schema validation disabled - using basic validation")
            return self._basic_validation(config_data)
        
        if not self.validator:
            warnings.append("Schema validator not available - using basic validation")
            return self._basic_validation(config_data)
        
        try:
            # التحقق من صحة المخطط
            self.validator.validate(config_data)
            logger.info("Configuration validation passed")
            
        except ValidationError as e:
            errors.append(f"Validation error: {e.message}")
            logger.error(f"Configuration validation failed: {e.message}")
            
            # محاولة إصلاح الأخطاء
            fixed_values = self._fix_validation_errors(config_data, e)
            
        except SchemaError as e:
            errors.append(f"Schema error: {e.message}")
            logger.error(f"Schema error: {e.message}")
        
        # التحقق من القيم المفقودة
        missing_values = self._check_missing_values(config_data)
        if missing_values:
            warnings.extend([f"Missing value: {key}" for key in missing_values])
            fixed_values.update(self._add_default_values(missing_values))
        
        # التحقق من القيم غير الصالحة
        invalid_values = self._check_invalid_values(config_data)
        if invalid_values:
            warnings.extend([f"Invalid value for {key}: {value}" for key, value in invalid_values.items()])
            fixed_values.update(self._fix_invalid_values(invalid_values))
        
        is_valid = len(errors) == 0
        
        return ValidationResult(
            is_valid=is_valid,
            errors=errors,
            warnings=warnings,
            fixed_values=fixed_values
        )
    
    def _basic_validation(self, config_data: Dict[str, Any]) -> ValidationResult:
        """التحقق الأساسي من الإعدادات"""
        errors = []
        warnings = []
        fixed_values = {}
        
        # التحقق من وجود الحقول الأساسية
        required_sections = [
            "app_info", "user_preferences", "ai_settings", 
            "notification_settings", "database_settings", 
            "security_settings", "ui_settings", "performance_settings"
        ]
        
        for section in required_sections:
            if section not in config_data:
                errors.append(f"Missing required section: {section}")
                fixed_values[section] = self._get_default_section(section)
            elif not isinstance(config_data[section], dict):
                errors.append(f"Invalid section type for {section}")
                fixed_values[section] = self._get_default_section(section)
        
        # التحقق من القيم الأساسية
        if "user_preferences" in config_data:
            prefs = config_data["user_preferences"]
            if "language" not in prefs or prefs["language"] not in ["ar", "en", "fr"]:
                warnings.append("Invalid language setting")
                fixed_values.setdefault("user_preferences", {})["language"] = "ar"
            
            if "theme" not in prefs or prefs["theme"] not in ["dark", "light", "auto", "default"]:
                warnings.append("Invalid theme setting")
                fixed_values.setdefault("user_preferences", {})["theme"] = "default"
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            fixed_values=fixed_values
        )
    
    def _check_missing_values(self, config_data: Dict[str, Any]) -> List[str]:
        """التحقق من القيم المفقودة"""
        missing = []
        
        # التحقق من القيم المطلوبة في كل قسم
        required_values = {
            "app_info": ["app_name", "app_version", "app_description", "environment"],
            "user_preferences": ["language", "theme", "date_format", "time_format", "currency"],
            "ai_settings": ["enabled", "model", "max_tokens", "temperature"],
            "notification_settings": ["enabled", "sound_enabled", "desktop_notifications"],
            "database_settings": ["type", "auto_backup"],
            "security_settings": ["session_timeout", "password_min_length"],
            "ui_settings": ["window_size", "sidebar_width"],
            "performance_settings": ["log_level", "cache_enabled"]
        }
        
        for section, required in required_values.items():
            if section in config_data:
                section_data = config_data[section]
                for field in required:
                    if field not in section_data:
                        missing.append(f"{section}.{field}")
        
        return missing
    
    def _check_invalid_values(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """التحقق من القيم غير الصالحة"""
        invalid = {}
        
        # التحقق من قيم محددة
        if "user_preferences" in config_data:
            prefs = config_data["user_preferences"]
            
            if "language" in prefs and prefs["language"] not in ["ar", "en", "fr"]:
                invalid["user_preferences.language"] = prefs["language"]
            
            if "theme" in prefs and prefs["theme"] not in ["dark", "light", "auto", "default"]:
                invalid["user_preferences.theme"] = prefs["theme"]
        
        if "ai_settings" in config_data:
            ai = config_data["ai_settings"]
            
            if "model" in ai and ai["model"] not in ["gpt-3.5-turbo", "gpt-4", "local"]:
                invalid["ai_settings.model"] = ai["model"]
            
            if "max_tokens" in ai and (not isinstance(ai["max_tokens"], int) or ai["max_tokens"] < 100):
                invalid["ai_settings.max_tokens"] = ai["max_tokens"]
        
        return invalid
    
    def _fix_validation_errors(self, config_data: Dict[str, Any], error) -> Dict[str, Any]:
        """إصلاح أخطاء التحقق"""
        fixed = {}
        
        # إصلاح الأخطاء الشائعة
        if "language" in str(error.path) and "user_preferences" in str(error.path):
            fixed["user_preferences.language"] = "ar"
        
        if "theme" in str(error.path) and "user_preferences" in str(error.path):
            fixed["user_preferences.theme"] = "default"
        
        if "model" in str(error.path) and "ai_settings" in str(error.path):
            fixed["ai_settings.model"] = "gpt-3.5-turbo"
        
        return fixed
    
    def _add_default_values(self, missing_fields: List[str]) -> Dict[str, Any]:
        """إضافة القيم الافتراضية للحقول المفقودة"""
        defaults = {}
        
        default_values = {
            "app_info.app_name": "Smart Accounting App",
            "app_info.app_version": "1.0.0",
            "app_info.app_description": "برنامج محاسبة ذكي مع مساعد ذكي مدمج",
            "app_info.environment": "development",
            "user_preferences.language": "ar",
            "user_preferences.theme": "default",
            "user_preferences.date_format": "dd/mm/yyyy",
            "user_preferences.time_format": "24h",
            "user_preferences.currency": "SAR",
            "ai_settings.enabled": True,
            "ai_settings.model": "gpt-3.5-turbo",
            "ai_settings.max_tokens": 1000,
            "ai_settings.temperature": 0.7,
            "notification_settings.enabled": True,
            "notification_settings.sound_enabled": True,
            "notification_settings.desktop_notifications": True,
            "database_settings.type": "sqlite",
            "database_settings.auto_backup": True,
            "security_settings.session_timeout": 30,
            "security_settings.password_min_length": 8,
            "ui_settings.sidebar_width": 250,
            "performance_settings.log_level": "INFO",
            "performance_settings.cache_enabled": True
        }
        
        for field in missing_fields:
            if field in default_values:
                defaults[field] = default_values[field]
        
        return defaults
    
    def _fix_invalid_values(self, invalid_values: Dict[str, Any]) -> Dict[str, Any]:
        """إصلاح القيم غير الصالحة"""
        fixes = {}
        
        for field, value in invalid_values.items():
            if field == "user_preferences.language":
                fixes[field] = "ar"
            elif field == "user_preferences.theme":
                fixes[field] = "default"
            elif field == "ai_settings.model":
                fixes[field] = "gpt-3.5-turbo"
            elif field == "ai_settings.max_tokens":
                fixes[field] = 1000
        
        return fixes
    
    def _get_default_section(self, section: str) -> Dict[str, Any]:
        """الحصول على القيم الافتراضية لقسم معين"""
        defaults = {
            "app_info": {
                "app_name": "Smart Accounting App",
                "app_version": "1.0.0",
                "app_description": "برنامج محاسبة ذكي مع مساعد ذكي مدمج",
                "environment": "development"
            },
            "user_preferences": {
                "language": "ar",
                "theme": "default",
                "date_format": "dd/mm/yyyy",
                "time_format": "24h",
                "currency": "SAR"
            },
            "ai_settings": {
                "enabled": True,
                "model": "gpt-3.5-turbo",
                "max_tokens": 1000,
                "temperature": 0.7
            },
            "notification_settings": {
                "enabled": True,
                "sound_enabled": True,
                "desktop_notifications": True
            },
            "database_settings": {
                "type": "sqlite",
                "auto_backup": True
            },
            "security_settings": {
                "session_timeout": 30,
                "password_min_length": 8
            },
            "ui_settings": {
                "sidebar_width": 250
            },
            "performance_settings": {
                "log_level": "INFO",
                "cache_enabled": True
            }
        }
        
        return defaults.get(section, {})
    
    def apply_fixes(self, config_data: Dict[str, Any], fixes: Dict[str, Any]) -> Dict[str, Any]:
        """تطبيق الإصلاحات على بيانات الإعدادات"""
        fixed_config = config_data.copy()
        
        for field_path, value in fixes.items():
            # تقسيم مسار الحقل إلى أقسام
            parts = field_path.split('.')
            current = fixed_config
            
            # الوصول إلى القسم الأخير
            for part in parts[:-1]:
                if part not in current:
                    current[part] = {}
                current = current[part]
            
            # تعيين القيمة
            current[parts[-1]] = value
        
        return fixed_config
    
    def create_default_config(self) -> Dict[str, Any]:
        """إنشاء إعدادات افتراضية كاملة"""
        return {
            "app_info": {
                "app_name": "Smart Accounting App",
                "app_version": "1.0.0",
                "app_description": "برنامج محاسبة ذكي مع مساعد ذكي مدمج",
                "environment": "development",
                "base_path": "",
                "resources_path": "resources",
                "translations_path": "translations"
            },
            "user_preferences": {
                "language": "ar",
                "theme": "default",
                "date_format": "dd/mm/yyyy",
                "time_format": "24h",
                "currency": "SAR",
                "decimal_separator": ".",
                "thousands_separator": ","
            },
            "ai_settings": {
                "enabled": True,
                "model": "gpt-3.5-turbo",
                "api_key": "",
                "max_tokens": 1000,
                "temperature": 0.7,
                "context_window": 10,
                "auto_suggest": True
            },
            "notification_settings": {
                "enabled": True,
                "sound_enabled": True,
                "sound_file": "notification.wav",
                "desktop_notifications": True,
                "notification_duration": 5,
                "notification_position": "top-right",
                "auto_hide": True
            },
            "database_settings": {
                "type": "sqlite",
                "host": "localhost",
                "port": 5432,
                "database_name": "accounting_app",
                "username": "",
                "password": "",
                "auto_backup": True,
                "backup_interval": 7,
                "max_backups": 10
            },
            "security_settings": {
                "session_timeout": 30,
                "password_min_length": 8,
                "require_special_chars": True,
                "max_login_attempts": 5,
                "lockout_duration": 15,
                "encrypt_sensitive_data": True
            },
            "ui_settings": {
                "window_size": {
                    "width": 1200,
                    "height": 800
                },
                "sidebar_width": 250,
                "font_size": 12,
                "font_family": "Arial",
                "show_tooltips": True,
                "show_status_bar": True,
                "auto_save_ui_state": True
            },
            "performance_settings": {
                "log_level": "INFO",
                "cache_enabled": True,
                "cache_size": 100,
                "max_memory_usage": 512,
                "auto_cleanup": True,
                "cleanup_interval": 7
            }
        }

def validate_config_file(config_path: str) -> ValidationResult:
    """التحقق من صحة ملف الإعدادات"""
    validator = ConfigValidator()
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        
        return validator.validate_config(config_data)
        
    except FileNotFoundError:
        return ValidationResult(
            is_valid=False,
            errors=[f"Config file not found: {config_path}"],
            warnings=[],
            fixed_values={}
        )
    except json.JSONDecodeError as e:
        return ValidationResult(
            is_valid=False,
            errors=[f"Invalid JSON format: {e}"],
            warnings=[],
            fixed_values={}
        )
    except Exception as e:
        return ValidationResult(
            is_valid=False,
            errors=[f"Unexpected error: {e}"],
            warnings=[],
            fixed_values={}
        )

def create_valid_config_file(config_path: str) -> bool:
    """إنشاء ملف إعدادات صحيح"""
    try:
        validator = ConfigValidator()
        default_config = validator.create_default_config()
        
        # التأكد من وجود المجلد
        config_dir = Path(config_path).parent
        config_dir.mkdir(parents=True, exist_ok=True)
        
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(default_config, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Created valid config file: {config_path}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to create config file: {e}")
        return False 