from database.repositories.transaction_repository import TransactionRepository
from database.models import Transaction
from sqlalchemy.orm import Session

class TransactionService:
    def __init__(self, db: Session):
        self.transaction_repo = TransactionRepository(db)

    def create_transaction(self, company_id: str, description: str = ""):
        transaction = Transaction(company_id=company_id, description=description)
        return self.transaction_repo.create(transaction)

    def get_transactions_by_company(self, company_id: str):
        return self.transaction_repo.get_by_company(company_id)

    def get_transaction(self, transaction_id: str):
        return self.transaction_repo.get_by_id(transaction_id) 