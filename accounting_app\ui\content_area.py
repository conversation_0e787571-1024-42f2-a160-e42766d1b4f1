from PySide6.QtWidgets import Q<PERSON>abWidget, QW<PERSON>t, QLabel, QVBoxLayout, QFormLayout, QComboBox, QPushButton, QHBoxLayout, QMenu, QTableWidget, QTableWidgetItem, QHeaderView, QLineEdit, QDialog, QFormLayout, QDialogButtonBox, QMessageBox
from PySide6.QtCore import Qt
import logging
from accounting_app.utils import safe_update
from .effects import ShadowEffects
from .toast import show_error_toast, ToastWidget
from accounting_app.modules.inventory.ui.product_view import ProductListTab
from accounting_app.modules.inventory.ui.warehouse_view import WarehouseListTab
from accounting_app.modules.inventory.ui.stock_movements_view import StockMovementsTab
from accounting_app.modules.inventory.ui.inventory_adjustment_view import InventoryAdjustmentTab
from accounting_app.modules.inventory.ui.stock_alerts_view import StockAlertsTab
from accounting_app.modules.inventory.ui.inventory_main_tab import InventoryMainTab
from accounting_app.modules.inventory.ui.warehouses_main_tab import WarehousesMainTab

class SettingsTab(QWidget):
    def __init__(self, settings, event_bus, parent=None):
        super().__init__(parent)
        self.settings = settings
        self.event_bus = event_bus
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(16)
        # تمت إزالة عنصر التشخيص
        form = QFormLayout()
        self.theme_combo = QComboBox()
        self.theme_combo.addItem(self.tr("داكن"), "dark")
        self.theme_combo.addItem(self.tr("فاتح"), "light")
        self.theme_combo.addItem(self.tr("افتراضي"), "default")
        idx = self.theme_combo.findData(self.settings.theme)
        if idx >= 0:
            self.theme_combo.setCurrentIndex(idx)
        form.addRow(QLabel(self.tr("الثيم:")), self.theme_combo)
        btn_layout = QHBoxLayout()
        btn_layout.addStretch()
        save_btn = QPushButton(self.tr("حفظ"))
        save_btn.setFixedWidth(120)
        save_btn.clicked.connect(self.save_theme)
        ShadowEffects.apply_button_shadow(save_btn)
        btn_layout.addWidget(save_btn)
        btn_layout.addStretch()
        main_layout = QVBoxLayout()
        main_layout.addLayout(form)
        main_layout.addSpacing(16)
        main_layout.addLayout(btn_layout)
        main_layout.addStretch()
        layout.addLayout(main_layout)
        self.setLayout(layout)

    def showEvent(self, event):
        super().showEvent(event)
        self.updateGeometry()
        safe_update(self)
        if self.layout():
            safe_update(self.layout())
        self.repaint()

    def save_theme(self):
        mw = self.window()
        theme = self.theme_combo.currentData()
        if self.settings.theme == theme:
            if mw and hasattr(mw, 'show_toast'):
                mw.show_toast("الثيم المختار هو نفسه الحالي، لا حاجة لإعادة التطبيق", True)
            return
        if mw and hasattr(mw, 'show_toast'):
            mw.show_toast("تم استدعاء دالة حفظ الثيم", True)
        self.settings.theme = theme
        self.settings.save()
        from accounting_app.core.style_manager import StyleManager
        from PySide6.QtWidgets import QApplication
        StyleManager(QApplication.instance(), theme=theme).apply_style()
        if mw:
            mw.setStyle(QApplication.instance().style())
            for child in mw.findChildren(QWidget):
                child.setStyle(QApplication.instance().style())
        # إرسال حدث تغيير الثيم
        if self.event_bus:
            from accounting_app.core.events import AppEvents
            self.event_bus.emit(AppEvents.THEME_CHANGED, {"theme": theme})
            if mw and hasattr(mw, 'show_toast'):
                mw.show_toast("تم إرسال حدث تغيير الثيم", True)

class ContentArea(QTabWidget):
    def __init__(self, event_bus, language_controller=None, settings=None, parent=None):
        super().__init__(parent)
        self.event_bus = event_bus
        self.language_controller = language_controller
        self.settings = settings
        self.setObjectName("MainContent")
        self.setTabsClosable(True)
        self.tabCloseRequested.connect(self.close_tab)
        self.setMovable(True)
        self.setTabPosition(QTabWidget.North)
        self.init_tabs()
        self.pinned_tabs = set()
        self.setContextMenuPolicy(Qt.CustomContextMenu)
        self.customContextMenuRequested.connect(self.show_tab_context_menu)
        self.tab_cache = {}
        self.event_bus.subscribe("LANGUAGE_CHANGED", self.reload_texts)
        self.event_bus.subscribe("OPEN_INVENTORY_TAB", self.open_inventory_tab)
        self.event_bus.subscribe("OPEN_WAREHOUSES_TAB", self.open_warehouses_tab)
        self.event_bus.subscribe("OPEN_SALES_TAB", self.open_sales_tab)
        self.event_bus.subscribe("OPEN_PURCHASES_TAB", self.open_purchases_tab)
        # إزالة دوال فتح الحركات والجرد والتنبيهات من الشريط الجانبي
        self.update_layout_direction()

    def init_tabs(self):
        home_tab = QWidget()
        layout = QVBoxLayout()
        label = QLabel(self.tr("مرحبًا بك في الصفحة الرئيسية!"))
        label.setAlignment(Qt.AlignCenter)
        layout.addWidget(label)
        home_tab.setLayout(layout)
        self.addTab(home_tab, self.tr("الرئيسية"))
        self.home_tab = home_tab
        self.home_label = label
        logging.getLogger(__name__).info("ContentArea home tab initialized.")

    def reload_texts(self, lang_code=None):
        self.setTabText(0, self.tr("الرئيسية"))
        self.home_label.setText(self.tr("مرحبًا بك في الصفحة الرئيسية!"))
        self.update_layout_direction()
        logging.getLogger(__name__).info(f"ContentArea texts reloaded for language: {lang_code}")

    def update_layout_direction(self):
        try:
            if self.language_controller:
                direction = self.language_controller.get_language_direction(self.language_controller.current_lang)
                self.setLayoutDirection(direction)
                for child in self.findChildren(QWidget):
                    child.setLayoutDirection(direction)
                logging.getLogger(__name__).info(f"ContentArea layout direction updated to: {'RTL' if direction == Qt.RightToLeft else 'LTR'}")
        except Exception as e:
            logging.getLogger(__name__).error(f"Failed to update ContentArea layout direction: {e}")

    # تم حذف دالة animate_tab نهائيًا لأنها كانت تسبب مشاكل في رسم التبويبات

    def addTab(self, widget, label):
        idx = super().addTab(widget, label)
        # لم يعد هناك تحريك
        widget.updateGeometry()
        safe_update(widget)
        self.updateGeometry()
        safe_update(self)
        return idx

    def show_tab_context_menu(self, pos):
        index = self.tabBar().tabAt(pos)
        if index == -1:
            return
        menu = QMenu(self)
        if index in self.pinned_tabs:
            action = menu.addAction(self.tr("إلغاء تثبيت التبويب"))
            action.triggered.connect(lambda: self.unpin_tab(index))
        else:
            action = menu.addAction(self.tr("تثبيت التبويب"))
            action.triggered.connect(lambda: self.pin_tab(index))
        menu.addSeparator()
        close_all_action = menu.addAction(self.tr("إغلاق كل التبويبات"))
        close_all_action.triggered.connect(self.close_all_tabs)
        menu.exec(self.mapToGlobal(pos))

    def pin_tab(self, index):
        self.pinned_tabs.add(index)
        self.setTabText(index, "📌 " + self.tabText(index).replace("📌 ", ""))
        self.setTabClosable(index, False)

    def unpin_tab(self, index):
        self.pinned_tabs.discard(index)
        self.setTabText(index, self.tabText(index).replace("📌 ", ""))
        self.setTabClosable(index, True)

    def close_tab(self, index):
        if index in self.pinned_tabs:
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.information(self, self.tr("تنبيه"), self.tr("لا يمكن إغلاق التبويب المثبت."))
            return
        important_tabs = [self.tr("الإعدادات"), self.tr("الحسابات العامة")]
        tab_title = self.tabText(index)
        if tab_title in important_tabs:
            from PySide6.QtWidgets import QMessageBox
            reply = QMessageBox.question(self, self.tr("تأكيد الإغلاق"), self.tr("هل أنت متأكد أنك تريد إغلاق تبويب '{}'؟").format(tab_title), QMessageBox.Yes | QMessageBox.No)
            if reply != QMessageBox.Yes:
                return
        self.removeTab(index)

    def close_all_tabs(self):
        indices = [i for i in range(self.count()) if i not in self.pinned_tabs]
        for i in sorted(indices, reverse=True):
            self.removeTab(i)

    def add_lazy_table(self, tab, data_loader):
        idx = self.addTab(tab, tab.windowTitle())
        tab._data_loaded = False
        def on_activated(i):
            if i == idx and not tab._data_loaded:
                data_loader(tab)
                tab._data_loaded = True
        self.currentChanged.connect(on_activated)
        return idx

    def get_tab_cache(self, tab_name):
        return self.tab_cache.get(tab_name)

    def set_tab_cache(self, tab_name, data):
        self.tab_cache[tab_name] = data

    def add_settings_tab(self):
        from PySide6.QtWidgets import QApplication
        for i in range(self.count()):
            if self.tabText(i) == self.tr("الإعدادات"):
                self.setCurrentIndex(i)
                return i
        settings_tab = SettingsTab(self.settings, self.event_bus, parent=self)
        idx = self.addTab(settings_tab, self.tr("الإعدادات"))
        self.setCurrentIndex(idx)
        QApplication.processEvents()  # إجبار Qt على معالجة الأحداث والرسم
        settings_tab.updateGeometry()
        settings_tab.repaint()
        settings_tab.show()
        self.updateGeometry()
        self.repaint()
        return idx 

    def open_inventory_tab(self, *_):
        # تحقق إذا كان التبويب مفتوح مسبقًا
        for i in range(self.count()):
            widget = self.widget(i)
            # تحقق من objectName الصحيح
            if widget.objectName() == "InventoryMainTab":
                self.setCurrentIndex(i)
                return
        # إذا لم يكن مفتوحًا، أضف التبويب
        tab = InventoryMainTab(self)
        idx = self.addTab(tab, self.tr("إدارة المخزون"))
        self.setCurrentIndex(idx) 

    def open_warehouses_tab(self, *_):
        # تحقق إذا كان التبويب مفتوح مسبقًا
        for i in range(self.count()):
            widget = self.widget(i)
            if widget.objectName() == "WarehousesMainTab":
                self.setCurrentIndex(i)
                return
        # إذا لم يكن مفتوحًا، أضف التبويب
        tab = WarehousesMainTab(self)
        idx = self.addTab(tab, self.tr("المخازن"))
        self.setCurrentIndex(idx)

    def open_sales_tab(self, *_):
        # تحقق إذا كان التبويب مفتوح مسبقًا
        for i in range(self.count()):
            widget = self.widget(i)
            if widget.objectName() == "SalesMainTab":
                self.setCurrentIndex(i)
                return
        # إذا لم يكن مفتوحًا، أضف التبويب
        from accounting_app.modules.sales.ui.sales_main_tab import SalesMainTab
        tab = SalesMainTab(self)
        idx = self.addTab(tab, self.tr("المبيعات"))
        self.setCurrentIndex(idx)

    def open_purchases_tab(self, *_):
        # تحقق إذا كان التبويب مفتوح مسبقًا
        for i in range(self.count()):
            widget = self.widget(i)
            if widget.objectName() == "PurchasesMainTab":
                self.setCurrentIndex(i)
                return
        # إذا لم يكن مفتوحًا، أضف التبويب
        from accounting_app.modules.purchases.ui.purchases_main_tab import PurchasesMainTab
        tab = PurchasesMainTab(self)
        idx = self.addTab(tab, self.tr("المشتريات"))
        self.setCurrentIndex(idx)

    # إزالة دوال فتح الحركات والجرد والتنبيهات من الشريط الجانبي 