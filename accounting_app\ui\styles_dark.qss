/* الشريط العلوي والجانبي */
#TopBar, #SideBar {
    background-color: #1F2937;
    color: #FFFFFF;
}
#TopBar QLabel, #TopBar QPushButton, #TopBar QToolButton {
    background: transparent;
    color: #FFFFFF;
    border: none;
}
#SideBarButton {
    background: #232e3c;
    color: #FFFFFF;
    border: none;
    border-radius: 8px;
    font-size: 15px;
    padding: 6px 0;
}
#SideBarButton:hover, #SideBarButton:pressed, #SideBarButton:selected {
    background-color: #3b82f6;
    color: #FFFFFF;
}

/* منطقة العمل الرئيسية */
QWidget#MainContent, QWidget#ContentArea {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #202632, stop:1 #11151a);
}

/* البطاقات والفريمات والفوتر */
QFrame, #<PERSON><PERSON>, QStatusBar {
    background: #2d3748;
    color: #F3F4F6;
    border-radius: 8px;
    border: 1px solid #374151;
}

QWidget {
    font-family: 'Cairo', 'Segoe UI', sans-serif;
    border-radius: 12px;
}
QWidget#SideBar {
    background: #1F2937;
}
QWidget#TopBar {
    background: #1F2937;
}

/* التبويبات */
QTabWidget::pane {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #232e3c, stop:1 #1F2937);
    border-radius: 16px;
    border: 1px solid #374151;
}
QTabBar::tab {
    background: #2d3748;
    color: #F3F4F6;
    border-radius: 8px 8px 0 0;
    padding: 8px 20px;
    margin: 0 2px;
    font-weight: bold;
    border: 1px solid #374151;
    border-bottom: none;
}
QTabBar::tab:selected {
    background: #374151;
    color: #fff;
    border-color: #3b82f6;
}
QTabBar::tab:!selected {
    background: #2d3748;
    color: #F3F4F6;
    border-color: #374151;
}

/* الأزرار العامة */
QPushButton {
    background: #2563eb;
    color: #FFFFFF;
    border-radius: 10px;
    font-weight: bold;
    padding: 10px 20px;
    font-size: 15px;
    border: none;
    margin: 4px 0;
}
QPushButton:hover {
    background: #3b82f6;
}
QPushButton:pressed {
    background: #1d4ed8;
}
QPushButton:disabled {
    background: #374151;
    color: #b0b0b0;
}

/* النصوص والحقول */
QLabel, QLineEdit, QComboBox {
    font-size: 15px;
    color: #FFFFFF;
}
QLineEdit, QComboBox {
    background: #374151;
    border-radius: 8px;
    padding: 6px 12px;
    border: 1.5px solid #4b5563;
    color: #FFFFFF;
}
QLineEdit:focus, QComboBox:focus {
    border: 2px solid #3b82f6;
    outline: none;
}

QStatusBar, #Footer {
    background: #2d3748;
    color: #F3F4F6;
    border-top: 1px solid #374151;
}
#SideBarButton:selected {
    background-color: #3b82f6;
    color: #FFFFFF;
} 