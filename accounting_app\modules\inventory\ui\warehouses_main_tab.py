from PySide6.QtWidgets import QWidget, QVBoxLayout, QTabWidget
from accounting_app.modules.inventory.ui.warehouse_view import WarehouseListTab

class WarehousesMainTab(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("WarehousesMainTab")
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout(self)
        self.tabs = QTabWidget(self)
        self.tabs.addTab(WarehouseListTab(self), self.tr("قائمة المستودعات"))
        # يمكن إضافة تبويبات أخرى هنا مستقبلاً
        layout.addWidget(self.tabs)
        self.setLayout(layout) 