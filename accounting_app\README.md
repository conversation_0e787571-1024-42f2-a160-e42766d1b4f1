# Smart Accounting App - برنامج المحاسبة الذكي

## 📋 **نظرة عامة**

برنامج محاسبة ذكي مبني بـ Python و PySide6 مع مساعد ذكي مدمج. يدعم اللغات العربية والإنجليزية والفرنسية.

## 🚀 **بدء سريع**

### **1. إعداد بيئة التطوير (مطور جديد):**
```bash
# إعداد بيئة التطوير الكاملة
make setup-dev

# أو يدوياً
python -m venv .venv
source .venv/bin/activate  # Linux/Mac
# .venv\Scripts\activate   # Windows
pip install -r dev-requirements.txt
pre-commit install
```

### **2. إعداد بيئة الإنتاج:**
```bash
# تثبيت التبعيات الأساسية
pip install -r requirements.txt

# أو التبعيات الدنيا (للموارد المحدودة)
pip install -r requirements-minimal.txt

# أو تثبيت الحزمة مباشرة
pip install -e .
```

### **3. تشغيل التطبيق:**
```bash
# تشغيل التطبيق كحزمة
make run

# أو مباشرة
python -m accounting_app.main

# أو باستخدام console script (إذا تم التثبيت)
smart-accounting
```

## 📦 **إدارة الاعتمادات**

### **ملفات الاعتمادات:**
- `requirements.txt` - تبعيات الإنتاج الأساسية
- `requirements-minimal.txt` - تبعيات الإنتاج الدنيا
- `dev-requirements.txt` - تبعيات التطوير (يشمل أدوات الاختبار والجودة)
- `requirements-lock.txt` - تبعيات مقفلة مع التبعيات الفرعية

### **أوامر مفيدة:**
```bash
# عرض جميع الأوامر المتاحة
make help

# تشغيل الاختبارات
make test

# فحص جودة الكود
make lint

# تنسيق الكود
make format

# فحص الأنواع
make type-check

# تحويل ملفات الترجمة
make convert-ts

# تثبيت الحزمة
make install-package
```

## 🏗️ **هيكل المشروع**

```
accounting_app/
├── __init__.py              # نقطة دخول الحزمة الرئيسية
├── main.py                  # نقطة دخول التطبيق
├── app_config.py            # إعدادات التطبيق المركزية
├── core/                    # النواة الأساسية
│   ├── __init__.py
│   ├── app_initializer.py   # مُهيئ التطبيق
│   ├── event_bus.py         # نظام الأحداث
│   ├── settings.py          # الإعدادات
│   ├── style_manager.py     # مدير الأنماط
│   ├── logger.py            # نظام التسجيل
│   └── error_handler.py     # معالج الأخطاء
├── controllers/             # المتحكمات
│   ├── __init__.py
│   ├── ai_controller.py     # متحكم الذكاء الاصطناعي
│   ├── language_controller.py # متحكم اللغة
│   ├── notification_controller.py # متحكم الإشعارات
│   └── sidebar_controller.py # متحكم الشريط الجانبي
├── ui/                      # واجهة المستخدم
│   ├── __init__.py
│   ├── main_window.py       # النافذة الرئيسية
│   ├── sidebar.py           # الشريط الجانبي
│   ├── content_area.py      # منطقة المحتوى
│   ├── ai_assistant.py      # المساعد الذكي
│   └── *.qss                # ملفات الأنماط
├── models/                  # النماذج
│   ├── __init__.py
│   └── user.py              # نموذج المستخدم
├── services/                # الخدمات
│   └── __init__.py
├── config/                  # الإعدادات
│   ├── __init__.py
│   ├── app_config.py        # إعدادات التطبيق
│   └── app_config.json      # ملف الإعدادات
├── translations/            # ملفات الترجمة
│   ├── __init__.py
│   ├── en.ts               # الإنجليزية
│   ├── ar.ts               # العربية
│   └── fr.ts               # الفرنسية
├── resources/               # الموارد
│   ├── __init__.py
│   ├── *.svg               # الأيقونات
│   ├── *.wav               # الأصوات
│   └── download_resources.py
├── scripts/                 # السكريبتات
├── tests/                   # الاختبارات
│   ├── __init__.py
│   ├── conftest.py
│   ├── test_core.py
│   └── test_controllers.py
├── docs/                    # التوثيق
└── logs/                    # السجلات
```

## 🎯 **الميزات الرئيسية**

### **🌐 دعم متعدد اللغات**
- العربية (اللغة الافتراضية)
- الإنجليزية
- الفرنسية
- تبديل فوري بين اللغات

### **🤖 مساعد ذكي مدمج**
- مساعد ذكي للاستعلامات المحاسبية
- دعم الأوامر الصوتية
- اقتراحات ذكية

### **🔔 نظام إشعارات متقدم**
- إشعارات فورية
- أصوات تنبيه قابلة للتخصيص
- لوحة إشعارات تفاعلية

### **🎨 واجهة مستخدم حديثة**
- تصميم عصري وسلس
- دعم الوضع المظلم/الفاتح
- تجربة مستخدم محسنة

### **⚡ أداء محسن**
- تحميل سريع
- استجابة فورية
- استهلاك موارد منخفض

## 🛠️ **التطوير**

### **إعداد بيئة التطوير:**
```bash
# إعداد كامل
make setup-dev

# تثبيت التبعيات فقط
pip install -r dev-requirements.txt

# إعداد pre-commit
pre-commit install
```

### **أدوات التطوير المتاحة:**
- **Black** - تنسيق الكود
- **isort** - ترتيب الاستيرادات
- **flake8** - فحص جودة الكود
- **mypy** - فحص الأنواع
- **pytest** - الاختبارات
- **pre-commit** - hooks قبل الـ commit

### **تشغيل الاختبارات:**
```bash
# جميع الاختبارات
make test

# اختبارات محددة
pytest tests/test_specific.py -v

# اختبارات مع تغطية
pytest --cov=accounting_app --cov-report=html
```

### **فحص جودة الكود:**
```bash
# فحص شامل
make lint

# تنسيق الكود
make format

# فحص الأنواع
make type-check
```

## 📚 **التوثيق**

### **الملفات المتاحة:**
- `docs/dependencies_management.md` - إدارة الاعتمادات
- `docs/events.md` - نظام الأحداث
- `docs/translation_guide.md` - دليل الترجمة
- `docs/effects_guide.md` - دليل التأثيرات

### **بناء التوثيق:**
```bash
# تثبيت أدوات التوثيق
pip install -e .[docs]

# بناء التوثيق
make docs
```

## 🌍 **الترجمة**

### **إضافة لغة جديدة:**
1. أنشئ ملف `translations/xx.ts`
2. أضف الترجمات
3. حول الملف إلى `.qm`:
   ```bash
   make convert-ts
   ```
4. أضف اللغة في `app_config.py`

### **اختبار الترجمة:**
```bash
make test-trans
```

## 🚀 **النشر**

### **بناء التطبيق:**
```bash
# بناء الحزمة
make build

# أو باستخدام setuptools
python setup.py build
```

### **تثبيت في بيئة الإنتاج:**
```bash
# تثبيت التبعيات الدنيا
pip install -r requirements-minimal.txt

# تشغيل التطبيق
python -m accounting_app.main
```

## 🔧 **الإعدادات**

### **ملف الإعدادات (`config.json`):**
```json
{
  "language": "ar",
  "theme": "dark",
  "notifications": {
    "sound": true,
    "volume": 0.7
  },
  "ai": {
    "enabled": true,
    "voice_enabled": false
  }
}
```

## 🐛 **استكشاف الأخطاء**

### **مشاكل شائعة:**

1. **خطأ في الاستيراد:**
   ```bash
   # تأكد من تثبيت التبعيات
   pip install -r requirements.txt
   
   # أو تثبيت الحزمة
   pip install -e .
   ```

2. **خطأ في ملفات الترجمة:**
   ```bash
   # تحويل ملفات الترجمة
   make convert-ts
   
   # اختبار الترجمة
   make test-trans
   ```

3. **خطأ في الموارد:**
   ```bash
   # تحميل الموارد
   python resources/download_resources.py
   ```

### **تشغيل في وضع التصحيح:**
```bash
# تشغيل مع تسجيل مفصل
python -m accounting_app.main --debug

# تشغيل الاختبارات مع تغطية
make test-coverage
```

## 📄 **الترخيص**

هذا المشروع مرخص تحت رخصة MIT. راجع ملف `LICENSE` للتفاصيل.

## 🤝 **المساهمة**

نرحب بالمساهمات! يرجى قراءة دليل المساهمة قبل البدء.

### **خطوات المساهمة:**
1. Fork المشروع
2. إنشاء branch للميزة الجديدة
3. Commit التغييرات
4. Push إلى Branch
5. إنشاء Pull Request

## 📞 **الدعم**

- **البريد الإلكتروني:** <EMAIL>
- **المسائل:** [GitHub Issues](https://github.com/smartaccounting/app/issues)
- **التوثيق:** [Read the Docs](https://smartaccounting.readthedocs.io/)

---

**Smart Accounting App** - برنامج المحاسبة الذكي 🚀 

## 📝 ملاحظة حول السجلات (Logs)

- جميع سجلات التطبيق (logs) يتم حفظها تلقائيًا في المجلد `logs/` داخل المشروع، وتحديدًا في الملفات:
  - `logs/app.log` (السجلات العامة)
  - `logs/errors.log` (سجلات الأخطاء)
  - `logs/debug.log` (سجلات التصحيح)
- لا يتم استخدام أو إنشاء أي ملفات سجلات في أماكن أخرى مثل `accounting_app/app.log` أو `/app.log`.
- إذا وجدت ملفات سجلات في مواقع أخرى، يمكنك حذفها بأمان.
- نظام التسجيل مهيأ تلقائيًا ولا يحتاج لتعديل يدوي للمسارات. 