#!/usr/bin/env python3
"""
تكوين التطبيق الشامل
Comprehensive Application Configuration
"""

import os
import json
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict, field
from enum import Enum

class Environment(Enum):
    """بيئات التطبيق"""
    DEVELOPMENT = "development"
    TESTING = "testing"
    PRODUCTION = "production"

class LogLevel(Enum):
    """مستويات التسجيل"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"

@dataclass
class DatabaseConfig:
    """تكوين قاعدة البيانات"""
    host: str = "localhost"
    port: int = 5432
    name: str = "smart_accounting"
    user: str = "postgres"
    password: str = ""
    pool_size: int = 10
    max_overflow: int = 20

@dataclass
class AIConfig:
    """تكوين الذكاء الاصطناعي"""
    enabled: bool = True
    api_key: str = ""
    model: str = "gpt-3.5-turbo"
    max_tokens: int = 1000
    temperature: float = 0.7
    voice_enabled: bool = False
    language_model: str = "arabic"

@dataclass
class SecurityConfig:
    """تكوين الأمان"""
    session_timeout: int = 3600  # ثانية
    max_login_attempts: int = 3
    password_min_length: int = 8
    require_special_chars: bool = True
    encryption_key: str = ""
    jwt_secret: str = ""

@dataclass
class NotificationConfig:
    """تكوين الإشعارات"""
    sound_enabled: bool = True
    volume: float = 0.7
    duration: int = 5000  # مللي ثانية
    position: str = "top-right"
    max_notifications: int = 10

@dataclass
class UIConfig:
    """تكوين واجهة المستخدم"""
    theme: str = "dark"
    language: str = "ar"
    font_size: int = 12
    window_width: int = 1200
    window_height: int = 800
    auto_save: bool = True
    show_tooltips: bool = True

@dataclass
class PerformanceConfig:
    """تكوين الأداء"""
    cache_enabled: bool = True
    cache_size: int = 1000
    auto_backup: bool = True
    backup_interval: int = 3600  # ثانية
    max_file_size: int = 10 * 1024 * 1024  # 10 MB

@dataclass
class AppConfig:
    """التكوين الرئيسي للتطبيق"""
    
    # معلومات التطبيق الأساسية
    app_name: str = "Smart Accounting App"
    app_version: str = "1.0.0"
    app_description: str = "برنامج محاسبة ذكي مع مساعد ذكي مدمج"
    
    # بيئة التطبيق
    environment: Environment = Environment.DEVELOPMENT
    
    # المسارات
    base_path: Path = Path(__file__).parent.parent
    resources_path: Path = Path("resources")
    translations_path: Path = Path("translations")
    logs_path: Path = Path("logs")
    data_path: Path = Path("data")
    
    # التكوينات الفرعية
    database: DatabaseConfig = field(default_factory=DatabaseConfig)
    ai: AIConfig = field(default_factory=AIConfig)
    security: SecurityConfig = field(default_factory=SecurityConfig)
    notifications: NotificationConfig = field(default_factory=NotificationConfig)
    ui: UIConfig = field(default_factory=UIConfig)
    performance: PerformanceConfig = field(default_factory=PerformanceConfig)
    
    # إعدادات التسجيل
    log_level: LogLevel = LogLevel.INFO
    log_to_file: bool = True
    log_to_console: bool = True
    
    # إعدادات التطوير
    debug_mode: bool = True
    auto_reload: bool = False
    hot_reload: bool = False
    
    def __post_init__(self):
        """تهيئة بعد الإنشاء"""
        # تحويل المسارات إلى Path objects
        if isinstance(self.base_path, str):
            self.base_path = Path(self.base_path)
        if isinstance(self.resources_path, str):
            self.resources_path = Path(self.resources_path)
        if isinstance(self.translations_path, str):
            self.translations_path = Path(self.translations_path)
        if isinstance(self.logs_path, str):
            self.logs_path = Path(self.logs_path)
        if isinstance(self.data_path, str):
            self.data_path = Path(self.data_path)
    
    @classmethod
    def load_from_file(cls, config_path: str) -> 'AppConfig':
        """تحميل التكوين من ملف"""
        config_path = Path(config_path)
        
        if not config_path.exists():
            # إنشاء تكوين افتراضي
            config = cls()
            config.save_to_file(config_path)
            return config
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # تحويل البيانات إلى كائنات التكوين
            config = cls()
            
            # تحديث التكوينات الفرعية
            if 'database' in data:
                config.database = DatabaseConfig(**data['database'])
            if 'ai' in data:
                config.ai = AIConfig(**data['ai'])
            if 'security' in data:
                config.security = SecurityConfig(**data['security'])
            if 'notifications' in data:
                config.notifications = NotificationConfig(**data['notifications'])
            if 'ui' in data:
                config.ui = UIConfig(**data['ui'])
            if 'performance' in data:
                config.performance = PerformanceConfig(**data['performance'])
            
            # تحديث الإعدادات العامة
            for key, value in data.items():
                if hasattr(config, key) and not key.startswith('_'):
                    if key == 'environment':
                        config.environment = Environment(value)
                    elif key == 'log_level':
                        config.log_level = LogLevel(value)
                    else:
                        setattr(config, key, value)
            
            return config
            
        except Exception as e:
            print(f"خطأ في تحميل التكوين: {e}")
            return cls()
    
    def save_to_file(self, config_path: str):
        """حفظ التكوين إلى ملف"""
        config_path = Path(config_path)
        config_path.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            # تحويل التكوين إلى قاموس
            data = asdict(self)
            
            # تحويل Enum إلى string
            data['environment'] = self.environment.value
            data['log_level'] = self.log_level.value
            
            # تحويل Path إلى string
            data['base_path'] = str(self.base_path)
            data['resources_path'] = str(self.resources_path)
            data['translation_path'] = str(self.translations_path)
            data['logs_path'] = str(self.logs_path)
            data['data_path'] = str(self.data_path)
            
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"خطأ في حفظ التكوين: {e}")
    
    def get_resource_path(self, resource_name: str) -> Path:
        """الحصول على مسار مورد معين"""
        return self.resources_path / resource_name
    
    def get_translation_path(self, language: str) -> Path:
        """الحصول على مسار ملف الترجمة"""
        return self.translations_path / f"{language}.qm"
    
    def get_log_path(self, log_type: str = "app") -> Path:
        """الحصول على مسار ملف السجل"""
        self.logs_path.mkdir(exist_ok=True)
        return self.logs_path / f"{log_type}.log"
    
    def get_data_path(self, filename: str) -> Path:
        """الحصول على مسار ملف البيانات"""
        self.data_path.mkdir(exist_ok=True)
        return self.data_path / filename
    
    def is_development(self) -> bool:
        """التحقق من كون البيئة تطوير"""
        return self.environment == Environment.DEVELOPMENT
    
    def is_production(self) -> bool:
        """التحقق من كون البيئة إنتاج"""
        return self.environment == Environment.PRODUCTION
    
    def is_testing(self) -> bool:
        """التحقق من كون البيئة اختبار"""
        return self.environment == Environment.TESTING
    
    def update_ui_config(self, **kwargs):
        """تحديث تكوين واجهة المستخدم"""
        for key, value in kwargs.items():
            if hasattr(self.ui, key):
                setattr(self.ui, key, value)
    
    def update_ai_config(self, **kwargs):
        """تحديث تكوين الذكاء الاصطناعي"""
        for key, value in kwargs.items():
            if hasattr(self.ai, key):
                setattr(self.ai, key, value)
    
    def validate(self) -> bool:
        """التحقق من صحة التكوين"""
        try:
            # التحقق من المسارات
            if not self.base_path.exists():
                print(f"خطأ: مجلد المشروع غير موجود: {self.base_path}")
                return False
            
            # التحقق من إعدادات الأمان
            if self.security.password_min_length < 6:
                print("خطأ: الحد الأدنى لطول كلمة المرور يجب أن يكون 6 على الأقل")
                return False
            
            # التحقق من إعدادات الإشعارات
            if not 0 <= self.notifications.volume <= 1:
                print("خطأ: مستوى الصوت يجب أن يكون بين 0 و 1")
                return False
            
            return True
            
        except Exception as e:
            print(f"خطأ في التحقق من التكوين: {e}")
            return False

# إنشاء مثيل عام للتكوين
app_config = AppConfig()

def load_config(config_path: str = "config/app_config.json") -> AppConfig:
    """تحميل تكوين التطبيق"""
    global app_config
    app_config = AppConfig.load_from_file(config_path)
    return app_config

def get_config() -> AppConfig:
    """الحصول على تكوين التطبيق الحالي"""
    return app_config

def save_config(config_path: str = "config/app_config.json"):
    """حفظ تكوين التطبيق"""
    app_config.save_to_file(config_path)

# دوال مساعدة للوصول السريع للتكوين
def get_database_config() -> DatabaseConfig:
    """الحصول على تكوين قاعدة البيانات"""
    return app_config.database

def get_ai_config() -> AIConfig:
    """الحصول على تكوين الذكاء الاصطناعي"""
    return app_config.ai

def get_security_config() -> SecurityConfig:
    """الحصول على تكوين الأمان"""
    return app_config.security

def get_ui_config() -> UIConfig:
    """الحصول على تكوين واجهة المستخدم"""
    return app_config.ui

def get_notification_config() -> NotificationConfig:
    """الحصول على تكوين الإشعارات"""
    return app_config.notifications

def get_performance_config() -> PerformanceConfig:
    """الحصول على تكوين الأداء"""
    return app_config.performance 