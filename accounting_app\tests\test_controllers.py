#!/usr/bin/env python3
"""
اختبارات المتحكمات
Controllers Tests
"""

import pytest
from unittest.mock import Mock, patch, MagicMock

from PySide6.QtWidgets import QApplication, QWidget
from PySide6.QtCore import Qt

# استيراد من الحزمة
from accounting_app import (
    LanguageController,
    NotificationController,
    AIController,
    SidebarController,
    event_bus,
    AppSettings,
    AppEvents
)


class TestLanguageController:
    """اختبارات متحكم اللغة"""
    
    @pytest.fixture
    def app(self):
        """إنشاء تطبيق Qt للاختبار"""
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        yield app
    
    @pytest.fixture
    def settings(self):
        """إنشاء إعدادات للاختبار"""
        return AppSettings()
    
    def test_language_controller_initialization(self, app, settings):
        """اختبار تهيئة متحكم اللغة"""
        controller = LanguageController(app, event_bus, settings)
        assert controller.app == app
        assert controller.settings == settings
        assert controller.current_lang == settings.language
    
    def test_language_switching(self, app, settings):
        """اختبار تبديل اللغة"""
        controller = LanguageController(app, event_bus, settings)
        
        # اختبار تغيير اللغة
        controller.set_language("en")
        assert controller.get_current_language() == "en"
        
        controller.set_language("fr")
        assert controller.get_current_language() == "fr"
    
    def test_supported_languages(self, app, settings):
        """اختبار اللغات المدعومة"""
        controller = LanguageController(app, event_bus, settings)
        supported = controller.get_supported_languages()
        
        assert "ar" in supported
        assert "en" in supported
        assert "fr" in supported
    
    def test_invalid_language(self, app, settings):
        """اختبار لغة غير صحيحة"""
        controller = LanguageController(app, event_bus, settings)
        original_lang = controller.get_current_language()
        
        # محاولة تعيين لغة غير موجودة
        controller.set_language("invalid_lang")
        assert controller.get_current_language() == original_lang


class TestNotificationController:
    """اختبارات متحكم الإشعارات"""
    
    @pytest.fixture
    def notification_panel(self):
        """إنشاء لوحة إشعارات وهمية"""
        panel = Mock()
        panel.add_notification = Mock()
        return panel
    
    def test_notification_controller_initialization(self, notification_panel):
        """اختبار تهيئة متحكم الإشعارات"""
        controller = NotificationController(notification_panel)
        assert controller.notification_panel == notification_panel
    
    def test_notification_success(self, notification_panel):
        """اختبار إضافة إشعار بنجاح"""
        controller = NotificationController(notification_panel)
        
        result = controller.notify("Test notification", "info")
        assert result is True
        notification_panel.add_notification.assert_called_once_with("Test notification", "info")
    
    def test_notification_failure(self, notification_panel):
        """اختبار فشل إضافة إشعار"""
        notification_panel.add_notification.side_effect = Exception("Test error")
        controller = NotificationController(notification_panel)
        
        result = controller.notify("Test notification", "info")
        assert result is False


class TestAIController:
    """اختبارات متحكم الذكاء الاصطناعي"""
    
    @pytest.fixture
    def ai_assistant(self):
        """إنشاء مساعد ذكي وهمي"""
        assistant = Mock()
        assistant.send_message = Mock()
        assistant.receive_message = Mock()
        return assistant
    
    @pytest.fixture
    def settings(self):
        """إنشاء إعدادات للاختبار"""
        return AppSettings()
    
    def test_ai_controller_initialization(self, ai_assistant, settings):
        """اختبار تهيئة متحكم الذكاء الاصطناعي"""
        controller = AIController(ai_assistant, event_bus, settings)
        assert controller.ai_assistant == ai_assistant
        assert controller.settings == settings
    
    def test_send_message(self, ai_assistant, settings):
        """اختبار إرسال رسالة"""
        controller = AIController(ai_assistant, event_bus, settings)
        
        controller.send_message("Hello AI")
        ai_assistant.send_message.assert_called_once_with("Hello AI")
    
    def test_receive_message(self, ai_assistant, settings):
        """اختبار استقبال رسالة"""
        controller = AIController(ai_assistant, event_bus, settings)
        
        # محاكاة استقبال رسالة
        controller.receive_message("AI Response")
        ai_assistant.receive_message.assert_called_once_with("AI Response")
    
    def test_ai_enabled_setting(self, ai_assistant, settings):
        """اختبار إعداد تفعيل الذكاء الاصطناعي"""
        controller = AIController(ai_assistant, event_bus, settings)
        
        # اختبار عندما يكون الذكاء الاصطناعي مفعل
        settings.set("ai_enabled", True)
        assert controller.is_ai_enabled() is True
        
        # اختبار عندما يكون الذكاء الاصطناعي معطل
        settings.set("ai_enabled", False)
        assert controller.is_ai_enabled() is False


class TestSidebarController:
    """اختبارات متحكم الشريط الجانبي"""
    
    @pytest.fixture
    def sidebar(self):
        """إنشاء شريط جانبي وهمي"""
        sidebar = Mock()
        sidebar.buttons = [Mock(), Mock(), Mock()]
        sidebar.window = Mock(return_value=Mock())
        return sidebar
    
    @pytest.fixture
    def content_area(self):
        """إنشاء منطقة محتوى وهمية"""
        content_area = Mock()
        content_area.count = Mock(return_value=2)
        content_area.tabText = Mock(side_effect=lambda i: f"Tab {i}")
        content_area.setCurrentIndex = Mock()
        content_area.addTab = Mock()
        return content_area
    
    def test_sidebar_controller_initialization(self, sidebar, content_area):
        """اختبار تهيئة متحكم الشريط الجانبي"""
        controller = SidebarController(sidebar, content_area)
        assert controller.sidebar == sidebar
        assert controller.content_area == content_area
    
    def test_button_connections(self, sidebar, content_area):
        """اختبار توصيل الأزرار"""
        controller = SidebarController(sidebar, content_area)
        
        # التحقق من توصيل جميع الأزرار
        for button in sidebar.buttons:
            assert button.clicked.connect.called
    
    def test_button_click_handling(self, sidebar, content_area):
        """اختبار معالجة النقر على الأزرار"""
        controller = SidebarController(sidebar, content_area)
        
        # محاكاة النقر على زر
        button = sidebar.buttons[0]
        button.clicked.emit()
        
        # التحقق من أن المنطقة تم تحديثها
        assert content_area.setCurrentIndex.called
    
    def test_new_tab_creation(self, sidebar, content_area):
        """اختبار إنشاء تبويب جديد"""
        controller = SidebarController(sidebar, content_area)
        
        # محاكاة إنشاء تبويب جديد
        controller.create_new_tab("New Tab")
        
        # التحقق من إضافة التبويب
        content_area.addTab.assert_called_once()


class TestControllerIntegration:
    """اختبارات تكامل المتحكمات"""
    
    @pytest.fixture
    def app(self):
        """إنشاء تطبيق Qt للاختبار"""
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        yield app
    
    @pytest.fixture
    def settings(self):
        """إنشاء إعدادات للاختبار"""
        return AppSettings()
    
    def test_event_bus_integration(self, app, settings):
        """اختبار تكامل نظام الأحداث"""
        # إنشاء متحكم اللغة
        lang_controller = LanguageController(app, event_bus, settings)
        
        # تسجيل مستمع للحدث
        event_received = False
        
        def on_language_changed(data):
            nonlocal event_received
            event_received = True
        
        event_bus.subscribe(AppEvents.LANGUAGE_CHANGED, on_language_changed)
        
        # تغيير اللغة
        lang_controller.set_language("en")
        
        # التحقق من استقبال الحدث
        assert event_received is True
    
    def test_settings_persistence(self, app, settings):
        """اختبار استمرارية الإعدادات"""
        # تغيير إعداد
        settings.language = "en"
        settings.save()
        
        # إنشاء إعدادات جديدة وتحميلها
        new_settings = AppSettings(settings.config_path)
        assert new_settings.language == "en"


if __name__ == "__main__":
    pytest.main([__file__, "-v"]) 