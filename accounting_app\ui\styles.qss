/* الشريط العلوي والجانبي */
#TopBar, #SideBar {
    background-color: #1F2937;
    color: #FFFFFF;
}
#TopBar QLabel, #TopBar QPushButton, #TopBar QToolButton {
    background: transparent;
    color: #FFFFFF;
    border: none;
}
#SideBarButton {
    background: #1F2937;
    color: #FFFFFF;
    border: none;
    border-radius: 8px;
    font-size: 15px;
    padding: 6px 0;
}
#SideBarButton:hover, #SideBarButton:pressed, #SideBarButton:selected {
    background-color: #E5E7EB;
    color: #1F2937;
}
Q<PERSON><PERSON>e, QStatusBar, #Footer {
    background-color: #E5E7EB;
    color: #374151;
    border-radius: 8px;
}
QWidget {
    font-family: 'Cairo', 'Segoe UI', sans-serif;
    border-radius: 12px;
} 
QWidget#SideBar {
    background: #232e3c;
}
QWidget#TopBar {
    background: #232e3c;
}
#MainContent, #ContentArea {
    background-color: #FFFFFF;
}
QTabWidget::pane {
    background: #FFFFFF;
    border-radius: 16px;
    border: 1px solid #E5E7EB;
}
QPushButton {
    background: #E5E7EB;
    color: #374151;
    border-radius: 10px;
    font-weight: bold;
    padding: 10px 20px;
    font-size: 15px;
    border: none;
    margin: 4px 0;
    /* تم إزالة box-shadow واستبداله بـ QGraphicsDropShadowEffect */
}
QPushButton:hover {
    background: #b0b0b0;
}
QPushButton:pressed {
    background: #9ca3af;
}
QPushButton:disabled {
    background: #f3f4f6;
    color: #a1a1aa;
}
QLabel, QLineEdit, QComboBox {
    font-size: 15px;
    color: #374151;
}
QLineEdit, QComboBox {
    background: #E5E7EB;
    border-radius: 8px;
    padding: 6px 12px;
    border: 1.5px solid #b0b0b0;
    color: #374151;
}
QLineEdit:focus, QComboBox:focus {
    border: 2px solid #3b82f6;
    outline: none;
}
QTabBar::tab {
    background: #E5E7EB;
    color: #374151;
    border-radius: 8px 8px 0 0;
    padding: 8px 20px;
    margin: 0 2px;
    font-weight: bold;
}
QTabBar::tab:selected {
    background: #fff;
    color: #374151;
}
QTabBar::tab:!selected {
    background: #E5E7EB;
    color: #374151;
}
QStatusBar, #Footer {
    background: #E5E7EB;
    color: #374151;
    border-top: 1px solid #E5E7EB;
} 
#SideBarButton:selected {
    background-color: #E5E7EB;
    color: #1F2937;
} 