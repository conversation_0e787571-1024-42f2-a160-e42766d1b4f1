"""
وحدة منع تشغيل مثيلات متعددة للتطبيق
تستخدم ملف قفل ومنفذ Socket للتحقق من وجود نسخة أخرى قيد التشغيل
"""

import os
import sys
import socket
import tempfile
import atexit
import signal
import threading
import time
from pathlib import Path
from typing import Optional, Tuple
import logging

logger = logging.getLogger(__name__)


class SingleInstanceApp:
    """
    فئة لإدارة تشغيل نسخة واحدة من التطبيق
    """
    
    def __init__(self, app_name: str = "accounting_app", port: int = 12345):
        """
        تهيئة مدير النسخة الواحدة
        
        Args:
            app_name: اسم التطبيق (يستخدم في اسم ملف القفل)
            port: رقم المنفذ للتحقق من وجود نسخة أخرى
        """
        self.app_name = app_name
        self.port = port
        self.lock_file = None
        self.socket_server = None
        self.is_running = False
        self._cleanup_registered = False
        
    def acquire_lock(self) -> bool:
        """
        محاولة الحصول على قفل التطبيق
        
        Returns:
            True إذا نجحت في الحصول على القفل، False إذا كان هناك نسخة أخرى قيد التشغيل
        """
        try:
            print(f"🔒 محاولة الحصول على قفل التطبيق: {self.app_name}")
            
            # إنشاء ملف القفل
            lock_file_path = self._get_lock_file_path()
            self.lock_file = open(lock_file_path, 'w')
            
            # كتابة معرف العملية
            current_pid = os.getpid()
            self.lock_file.write(str(current_pid))
            self.lock_file.flush()
            print(f"📝 تم كتابة PID الحالي في ملف القفل: {current_pid}")
            
            # محاولة الحصول على قفل الملف
            if not self._acquire_file_lock():
                print("❌ فشل في الحصول على قفل الملف")
                logger.warning("فشل في الحصول على قفل الملف")
                return False
            
            # محاولة فتح منفذ Socket
            if not self._acquire_socket_lock():
                print("❌ فشل في الحصول على قفل المنفذ")
                logger.warning("فشل في الحصول على قفل المنفذ")
                return False
            
            # تسجيل دالة التنظيف
            if not self._cleanup_registered:
                atexit.register(self.release_lock)
                signal.signal(signal.SIGINT, self._signal_handler)
                signal.signal(signal.SIGTERM, self._signal_handler)
                self._cleanup_registered = True
                print("✅ تم تسجيل دوال التنظيف")
            
            self.is_running = True
            print("🎉 تم الحصول على قفل التطبيق بنجاح!")
            logger.info("تم الحصول على قفل التطبيق بنجاح")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في الحصول على قفل التطبيق: {e}")
            logger.error(f"خطأ في الحصول على قفل التطبيق: {e}")
            self.release_lock()
            return False
    
    def release_lock(self):
        """إطلاق قفل التطبيق"""
        try:
            self.is_running = False
            
            # إغلاق منفذ Socket
            if self.socket_server:
                try:
                    self.socket_server.close()
                except:
                    pass
                self.socket_server = None
            
            # إغلاق ملف القفل
            if self.lock_file:
                try:
                    self.lock_file.close()
                except:
                    pass
                self.lock_file = None
            
            # حذف ملف القفل
            lock_file_path = self._get_lock_file_path()
            if os.path.exists(lock_file_path):
                try:
                    os.remove(lock_file_path)
                except:
                    pass
            
            logger.info("تم إطلاق قفل التطبيق")
            
        except Exception as e:
            logger.error(f"خطأ في إطلاق قفل التطبيق: {e}")
    
    def _get_lock_file_path(self) -> str:
        """الحصول على مسار ملف القفل"""
        temp_dir = tempfile.gettempdir()
        return os.path.join(temp_dir, f"{self.app_name}.lock")
    
    def _acquire_file_lock(self) -> bool:
        """محاولة الحصول على قفل الملف"""
        try:
            # فحص وجود ملف قفل قديم
            lock_file_path = self._get_lock_file_path()
            print(f"🔍 فحص ملف القفل: {lock_file_path}")
            
            if os.path.exists(lock_file_path):
                print(f"⚠️  وجد ملف قفل موجود: {lock_file_path}")
                # محاولة قراءة معرف العملية من الملف
                try:
                    with open(lock_file_path, 'r') as f:
                        pid_str = f.read().strip()
                        print(f"📄 محتوى ملف القفل: '{pid_str}'")
                        if pid_str.isdigit():
                            pid = int(pid_str)
                            current_pid = os.getpid()
                            print(f"🔢 PID من الملف: {pid}, PID الحالي: {current_pid}")
                            
                            # إذا كان PID هو نفس العملية الحالية، نتجاهل القفل
                            if pid == current_pid:
                                print(f"✅ نفس العملية، نتجاهل القفل")
                                return True
                            
                            # فحص ما إذا كانت العملية لا تزال قيد التشغيل
                            if self._is_process_running(pid):
                                print(f"❌ العملية {pid} لا تزال قيد التشغيل!")
                                logger.warning(f"التطبيق قيد التشغيل بالفعل (PID: {pid})")
                                return False
                            else:
                                # العملية غير موجودة، حذف الملف القديم
                                print(f"✅ العملية {pid} غير موجودة، حذف ملف القفل")
                                logger.info("حذف ملف قفل قديم (عملية غير موجودة)")
                                os.remove(lock_file_path)
                        else:
                            # الملف تالف أو لا يحتوي PID صالح
                            print(f"⚠️  الملف تالف أو لا يحتوي PID صالح: '{pid_str}'")
                            logger.info("ملف القفل تالف أو لا يحتوي PID صالح، سيتم حذفه")
                            os.remove(lock_file_path)
                except Exception as e:
                    # في حالة خطأ في قراءة الملف، حذفه تلقائيًا
                    print(f"❌ خطأ في قراءة ملف القفل: {e}")
                    logger.info(f"خطأ في قراءة ملف القفل: {e}، سيتم حذفه تلقائيًا")
                    try:
                        os.remove(lock_file_path)
                        print("✅ تم حذف ملف القفل التالف")
                    except Exception as e2:
                        print(f"❌ فشل في حذف ملف القفل التالف: {e2}")
                        logger.warning(f"فشل في حذف ملف القفل التالف: {e2}")
            else:
                print("✅ لا يوجد ملف قفل قديم")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في فحص قفل الملف: {e}")
            logger.error(f"خطأ في فحص قفل الملف: {e}")
            return False
    
    def _acquire_socket_lock(self) -> bool:
        """محاولة الحصول على قفل المنفذ"""
        try:
            print(f"🔌 محاولة حجز المنفذ: localhost:{self.port}")
            # محاولة فتح منفذ Socket
            self.socket_server = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket_server.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.socket_server.bind(('localhost', self.port))
            self.socket_server.listen(1)
            
            print(f"✅ تم حجز المنفذ بنجاح: localhost:{self.port}")
            
            # بدء خيط لاستقبال الاتصالات
            self._start_socket_thread()
            
            return True
            
        except socket.error as e:
            print(f"❌ فشل في حجز المنفذ {self.port}: {e}")
            logger.warning(f"المنفذ {self.port} محجوز بالفعل: {e}")
            return False
        except Exception as e:
            print(f"❌ خطأ في فتح منفذ Socket: {e}")
            logger.error(f"خطأ في فتح منفذ Socket: {e}")
            return False
    
    def _start_socket_thread(self):
        """بدء خيط لاستقبال الاتصالات على المنفذ"""
        def socket_listener():
            while self.is_running:
                try:
                    self.socket_server.settimeout(1.0)
                    client, addr = self.socket_server.accept()
                    client.close()
                except socket.timeout:
                    continue
                except Exception:
                    break
        
        thread = threading.Thread(target=socket_listener, daemon=True)
        thread.start()
    
    def _is_process_running(self, pid: int) -> bool:
        """فحص ما إذا كانت العملية قيد التشغيل"""
        try:
            # محاولة إرسال إشارة 0 للعملية (لا تفعل شيئاً)
            os.kill(pid, 0)
            return True
        except OSError:
            return False
    
    def _signal_handler(self, signum, frame):
        """معالج الإشارات للتنظيف عند الإغلاق"""
        logger.info(f"استلام إشارة {signum}، إغلاق التطبيق...")
        self.release_lock()
        sys.exit(0)
    
    def check_if_running(self) -> Tuple[bool, Optional[str]]:
        """
        فحص ما إذا كان التطبيق قيد التشغيل بالفعل
        
        Returns:
            (is_running, message): هل التطبيق قيد التشغيل ورسالة توضيحية
        """
        try:
            # فحص ملف القفل
            lock_file_path = self._get_lock_file_path()
            if os.path.exists(lock_file_path):
                try:
                    with open(lock_file_path, 'r') as f:
                        pid_str = f.read().strip()
                        if pid_str.isdigit():
                            pid = int(pid_str)
                            if self._is_process_running(pid):
                                return True, f"التطبيق قيد التشغيل بالفعل (PID: {pid})"
                except:
                    pass
            
            # فحص المنفذ
            try:
                test_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                test_socket.settimeout(1.0)
                result = test_socket.connect_ex(('localhost', self.port))
                test_socket.close()
                
                if result == 0:
                    return True, f"المنفذ {self.port} محجوز بالفعل"
            except:
                pass
            
            return False, None
            
        except Exception as e:
            logger.error(f"خطأ في فحص حالة التطبيق: {e}")
            return False, None


def ensure_single_instance(app_name: str = "accounting_app", port: int = 12345) -> SingleInstanceApp:
    """
    دالة مساعدة لضمان تشغيل نسخة واحدة من التطبيق
    
    Args:
        app_name: اسم التطبيق
        port: رقم المنفذ
        
    Returns:
        كائن SingleInstanceApp إذا نجح في الحصول على القفل
        
    Raises:
        RuntimeError: إذا كان التطبيق قيد التشغيل بالفعل
    """
    instance = SingleInstanceApp(app_name, port)
    
    # فحص ما إذا كان التطبيق قيد التشغيل
    is_running, message = instance.check_if_running()
    if is_running:
        raise RuntimeError(f"لا يمكن تشغيل نسخة أخرى من التطبيق: {message}")
    
    # محاولة الحصول على القفل
    if not instance.acquire_lock():
        raise RuntimeError("فشل في الحصول على قفل التطبيق")
    
    return instance 