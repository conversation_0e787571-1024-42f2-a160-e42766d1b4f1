from PySide6.QtWidgets import QWidget, QVBoxLayout, QPushButton
from PySide6.QtCore import Qt
from PySide6.QtGui import QIcon
import logging
from .toast import show_error_toast
from .effects import ShadowEffects
from accounting_app.core.events import AppEvents
from accounting_app.core.style_manager import get_resource_path

class Sidebar(QWidget):
    """الشريط الجانبي الرئيسي للبرنامج."""
    def __init__(self, event_bus, language_controller=None, settings=None, parent=None):
        super().__init__(parent)
        self.event_bus = event_bus
        self.language_controller = language_controller
        self.settings = settings
        self.setObjectName("SideBar")
        self.setAttribute(Qt.WA_StyledBackground, True)
        self.setFixedWidth(220)
        self.buttons = []
        self._init_ui()
        
        # الاشتراك في حدث تغيير اللغة باستخدام EventBus
        self.event_bus.subscribe(AppEvents.LANGUAGE_CHANGED, self.reload_texts)
        
        # تطبيق الاتجاه الصحيح عند الإنشاء
        self.update_layout_direction()

    def _init_ui(self):
        try:
            layout = QVBoxLayout()
            layout.setContentsMargins(8, 16, 8, 16)
            layout.setSpacing(8)
            # قائمة معرفات الأزرار
            button_keys = [
                "home", "accounts", "sales", "purchases", "inventory",
                "pos", "clients", "cash", "assets",
                "payroll", "reports", "settings", "test_loading"
            ]
            button_texts = {
                "home": self.tr("الصفحة الرئيسية"),
                "accounts": self.tr("الحسابات العامة"),
                "sales": self.tr("المبيعات"),
                "purchases": self.tr("المشتريات"),
                "inventory": self.tr("إدارة المخزون"),
                "pos": self.tr("نقطة البيع"),
                "clients": self.tr("العملاء والموردين"),
                "cash": self.tr("النقد والبنوك"),
                "assets": self.tr("الأصول الثابتة"),
                "payroll": self.tr("الرواتب"),
                "reports": self.tr("التقارير"),
                "settings": self.tr("الإعدادات"),
                "test_loading": self.tr("اختبار التحميل")
            }
            # تحميل الأيقونات مع معالجة الأخطاء
            try:
                icon_map = {
                    "home": QIcon(get_resource_path("home.svg")),
                    "settings": QIcon(get_resource_path("settings.svg")),
                    "test_loading": QIcon(get_resource_path("loading.svg"))
                }
            except Exception as e:
                logging.warning(f"Failed to load sidebar icons: {e}")
                icon_map = {}
            self.buttons.clear()
            # إزالة العناصر القديمة من الـ layout
            while self.layout() and self.layout().count():
                item = self.layout().takeAt(0)
                widget = item.widget()
                if widget:
                    widget.deleteLater()
            for key in button_keys:
                btn = QPushButton(button_texts[key])
                btn.setObjectName("SideBarButton")
                btn.setFixedHeight(40)
                btn.setEnabled(True)
                if key in icon_map:
                    btn.setIcon(icon_map[key])
                
                # تطبيق تأثير الظل على أزرار الشريط الجانبي
                ShadowEffects.apply_button_shadow(btn, blur_radius=6, y_offset=1)
                
                layout.addWidget(btn)
                self.buttons.append(btn)
                # ربط الأزرار بالأحداث
                if key == "inventory":
                    btn.clicked.connect(self.open_inventory_tab)
                elif key == "sales":
                    btn.clicked.connect(self.open_sales_tab)
                elif key == "purchases":
                    btn.clicked.connect(self.open_purchases_tab)
            layout.addStretch()
            self.setLayout(layout)
            logging.getLogger(__name__).info("Sidebar UI initialized with buttons: %s", button_keys)
        except Exception as e:
            logging.getLogger(__name__).exception("فشل تحميل الشريط الجانبي")
            show_error_toast(self, self.tr("تعذر تحميل الشريط الجانبي"))

    def reload_texts(self, lang_code=None):
        try:
            # إعادة تحميل نصوص الأزرار عند تغيير اللغة
            button_keys = [
                "home", "accounts", "sales", "purchases", "inventory",
                "pos", "clients", "cash", "assets",
                "payroll", "reports", "settings", "test_loading"
            ]
            button_texts = {
                "home": self.tr("الصفحة الرئيسية"),
                "accounts": self.tr("الحسابات العامة"),
                "sales": self.tr("المبيعات"),
                "purchases": self.tr("المشتريات"),
                "inventory": self.tr("إدارة المخزون"),
                "pos": self.tr("نقطة البيع"),
                "clients": self.tr("العملاء والموردين"),
                "cash": self.tr("النقد والبنوك"),
                "assets": self.tr("الأصول الثابتة"),
                "payroll": self.tr("الرواتب"),
                "reports": self.tr("التقارير"),
                "settings": self.tr("الإعدادات"),
                "test_loading": self.tr("اختبار التحميل")
            }
            for btn, key in zip(self.buttons, button_keys):
                btn.setText(button_texts[key])
            
            # تحديث اتجاه التخطيط
            self.update_layout_direction()
            
            logging.getLogger(__name__).info(f"Sidebar button texts reloaded for language: {lang_code}")
        except Exception as e:
            logging.getLogger(__name__).exception("فشل إعادة تحميل نصوص الشريط الجانبي")
            show_error_toast(self, self.tr("تعذر تحديث نصوص الشريط الجانبي"))
    
    def update_layout_direction(self):
        """تحديث اتجاه التخطيط للشريط الجانبي"""
        try:
            if self.language_controller:
                direction = self.language_controller.get_language_direction(self.language_controller.current_lang)
                self.setLayoutDirection(direction)
                
                # تحديث اتجاه جميع العناصر الفرعية
                for child in self.findChildren(QWidget):
                    child.setLayoutDirection(direction)
                    
                logging.getLogger(__name__).info(f"Sidebar layout direction updated to: {'RTL' if direction == Qt.RightToLeft else 'LTR'}")
        except Exception as e:
            logging.getLogger(__name__).error(f"Failed to update Sidebar layout direction: {e}") 

    def open_inventory_tab(self):
        # إرسال حدث لفتح تبويب المخزون
        self.event_bus.emit("OPEN_INVENTORY_TAB", None)

    def open_sales_tab(self):
        # إرسال حدث لفتح تبويب المبيعات
        self.event_bus.emit("OPEN_SALES_TAB", None)

    def open_purchases_tab(self):
        # إرسال حدث لفتح تبويب المشتريات
        self.event_bus.emit("OPEN_PURCHASES_TAB", None) 